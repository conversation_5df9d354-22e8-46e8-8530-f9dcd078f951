package com.dewu.utils;


import com.dewu.exception.HbaseIOException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.*;
import org.apache.hadoop.hbase.client.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.List;


@Data
@Slf4j
public class HbaseUtils implements Serializable {

	private Configuration configuration;
	private Connection connection;
	private String nameSpaceTableName;

	public static final String DATA_CELL_NAME = "cf";
	final Object lock = new Object();

	public HbaseUtils(String username, String password, String zookeeperQuorum) {
		this.configuration = HBaseConfiguration.create();
		this.configuration.set("hbase.zookeeper.quorum", zookeeperQuorum);
		this.configuration.set("zookeeper.recovery.retry", "3");
		this.configuration.set("zookeeper.session.timeout", "5000");
		if (StringUtils.isNotBlank(username)) {
			this.configuration.set("hbase.client.username", username);
		}
		if (StringUtils.isNotBlank(password)) {
			this.configuration.set("hbase.client.password", password);
		}
		configuration.set("hbase.client.ipc.pool.size", "1");
	}

	public void telnet() throws IOException {
		Admin admin = this.connection.getAdmin();
		ServerName master = admin.getMaster();
		System.out.println(master);
		admin.close();
		close();
	}

	public Connection getConnection() {
		if (connection == null || connection.isClosed()) {
			synchronized (lock) {
				if (connection == null || connection.isClosed()) {
					try {
						this.connection = ConnectionFactory.createConnection(configuration);
						log.info("HbaseUtils实例初始化success");
					} catch (IOException e) {
						log.error("HbaseUtils实例初始化失败！错误信息为：" + e.getMessage(), e);
					}
				}
			}
		}
		return connection;
	}

	public static void main(String[] args) throws ClassNotFoundException, SQLException, IOException {
		HbaseUtils hbaseUtils = new HbaseUtils("userName", "password", "hb-uf63uejwi4uoo2866-master3-001.hbase.rds.aliyuncs.com:21811");
		hbaseUtils.telnet();
	}

	public Result singleGet(Get get){
		return this.singleGet(nameSpaceTableName, get);
	}

	/**
	 * 获取某个rowKey的所有列簇所有列值
	 *
	 * @param tableName hbase表名
	 * @param get       只指定了rowKey的get
	 * @return 返回result
	 */
	public Result singleGet(String tableName, Get get) {
		Result result = null;
		try (Table table = connection.getTable(TableName.valueOf(tableName))) {
			result = table.get(get);
		} catch (IOException e) {
			closeWithException(String.format("singleGet rowKey:%s get failed", new String(get.getRow())), e);
		}
		return result;
	}


	public Result[] batchGet(List<Get> gets){
		return this.batchGet(nameSpaceTableName, gets);
	}

	/**
	 * 批量获取
	 *
	 * @param tableName 表名
	 * @param gets      get列表
	 * @return
	 */
	public Result[] batchGet(String tableName, List<Get> gets){
		Result[] results = null;
		try (Table table = connection.getTable(TableName.valueOf(tableName))) {
			results = table.get(gets);
		} catch (IOException e) {
			closeWithException("batchGets get failed", e);
		}
		return results;
	}


	public void putData(Put put) throws IOException {
		this.putData(nameSpaceTableName, put);
	}


	/**
	 * 向hbase表插入数据
	 *
	 * @param tableName hbase表名
	 * @param put       要插入的put，需指定列簇和列
	 */
	public void putData(String tableName, Put put) throws IOException {
		try (Table table = connection.getTable(TableName.valueOf(tableName))) {
			table.put(put);
		} catch (IOException e) {
			closeWithException(String.format("rowKey:%s put failed", new String(put.getRow())), e);
		}
	}

	public void putBatchData(List<Put> puts) throws IOException {
		this.putBatchData(nameSpaceTableName, puts);
	}

	/**
	 * 向hbase表批量插入数据
	 *
	 * @param tableName hbase表名
	 * @param puts      要插入的puts，需指定列簇和列
	 */
	public void putBatchData(String tableName, List<Put> puts) throws IOException {
		try (Table table = connection.getTable(TableName.valueOf(tableName))) {
			table.put(puts);
		} catch (IOException e) {
			closeWithException("put batch data failed", e);
		}
	}

	public void closeWithException(String message, Exception e) {
		try {
			close();
		} catch (IOException ex) {
			log.error("Close connection failed", ex);
		}
		throw new HbaseIOException(message, e);
	}

	public void close() throws IOException {
		connection.close();
	}
}
