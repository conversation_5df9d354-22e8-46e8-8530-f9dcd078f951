package com.dewu.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class TimeUtils {

    public static Date getLastDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, -1);
        return cal.getTime();
    }

    public static String getDateFormatToStr(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd HH:MM:ss");
        return simpleDateFormat.format(date);
    }

    public static String getDateFormatToStrForTaskName(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy_MM_dd_HH_MM_ss");
        return simpleDateFormat.format(date);
    }

    public static void main(String[] args) {
        System.out.println(TimeUtils.getDateFormatToStrForTaskName(new Date()));
    }

}
