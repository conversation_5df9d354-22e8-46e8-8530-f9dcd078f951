package com.dewu.utils;

import com.dewu.flink.template.meta.datasource.domain.ReDataSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.sql.Connection;
import java.sql.DriverManager;

public class TelnetUtils {

    public static ResponseEntity<Object> telnet(ReDataSource resources) {
        String ip = resources.getIp();
        String userName = resources.getUserName();
        String password = resources.getPassword();
        String dbName = resources.getDbName();
        if (Integer.valueOf(resources.getDataSourceType()) == Constant.DataSourceType.RDS) {
            try {
//                Class.forName(Constant.MYSQL_DRIVER);
//                if (StringUtils.isBlank(dbName)) {
//                    Connection conn = DriverManager.getConnection(ip, userName, password);
//                    conn.close();
//                } else {
//                    Connection conn = DriverManager.getConnection(ip + "/" + dbName, userName, password);
//                    conn.close();
//                }
            } catch (Exception e) {
                return new ResponseEntity<>(HttpStatus.valueOf("地址链接不上"));
            }
        } else if (Integer.valueOf(resources.getDataSourceType()) == Constant.DataSourceType.KAFKA) {
            resources.setDbName(resources.getDataSourceName());
        } else if (Integer.valueOf(resources.getDataSourceType()) == Constant.DataSourceType.HBASE) {
//            try {
//                HbaseUtils hbaseUtils = new HbaseUtils(userName, password, ip);
//                hbaseUtils.telnet();
//            } catch (Exception e) {
//                return new ResponseEntity<>(HttpStatus.valueOf("地址链接不上"));
//            }
        } else if (Integer.valueOf(resources.getDataSourceType()) == Constant.DataSourceType.ODPS) {
//            try {
//                Class.forName(Constant.ODPS_DRIVER);
//                //ip *****************************************************************************
//                Connection conn = DriverManager.getConnection(ip + "?project=" + dbName, userName, password);
//                conn.close();
//            } catch (Exception e) {
//                return new ResponseEntity<>(HttpStatus.valueOf("地址链接不上"));
//            }
        }
        return null;
    }
}
