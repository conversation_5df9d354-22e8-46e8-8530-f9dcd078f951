/*
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dewu.utils.sr;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Service
@RequiredArgsConstructor
public class FlinkJobMetricsVisitor implements Serializable {

	private static final long serialVersionUID = 1L;

	private static final Logger LOG = LoggerFactory.getLogger(FlinkJobMetricsVisitor.class);

	private final StarRocksJdbcConnectionProvider jdbcConnProvider;
	@Value("${pipeline.metrics.sr.database}")
	private String database;
	@Value("${pipeline.metrics.sr.table}")
	private String table;

	public List<Map<String, Object>> getTableColumnsMetaData(String txt) {
		final String query = txt;
		List<Map<String, Object>> rows;
		try {
			if (LOG.isDebugEnabled()) {
				LOG.debug(String.format("Executing query '%s'", query));
			}
			rows = executeQuery(query);
			LOG.info(String.format("reached sr data : %s", rows));
			System.out.println(rows);
		} catch (ClassNotFoundException se) {
			throw new IllegalArgumentException("Failed to find jdbc driver." + se.getMessage(), se);
		} catch (SQLException se) {
			throw new IllegalArgumentException("Failed to get table schema info from StarRocks. " + se.getMessage(), se);
		}
		return rows;
	}

	public List<Map<String, Object>> getJobDelay(String job, String ctime) {
		final String query = "select  `jobName` ,value  from metric_table  where taskProject in ( 'tech-data-dw2','dw-rt-2') and `metricName` = 'flink_taskmanager_job_task_operator_KafkaConsumer_topic_partition_consumer_lag_time' and `jobName` = ? and ctime= ? limit 100 ";
		List<Map<String, Object>> rows;
		try {
			if (LOG.isDebugEnabled()) {
				LOG.debug(String.format("Executing query '%s'", query));
			}
			long ts = System.currentTimeMillis();
			rows = executeQuery(query, job, ctime);
			LOG.info(String.format("takes %s ms to fetch sr data : %s", System.currentTimeMillis() - ts, rows));
		} catch (ClassNotFoundException se) {
			throw new IllegalArgumentException("Failed to find jdbc driver." + se.getMessage(), se);
		} catch (SQLException se) {
			throw new IllegalArgumentException("Failed to get table schema info from StarRocks. " + se.getMessage(), se);
		}
		return rows;
	}

	private List<Map<String, Object>> executeQuery(String query, String... args) throws ClassNotFoundException, SQLException {
		PreparedStatement stmt = jdbcConnProvider.getConnection().prepareStatement(query, ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
		for (int i = 0; i < args.length; i++) {
			stmt.setString(i + 1, args[i]);
		}
		ResultSet rs = stmt.executeQuery();
		rs.next();
		ResultSetMetaData meta = rs.getMetaData();
		int columns = meta.getColumnCount();
		List<Map<String, Object>> list = new ArrayList<>();
		int currRowIndex = rs.getRow();
		rs.beforeFirst();
		while (rs.next()) {
			Map<String, Object> row = new HashMap<>(columns);
			for (int i = 1; i <= columns; ++i) {
				row.put(meta.getColumnName(i), rs.getObject(i));
			}
			list.add(row);
		}
		rs.absolute(currRowIndex);
		rs.close();
		jdbcConnProvider.close();
		return list;
	}
}
