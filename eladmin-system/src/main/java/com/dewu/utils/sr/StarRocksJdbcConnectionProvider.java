/*
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dewu.utils.sr;

import com.dewu.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * Simple JDBC connection provider.
 */
@Service
public class StarRocksJdbcConnectionProvider implements StarRocksJdbcConnectionIProvider, Serializable {

    private static final Logger LOG = LoggerFactory.getLogger(StarRocksJdbcConnectionProvider.class);

    private static final long serialVersionUID = 1L;

    private transient volatile Connection connection;


    @Value("${pipeline.metrics.sr.url}")
    protected String url;
    protected String driverName = "com.mysql.jdbc.Driver";
    protected String cjDriverName = "com.mysql.cj.jdbc.Driver";
    @Value("${pipeline.metrics.sr.username}")
    protected String username;
    @Value("${pipeline.metrics.sr.password}")
    protected String password;

    @Override
    public Connection getConnection() throws SQLException, ClassNotFoundException {
        if (connection == null) {
            synchronized (this) {
                if (connection == null) {
                    try {
                        Class.forName(cjDriverName);
                    } catch (ClassNotFoundException ex) {
                        Class.forName(driverName);
                    }
                    if (StringUtils.isNotEmpty(username)) {
                        connection = DriverManager.getConnection(url, username, password);
                    } else {
                        connection = DriverManager.getConnection(url);
                    }
                }
            }
        }
        return connection;
    }

    @Override
    public Connection reestablishConnection() throws SQLException, ClassNotFoundException {
        close();
        connection = getConnection();
        return connection;
    }

    @Override
    public void close() {
        if (connection == null) {
            return;
        }
        try {
            connection.close();
        } catch (SQLException e) {
            LOG.error("JDBC connection close failed.", e);
        } finally {
            connection = null;
        }
    }
}
