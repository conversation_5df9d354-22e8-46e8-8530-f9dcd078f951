package com.dewu.utils;


import com.dewu.flink.template.meta.tableinfo.service.dto.ReDataSourceMetadataDto;

import java.sql.*;
import java.util.ArrayList;
import java.util.LinkedHashMap;

public class JdbcUtils {



    public static Connection getConnection(String dbName, String ip, String userName, String password) throws ClassNotFoundException, SQLException {
        Class.forName(com.dewu.utils.Constant.MYSQL_DRIVER);
        Connection connection;
        if (StringUtils.isBlank(dbName)) {
            connection = DriverManager.getConnection(ip, userName, password);
        } else {
            connection = DriverManager.getConnection(ip + "/" + dbName, userName, password);
        }
        return connection;
    }


    public static ArrayList<String> showRdsTables(Connection connection) throws SQLException {
        ArrayList<String> tables=new ArrayList<>();
        Statement statement = connection.createStatement();
        ResultSet rs = statement.executeQuery("Show tables");
        while(rs.next()) {
            tables.add(rs.getString(1));
        }
        rs.close();
        statement.close();
        return tables;
    }


    public static LinkedHashMap<String, String> getDbTableFieldAndInitData(ReDataSourceMetadataDto tableInfo, Connection connection) throws SQLException {
        String tableName = tableInfo.getTableName();
        DatabaseMetaData metaData = connection.getMetaData();
        ResultSet tables = metaData.getTables(null, null, tableName, null);
        LinkedHashMap<String, String> fieldNameAndTypeMap = new LinkedHashMap<>();
        while (tables.next()) {
            String name = tables.getString("TABLE_NAME");
            ResultSet columns = metaData.getColumns(null, null, name, null);
            while (columns.next()) {
                System.out.println(
                        columns.getString("COLUMN_NAME") + "\t" +
                                columns.getString("TYPE_NAME") + "(" +
                                columns.getInt("DATA_TYPE") + ")");
                fieldNameAndTypeMap.put(columns.getString("COLUMN_NAME"), columns.getString("TYPE_NAME"));
            }
            columns.close();
        }
        tables.close();
        connection.close();

        return fieldNameAndTypeMap;
    }

}
