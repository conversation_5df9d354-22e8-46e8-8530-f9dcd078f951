package com.dewu.utils;

import com.dewu.flink.template.meta.tablefield.service.dto.ReDataSourceTableFieldDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.sql.SqlNode;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.TableConfig;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.catalog.*;
import org.apache.flink.table.catalog.exceptions.DatabaseNotExistException;
import org.apache.flink.table.catalog.exceptions.FunctionAlreadyExistException;
import org.apache.flink.table.catalog.exceptions.TableAlreadyExistException;
import org.apache.flink.table.delegation.Parser;
import org.apache.flink.table.module.ModuleManager;
import org.apache.flink.table.operations.Operation;
import org.apache.flink.table.planner.calcite.FlinkPlannerImpl;
import org.apache.flink.table.planner.catalog.CatalogManagerCalciteSchema;
import org.apache.flink.table.planner.delegation.ParserImpl;
import org.apache.flink.table.planner.delegation.PlannerContext;
import org.apache.flink.table.planner.parse.CalciteParser;
import org.apache.flink.table.planner.parse.ExtendedParser;

import java.util.*;
import java.util.function.Supplier;

import static org.apache.calcite.jdbc.CalciteSchemaBuilder.asRootSchema;

@Slf4j
public class FlinkSqlValidateUtils {

    private final boolean isStreamingMode = false;
    private final TableConfig tableConfig = new TableConfig();
    private final Catalog catalog = new GenericInMemoryCatalog("MockCatalog", "default");
    private final CatalogManager catalogManager =
            CatalogManagerMocks.preparedCatalogManager().defaultCatalog("builtin", catalog).build();
    private final ModuleManager moduleManager = new ModuleManager();
    private final FunctionCatalog functionCatalog =
            new FunctionCatalog(tableConfig, catalogManager, moduleManager);
    private final PlannerContext plannerContext =
            new PlannerContext(
                    tableConfig,
                    functionCatalog,
                    catalogManager,
                    asRootSchema(new CatalogManagerCalciteSchema(catalogManager, isStreamingMode)),
                    new ArrayList<>());

    private final Supplier<FlinkPlannerImpl> plannerSupplier =
            () ->
                    plannerContext.createFlinkPlanner(
                            catalogManager.getCurrentCatalog(),
                            catalogManager.getCurrentDatabase());

    private final Parser parser =
            new ParserImpl(
                    catalogManager,
                    plannerSupplier,
                    () -> plannerSupplier.get().parser(),
                    plannerContext.getSqlExprToRexConverterFactory());

    private static final ExtendedParser EXTENDED_PARSER = ExtendedParser.INSTANCE;


    public String validateSql(String statement, String tableName1, List<ReDataSourceTableFieldDto> fieldNameList1, String tableName2, List<ReDataSourceTableFieldDto> fieldNameList2) {
        try {
            catalogManager.initSchemaResolver(
                    isStreamingMode,
                    ExpressionResolverMocks.basicResolver(catalogManager, functionCatalog, parser));
            Map<String, String> options = new HashMap<>(10);
            options.put("connector", "COLLECTION");
            if (initTableMetaData(tableName1, fieldNameList1, options)) {
                return "表:" + tableName1 + " 没有维护字段信息,请联系模板平台添加字段信息";
            }
//        TableSchema tableSchema1 =
//                TableSchema.builder()
//                        .field("age", DataTypes.BIGINT())
//                        .field("name", DataTypes.VARCHAR(Integer.MAX_VALUE))
//                        .build();

            if (tableName2 != null) {
                if (initTableMetaData(tableName2, fieldNameList2, options)) {
                    return "表:" + tableName2 + " 没有维护字段信息,请联系模板平台添加字段信息";
                }
            }


            CalciteParser parser = plannerSupplier.get().parser();
            FlinkPlannerImpl planner = plannerSupplier.get();
            Optional<Operation> command = EXTENDED_PARSER.parse(statement);
            if (command.isPresent()) {
//            return Collections.singletonList(command.get());
            }

            // parse the sql query
            // use parseSqlList here because we need to support statement end with ';' in sql client.
            SqlNode parsed = parser.parse(statement);
            SqlNode validated = planner.validate(parsed);
//        SqlNodeList sqlNodeList = parser.parseSqlList(statement);
//        List<SqlNode> parsed = sqlNodeList.getList();
//        SqlNode validated = planner.validate(parsed.get(0));
            return null;
        } catch (Exception e) {
            return "sql语法不正确:" + e.getMessage();
        }
    }

    private boolean initTableMetaData(String tableName, List<ReDataSourceTableFieldDto> fieldNameList, Map<String, String> options) throws TableAlreadyExistException, DatabaseNotExistException, FunctionAlreadyExistException {
        ObjectPath path = new ObjectPath(catalogManager.getCurrentDatabase(), tableName);
        TableSchema.Builder builder = TableSchema.builder();
        if (fieldNameList.size() == 0) {
            return true;
        }
        for (int i = 0; i < fieldNameList.size(); i++) {
            ReDataSourceTableFieldDto tableFieldDto = fieldNameList.get(i);
            log.info("table_name:{},tableField:{}",tableName,tableFieldDto.getFieldName());
            builder = builder.field(tableFieldDto.getFieldName(), DataTypes.VARCHAR(Integer.MAX_VALUE));
        }
        builder = builder.field("process_time", DataTypes.TIMESTAMP());

        TableSchema tableSchema = builder.build();
        CatalogTable catalogTable = new CatalogTableImpl(tableSchema, options, "");
        catalog.createTable(path, catalogTable, true);
        String functionName = "json_value";
        ObjectPath functionPath = new ObjectPath(catalogManager.getCurrentDatabase(), functionName);
        CatalogFunction cf =
                new CatalogFunctionImpl("com.dewu.utils.function.TestParserFunction");
        catalog.createFunction(functionPath, cf, true);


        String functionName1 = "split_array";
        ObjectPath functionPath1 = new ObjectPath(catalogManager.getCurrentDatabase(), functionName1);
        CatalogFunction cf1 =
                new CatalogFunctionImpl("com.dewu.utils.function.DataToArrayFunction");
        catalog.createFunction(functionPath1, cf1, true);

        String functionName2 = "GET_JSON_OBJECT";
        ObjectPath functionPath2 = new ObjectPath(catalogManager.getCurrentDatabase(), functionName2);
        CatalogFunction cf2 =
                new CatalogFunctionImpl("com.dewu.utils.function.TeJsonValueFunction");
        catalog.createFunction(functionPath2, cf2, true);


        String functionName3 = "SUBSTRING_INDEX";
        ObjectPath functionPath3 = new ObjectPath(catalogManager.getCurrentDatabase(), functionName3);
        CatalogFunction cf3 =
                new CatalogFunctionImpl("com.dewu.utils.function.TeSubstringIndexFunction");
        catalog.createFunction(functionPath3, cf3, true);



        return false;
    }


    public static void main(String[] args) throws TableAlreadyExistException, DatabaseNotExistException {
        FlinkSqlValidateUtils parserImplTest = new FlinkSqlValidateUtils();
        List<ReDataSourceTableFieldDto> fieldNameList1 = new ArrayList<>();
        ReDataSourceTableFieldDto reDataSourceTableFieldDto = new ReDataSourceTableFieldDto();
        reDataSourceTableFieldDto.setFieldName("name");
        reDataSourceTableFieldDto.setDataType("varchar");
        fieldNameList1.add(reDataSourceTableFieldDto);
        System.out.println(parserImplTest.validateSql("select * from (select json_value(name,'$value'),count(1) from B group by name)temp ",
                "B", fieldNameList1, null, null));

    }


}
