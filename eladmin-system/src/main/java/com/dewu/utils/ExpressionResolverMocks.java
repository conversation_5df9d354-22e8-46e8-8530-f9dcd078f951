package com.dewu.utils;

import org.apache.flink.table.api.TableConfig;
import org.apache.flink.table.catalog.CatalogManager;
import org.apache.flink.table.catalog.FunctionCatalog;
import org.apache.flink.table.delegation.Parser;
import org.apache.flink.table.expressions.resolver.ExpressionResolver;

import java.util.Optional;

public class ExpressionResolverMocks {

    public static ExpressionResolver.ExpressionResolverBuilder basicResolver(
            CatalogManager catalogManager, FunctionCatalog functionCatalog, Parser parser) {
        return ExpressionResolver.resolverFor(
                new TableConfig(),
                name -> Optional.empty(),
                functionCatalog.asLookup(parser::parseIdentifier),
                catalogManager.getDataTypeFactory(),
                parser::parseSqlExpression);
    }
}
