package com.dewu.utils;

import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.catalog.Catalog;
import org.apache.flink.table.catalog.CatalogManager;
import org.apache.flink.table.catalog.GenericInMemoryCatalog;

public final class CatalogManagerMocks {

    public static final String DEFAULT_CATALOG = EnvironmentSettings.DEFAULT_BUILTIN_CATALOG;

    public static final String DEFAULT_DATABASE = EnvironmentSettings.DEFAULT_BUILTIN_DATABASE;

    public static CatalogManager.Builder preparedCatalogManager() {
        return CatalogManager.newBuilder()
                .classLoader(CatalogManagerMocks.class.getClassLoader())
                .config(new Configuration())
                .defaultCatalog(DEFAULT_CATALOG, createEmptyCatalog())
                .executionConfig(new ExecutionConfig());
    }

    public static Catalog createEmptyCatalog() {
        return new GenericInMemoryCatalog(DEFAULT_CATALOG, DEFAULT_DATABASE);
    }

    private CatalogManagerMocks() {
        // no instantiation
    }
}

