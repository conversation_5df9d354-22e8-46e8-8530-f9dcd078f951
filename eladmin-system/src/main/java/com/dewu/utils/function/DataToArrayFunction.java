package com.dewu.utils.function;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

@FunctionHint(output = @DataTypeHint("ROW<word STRING>"))
public class DataToArrayFunction extends TableFunction<Row> {

    public void eval(String mess, String split) {

        String[] split1 = mess.split(split);
        for (int i = 0; i < split1.length; i++) {
            String s = split1[i];
            collect(Row.of(s));
        }
    }
}
