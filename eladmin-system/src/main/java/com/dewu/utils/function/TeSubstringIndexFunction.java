package com.dewu.utils.function;

import org.apache.flink.table.functions.ScalarFunction;

public class TeSubstringIndexFunction extends ScalarFunction {


    /**
     * 功能描述 截取字符串str第count个分隔符之前的字符串。如果count为正，则从左边开始截取。如果count为负，则从右边开始截取。
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/11/2
     */
    public String eval(String str, String separator, int count) {
        String[] split = str.split(separator);

        int tmp = Math.abs(count);
        if (tmp >= split.length) {
            return str;
        }

        if (count == 0) {
//            System.out.println("");
            return "";
        } else if (count > 0) {
            String tempStr = "";
            for (int i = 0; i < split.length; i++) {
                String s = split[i];
                if (i < count - 1) {
                    tempStr = tempStr + s + separator;
                } else if (i == count - 1) {
                    tempStr = tempStr + s;
                }
            }
//            System.out.println(tempStr);
            return tempStr;
        } else {
            String tempStr = "";
            int length = split.length;//4
            int nowant = length + count;//count -2 ,不要前2条数据
            for (int i = 0; i < split.length; i++) {
                String s = split[i];
                if (i < nowant) {

                } else if (i < length - 1) {
                    tempStr = tempStr + s + separator;
                } else if (i == length - 1) {
                    tempStr = tempStr + s;
                }
            }
            return tempStr;
        }
    }

//    public static void main(String[] args) {
//        TeSubstringIndexFunction teSubstringIdexFunction=new TeSubstringIndexFunction();
//
//        System.out.println(teSubstringIdexFunction
//                .eval( "123:888:aaa:hh:abc:last",":",4));
//    }

}
