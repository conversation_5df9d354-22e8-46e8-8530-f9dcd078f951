package com.dewu.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.*;

public class BinlogParse {

    private static final Map<String, String> JDBC_MAP_FLINK = new HashMap<String, String>();

    static {
        JDBC_MAP_FLINK.put("TINYINT", "TINYINT");
        JDBC_MAP_FLINK.put("SMALLINT", "SMALLINT");
        JDBC_MAP_FLINK.put("INT", "INT");
        JDBC_MAP_FLINK.put("MEDIUMINT", "MEDIUMINT");
        JDBC_MAP_FLINK.put("BIGINT", "BIGINT");
        JDBC_MAP_FLINK.put("FLOAT", "FLOAT");
        JDBC_MAP_FLINK.put("DOUBLE", "DOUBLE");
        JDBC_MAP_FLINK.put("NUMERIC", "DECIMAL");
        J<PERSON><PERSON>_MAP_FLINK.put("DECIMAL", "DECIMAL");
        JD<PERSON>_MAP_FLINK.put("BOOLEAN", "BOOLEAN");
        JDBC_MAP_FLINK.put("DATE", "DATE");
        JDBC_MAP_FLINK.put("TIME", "TIME");
        JDBC_MAP_FLINK.put("DATETIME", "TIMESTAMP");
        JDBC_MAP_FLINK.put("TIMESTAMP", "TIMESTAMP");
        JDBC_MAP_FLINK.put("CHAR", "STRING");
        JDBC_MAP_FLINK.put("VARCHAR", "STRING");
        JDBC_MAP_FLINK.put("TEXT", "STRING");
        JDBC_MAP_FLINK.put("BINARY", "BYTES");
        JDBC_MAP_FLINK.put("VARBINARY", "BYTES");
        JDBC_MAP_FLINK.put("BLOB", "BYTES");
    }


    public static final String BINLOG_DATA = "data";

    public static final String DATA_TYPE = "mysqlType";

    public static final String BINLOG_SOURCE_TABLE = "table";


    public static HashMap<String, String> parseFieldName(String mess) {
        HashMap<String, String> hashMap = new HashMap<String, String>();
        JSONObject jsonObject = JSON.parseObject(mess);
        String tableName = jsonObject.getString(BINLOG_SOURCE_TABLE);
        if (Objects.nonNull(jsonObject)) {
            String mysqlType = jsonObject.getString(DATA_TYPE);
            HashMap<String, String> tmpMap = JSON.parseObject(mysqlType, HashMap.class);
            Set<String> set = tmpMap.keySet();
            Iterator<String> iterator = set.iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                String value = tmpMap.get(key);
                String substring;
                int index = value.indexOf("(");
                if ( index > 0) {
                    substring = value.substring(0, index);
                } else {
                    substring = value;
                }
//                hashMap.put(key,JDBC_MAP_FLINK.get(substring.toUpperCase()));
                hashMap.put(key, substring.toUpperCase());
            }
        }
        return hashMap;
    }


}
