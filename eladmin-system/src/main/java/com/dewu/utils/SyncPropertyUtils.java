package com.dewu.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dewu.flink.template.meta.datasource.domain.ReDataSource;
import com.dewu.flink.template.meta.tablefield.domain.ReDataSourceTableField;
import com.dewu.flink.template.meta.tablefield.service.ReDataSourceTableFieldService;
import com.dewu.flink.template.meta.tablefield.service.dto.ReDataSourceTableFieldDto;
import com.dewu.flink.template.meta.tableinfo.service.dto.ReDataSourceMetadataDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndTimestamp;
import org.apache.kafka.common.PartitionInfo;
import org.apache.kafka.common.TopicPartition;

import java.time.Duration;
import java.util.*;

@Slf4j
public class SyncPropertyUtils {


    /**
     * 功能描述
     * 消费最近一个小时中符合要求的数据
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/9/28
     */
    public static String consumerOutputMess(String jobName, ReDataSource dataSource, ReDataSourceMetadataDto tableInfo) {
        String topic = tableInfo.getTableName();
        String ip = dataSource.getIp();
        Properties props = getProperties(ip, "100", "realtime-property-platform-web");
        KafkaConsumer kafkaConsumer = null;
        try {
            kafkaConsumer = new KafkaConsumer<>(props);
            List<String> list = consumerByTimestamp(kafkaConsumer, topic, System.currentTimeMillis() - 60 * 60 * 1000, jobName);
            return "【最近1小时数据采集到" + list.size() + "条消息】" + list.toString();
        } catch (Exception e) {
            e.printStackTrace();
            if (kafkaConsumer != null) {
                kafkaConsumer.close();
            }
        }
        return "【采集最近1小时数据】没有从目标topic中采集到符合数据，请检查模板sql的条件是否能命中数据";
    }


    private static Properties getProperties(String ip, String maxRecords, String groupId) {
        Properties props = new Properties();
        props.setProperty("bootstrap.servers", ip);
        props.setProperty("enable.auto.commit", "false");
        props.setProperty("auto.commit.interval.ms", "1000");
        props.setProperty("max.poll.records", maxRecords);
        props.setProperty("auto.offset.reset", "earliest");
        props.setProperty("group.id", groupId);
        props.setProperty("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.setProperty("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        return props;
    }


    public static Tuple2<Long, Integer> getTopicInfo(KafkaConsumer kafkaConsumer, String topic) {
        List<PartitionInfo> kafkaPartitions = kafkaConsumer.partitionsFor(topic);
        long sum = 0;
        for (int i = 0; i < kafkaPartitions.size(); i++) {
            TopicPartition topicPartition = new TopicPartition(topic, i);
            List<TopicPartition> list = new ArrayList<>();
            list.add(topicPartition);
            Map<TopicPartition, Long> map = kafkaConsumer.beginningOffsets(list);
            Iterator<Map.Entry<TopicPartition, Long>> iterator = map.entrySet().iterator();
            Map.Entry<TopicPartition, Long> next = iterator.next();
            Long startValue = next.getValue();
            map = kafkaConsumer.endOffsets(list);
            iterator = map.entrySet().iterator();
            next = iterator.next();
            Long endValue = next.getValue();
            long count = endValue - startValue;
//            log.info(kafkaPartitions.get(i) + " count:" + count);
            sum = sum + count;
        }
        System.out.println(sum);
        int size = kafkaPartitions.size();
//        log.info(topic + " sum:" + sum);
        return Tuple2.of(sum, size);
    }


    public static List<String> consumerByTimestamp(KafkaConsumer kafkaConsumer, String topic, long timestamp, String jobName) {
        List<String> list = new ArrayList<>(10);
        boolean flag = false;
        List<PartitionInfo> partitionInfos = kafkaConsumer.partitionsFor(topic);
        log.info("--------------消费topic:{},下面的分区情况:{}", topic, partitionInfos);
        if (null != partitionInfos && partitionInfos.size() > 0) {
            Map<TopicPartition, Long> map = new HashMap<>();
            for (PartitionInfo p : partitionInfos) {
                map.put(new TopicPartition(p.topic(), p.partition()), timestamp);
            }
            log.info("--------------得到时间戳对应的offset");
            Map<TopicPartition, OffsetAndTimestamp> offsetTimestamp = kafkaConsumer.offsetsForTimes(map);
            for (Map.Entry<TopicPartition, OffsetAndTimestamp> entry : offsetTimestamp.entrySet()) {
                if (flag) {
                    break;
                }
                TopicPartition key = entry.getKey();
                OffsetAndTimestamp value = entry.getValue();
                log.info("--------------TopicPartition:{},OffsetAndTimestamp:{}", key, value);
                long position = 0;
                if (value != null) {
                    position = value.offset();
                } else {
                    kafkaConsumer.assign(Collections.singleton(key));
                    kafkaConsumer.seekToEnd(Collections.singleton(key));
                    position = kafkaConsumer.position(key);
                }
                kafkaConsumer.assign(Collections.singleton(key));
                kafkaConsumer.seek(key, position);
                log.info("--------------从指定分区下面消费指定offset数据");
                //时间戳设置完毕
                //=========================
                while (true) {
                    ConsumerRecords<String, String> records = kafkaConsumer.poll(Duration.ofSeconds(10));
                    long timestampTmp = 0;
                    for (ConsumerRecord<String, String> record : records) {
                        JSONObject jsonObject = JSON.parseObject(record.value());
                        String dCheckSubjectCode = (String) jsonObject.get("dCheckSubjectCode");
                        if (("DCHECK_" + dCheckSubjectCode).toLowerCase().equals(jobName.toLowerCase())) {
                            list.add(record.value());
                            log.info("partition:" + record.partition() + " time:" + record.timestamp());
                            flag = true;
                            break;
                        }
                        timestampTmp = record.timestamp();
                    }

//                  if (timestampTmp > timestamp || timestampTmp == 0) {
                    if (timestampTmp == 0) {
                        break;
                    }
                    kafkaConsumer.commitSync();
                }
            }
        }
        return list;
    }


    /**
     * 功能描述
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/9/28
     */
    public static String consumerByLatest(ReDataSource dataSource, ReDataSourceMetadataDto tableInfo) {
        String mess = null;
        String topic = tableInfo.getTableName();
        String ip = dataSource.getIp();
        Properties props = getProperties(ip, "100", "realtime-property-platform-web");
        props.setProperty("auto.offset.reset", "latest");
        KafkaConsumer kafkaConsumer = null;
        try {
            kafkaConsumer = new KafkaConsumer<>(props);
            log.info("--------------消费topic:{}", topic);
            mess = consumerLatestOffset(kafkaConsumer, topic);
        } catch (Exception e) {
            e.printStackTrace();
            if (kafkaConsumer != null) {
                kafkaConsumer.close();
            }
        }
        if (StringUtils.isNoneBlank(mess)) {
            return mess;
        }
        return null;
    }


    public static String consumerLatestOffset(KafkaConsumer kafkaConsumer, String topic) {
        List<PartitionInfo> kafkaPartitions = kafkaConsumer.partitionsFor(topic);
        String mess = "";
        for (int i = 0; i < kafkaPartitions.size(); i++) {
            TopicPartition topicPartition = new TopicPartition(topic, i);
            List<TopicPartition> list = new ArrayList<>();
            list.add(topicPartition);
            Map<TopicPartition, Long> map = kafkaConsumer.beginningOffsets(list);
            Iterator<Map.Entry<TopicPartition, Long>> iterator = map.entrySet().iterator();
            Map.Entry<TopicPartition, Long> next = iterator.next();
            Long startValue = next.getValue();
            map = kafkaConsumer.endOffsets(list);
            iterator = map.entrySet().iterator();
            next = iterator.next();
            Long endValue = next.getValue();
            long count = endValue - startValue;
            if (count >= 1) {
                kafkaConsumer.assign(Arrays.asList(topicPartition));
                kafkaConsumer.seek(topicPartition, endValue - 1);
                ConsumerRecords<String, String> records = kafkaConsumer.poll(100);
                for (ConsumerRecord<String, String> record : records) {
                    mess = record.value();
                    break;
                }
            }
            if (StringUtils.isNoneBlank(mess)) {
                break;
            }
        }
        return mess;
    }

    /**
     * 功能描述
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/9/28
     */
    public static Set<String> syncKafkaTableField(ReDataSourceTableFieldService reDataSourceTableFieldService, ReDataSource
            dataSource, ReDataSourceMetadataDto tableInfo) {
        Set<String> set = new HashSet<>();
        Long tableId = tableInfo.getId();
        String topic = tableInfo.getTableName();

        String ip = dataSource.getIp();
        log.info("--------------消费ip:{}", ip);
        Properties props = getProperties(ip, "100", "realtime-property-platform-web");
        KafkaConsumer kafkaConsumer = null;
        try {
            kafkaConsumer = new KafkaConsumer<>(props);
            log.info("--------------消费topic:{}", topic);
            kafkaConsumer.subscribe(Arrays.asList(topic));
            ConsumerRecords<String, String> records = kafkaConsumer.poll(10000);
            for (ConsumerRecord<String, String> record : records) {
                log.info("消费topic数据成功--------------:{}", topic);
                String mess = record.value();
                HashMap<String, String> fieldNameAndTypeMap = new HashMap<>();
                try {
                    if (dataSource.isBinlogSource()) {
                        try {
                            fieldNameAndTypeMap = BinlogParse.parseFieldName(mess);
                        } catch (Exception e) {
                            log.error("解析kafka BinlogParse数据异常{}", mess, e);
//                            jsonParseFieldName(mess, fieldNameAndTypeMap);
                        }
                    } else {
                        try {
                            fieldNameAndTypeMap = BinlogParse.parseFieldName(mess);
                            log.warn("非binlog数据格式使用binlog解析成功：{}",topic);
                        } catch (Exception e) {
                            jsonParseFieldName(mess, fieldNameAndTypeMap);
                        }
                    }
                } catch (Exception e) {
                    log.error("解析kafka数据异常{}", mess, e);
                    break;
                }

                //查询当前表下已经添加的字段信息
                List<ReDataSourceTableFieldDto> tableFieldDtos = reDataSourceTableFieldService.findByTableId(Long.valueOf(tableId + ""));
                Set<String> dbTableField = new HashSet<>();
                for (int j = 0; j < tableFieldDtos.size(); j++) {
                    ReDataSourceTableFieldDto reDataSourceTableFieldDto = tableFieldDtos.get(j);
                    dbTableField.add(reDataSourceTableFieldDto.getFieldName());
                    set.add(reDataSourceTableFieldDto.getFieldName());
                }

                //找出要新增的字段信息
                for (Map.Entry<String, String> entry : fieldNameAndTypeMap.entrySet()) {
                    String fieldName = entry.getKey();
                    ReDataSourceTableField tableField = new ReDataSourceTableField();
                    if (dbTableField.contains(fieldName)) {
                        continue;
                    }
                    tableField.setFieldName(fieldName);
                    tableField.setDataType(entry.getValue());
                    tableField.setReDataSourceMetadataId(tableId);
                    set.add(fieldName);
//                    log.info("添加字段名:{}", tableField);
                    reDataSourceTableFieldService.create(tableField);
                }
                break;
            }
            kafkaConsumer.commitSync();
            kafkaConsumer.close();
        } catch (Exception e) {
            log.error("", e);
            if (kafkaConsumer != null) {
                kafkaConsumer.close();
            }
        }
        return set;
    }

    public static void jsonParseFieldName(String mess, HashMap<String, String> fieldNameAndTypeMap) {
        HashMap<String, String> tmpMap = JSON.parseObject(mess, HashMap.class);
        Set<String> set = tmpMap.keySet();
        Iterator<String> iterator = set.iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            fieldNameAndTypeMap.put(key, "VARCHAR");
        }
    }

}
