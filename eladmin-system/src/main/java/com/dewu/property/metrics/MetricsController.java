/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.metrics;

import com.dewu.annotation.rest.AnonymousGetMapping;
import com.dewu.annotation.rest.AnonymousPostMapping;
import com.dewu.flink.template.meta.metric.service.AdsMetricsService;
import com.dewu.flink.template.meta.metric.service.TeCommonAttributesService;
import com.dewu.flink.template.meta.metric.service.TeMetricGroupMetadataInfoService;
import com.dewu.flink.template.meta.metric.service.TeMetricMetadataInfoService;
import com.dewu.flink.template.meta.metric.service.dto.*;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author: WangLin
 * Date: 2022/11/01 下午13:35
 * Description: 指标管理
 */
@RestController
@RequestMapping("/api/metrics")
@RequiredArgsConstructor
public class MetricsController {

	Logger logger = LoggerFactory.getLogger(MetricsController.class);

	private final TeCommonAttributesService teCommonAttributesService;

	private final TeMetricGroupMetadataInfoService teMetricGroupMetadataInfoService;

	private final TeMetricMetadataInfoService teMetricMetadataInfoService;
	private final AdsMetricsService adsMetricsService;

	/**
	 * 指标明细分页查询接口
	 */
	@AnonymousGetMapping("/metricDetail/queryPageable")
	public Object metricDetailQueryPageable(TeMetricMetadataInfoQueryCriteria teMetricMetadataInfoQueryCriteria, Pageable pageable) {
		Map<String, Object> returnMap = new HashMap<>();

		try {
			Map<String, Object> stringObjectMap = teMetricMetadataInfoService.queryAll(teMetricMetadataInfoQueryCriteria, pageable);
			returnMap.put("code", "200");
			returnMap.put("data", stringObjectMap);
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("指标明细分页查询接口失败", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}
		return returnMap;
	}

	/**
	 * 创建指标明细
	 */
	@AnonymousPostMapping("/createMetricDetail")
	public Object createMetricDetail(@RequestBody TeMetricMetadataInfoDto resources) {
		Map<String, Object> returnMap = new HashMap<>();
		try {
			teMetricMetadataInfoService.create(resources);
			returnMap.put("code", "200");
			returnMap.put("data", "");
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("新增指标详情接口出错", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}
		return returnMap;
	}

	/**
	 * 修改指标详情
	 */
	@AnonymousPostMapping("/updateMetricDetail")
	public Object updateMetricDetail(@RequestBody TeMetricMetadataInfoDto resources) {
		Map<String, Object> returnMap = new HashMap<>();
		try {
			teMetricMetadataInfoService.update(resources);
			returnMap.put("code", "200");
			returnMap.put("data", "");
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("修改指标详情接口出错", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}
		return returnMap;
	}

	/**
	 * 上线指标明细
	 */
	@AnonymousGetMapping("/onlineMetricDetail/{metricDetailId}")
	public Object onlineMetricDetail(@PathVariable Long metricDetailId) {
		Map<String, Object> returnMap = new HashMap<>();
		try {
			TeMetricMetadataInfoDto resources = teMetricMetadataInfoService.findById(metricDetailId);
			adsMetricsService.createTeMetricMetadataInfo(resources);
			returnMap.put("code", "200");
			returnMap.put("data", "");
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("上线指标明细接口出错", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}
		return returnMap;
	}

	/**
	 * 指标组分页查询接口
	 */
	@AnonymousGetMapping("/metricGroup/queryPageable")
	public Object metricGroupQueryPageable(TeMetricGroupMetadataInfoQueryCriteria teMetricGroupMetadataInfoQueryCriteria, Pageable pageable) {
		Map<String, Object> returnMap = new HashMap<>();

		try {
			Map<String, Object> stringObjectMap = teMetricGroupMetadataInfoService.queryAll(teMetricGroupMetadataInfoQueryCriteria, pageable);
			returnMap.put("code", "200");
			returnMap.put("data", stringObjectMap);
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("指标组分页查询接口失败", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}
		return returnMap;
	}

	/**
	 * 创建指标组
	 */
	@AnonymousPostMapping("/createMetricGroup")
	public Object createMetricGroup(@RequestBody TeMetricGroupMetadataInfoDto resources) {
		Map<String, Object> returnMap = new HashMap<>();
		try {
			teMetricGroupMetadataInfoService.create(resources);
			returnMap.put("code", "200");
			returnMap.put("data", "");
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("新增指标组接口出错", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}
		return returnMap;
	}

	/**
	 * 修改指标组
	 */
	@AnonymousPostMapping("/updateMetricGroup")
	public Object updateMetricGroup(@RequestBody TeMetricGroupMetadataInfoDto resources) {
		Map<String, Object> returnMap = new HashMap<>();
		try {
			teMetricGroupMetadataInfoService.update(resources);
			returnMap.put("code", "200");
			returnMap.put("data", "");
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("修改指标组接口出错", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}
		return returnMap;
	}

	@AnonymousGetMapping("/commonAttributes")
	/**
	 * 获取公共属性
	 */
	public Object commonAttribute() {
		Map<String, Object> returnMap = new HashMap<>();

		try {
			List<Map<String, Object>> list = teCommonAttributesService.queryAll(new TeCommonAttributesQueryCriteria()).stream().map(item -> {
				Map<String, Object> map = new HashMap<>();
				map.put("name", item.getName());
				return map;
			}).collect(Collectors.toList());

			returnMap.put("code", "200");
			returnMap.put("data", list);
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("公共属性查询接口出错", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}

		return returnMap;
	}
}
