package com.dewu.property.bi.service.dto;

import com.dewu.annotation.Query;
import lombok.Data;

import java.util.Collection;
import java.util.List;

@Data
public class KeTopicQueryCriteria {

    @Query(type = Query.Type.INNER_LIKE)
    private String jobName;

    @Query(type = Query.Type.IN)
    private Collection<String> cluster;


    @Query(type = Query.Type.NOT_EQUAL)
    private int diskSize;

    @Query(type = Query.Type.EQUAL)
    private int status;

    @Query(type = Query.Type.LESS_THAN)
    private String created;


}

