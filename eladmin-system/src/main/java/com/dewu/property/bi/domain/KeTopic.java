/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.bi.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
import javax.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
 * @website https://el-admin.vip
 * @description /
 * <AUTHOR>
 * @date 2023-05-15
 **/
@Entity
@Data
@Table(name="ke_topic")
public class KeTopic implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    @ApiModelProperty(value = "自增id")
    private Long id;

    @Column(name = "`topic`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "topic")
    private String topic;

    @Column(name = "`description`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "description")
    private String description;

    @Column(name = "`cluster`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "cluster")
    private String cluster;

    @Column(name = "`status`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "status")
    private Integer status;

    @Column(name = "`partition`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "partition")
    private Integer partition;

    @Column(name = "`repli`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "repli")
    private Integer repli;

    @Column(name = "`username`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "username")
    private String username;

    @Column(name = "`created`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "created")
    private String created;

    @Column(name = "`modify`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "modify")
    private String modify;

    @Column(name = "`namespace`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "namespace")
    private String namespace;

    @Column(name = "`create_time`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @Column(name = "`creator`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "创建者")
    private String creator;

    @Column(name = "`modify_time`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "修改时间")
    private Timestamp modifyTime;

    @Column(name = "`modifier`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "修改者")
    private String modifier;

    @Column(name = "`topic_disp`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "显示的topic名称")
    private String topicDisp;

    @Column(name = "`disk_size`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "占用磁盘空间")
    private Long diskSize;

    @Column(name = "`retention_ms`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "磁盘保存时间ms")
    private Long retentionMs;

    public void copy(KeTopic source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
