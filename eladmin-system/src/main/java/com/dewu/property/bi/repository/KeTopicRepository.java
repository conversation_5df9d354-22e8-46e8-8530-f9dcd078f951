/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.bi.repository;

import com.dewu.property.bi.domain.KeTopic;
import com.dewu.property.bi.service.dto.KeTopicDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-06-09
 **/
public interface KeTopicRepository extends JpaRepository<KeTop<PERSON>, Long>, JpaSpecificationExecutor<KeTopic> {

    @Query(value = "SELECT ds.id,ds.username,ds.create_time,ds.cluster FROM ke_topic ds " +
            "WHERE ds.topic = ?1 and ds.cluster =?2 and ds.`status`=2 ", nativeQuery = true)
    KeTopic findTopic(String topic, String cluster);

    @Query(value = "SELECT ds.id,ds.username,ds.create_time,ds.cluster FROM ke_topic ds " +
            "WHERE ds.topic = ?1 and (ds.cluster =?2 or ds.cluster =?3) and ds.`status`=2 limit 1", nativeQuery = true)
    KeTopic findTopic(String topic, String cluster1, String cluster2);

    @Query(value = "SELECT * FROM ke_topic where " +
            " LOWER(cluster)=?1 and LOWER(topic)=?2  and `status`=2", nativeQuery = true)
    List<KeTopic> findByClusterAndTopic(String cluster, String topic);
}