/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.bi;

import com.dewu.annotation.Log;
import com.dewu.property.bi.domain.BiBean;
import com.dewu.property.bi.mapstruct.BiMapper;
import com.dewu.utils.FileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-06-09
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "单流规则管理")
@RequestMapping("/api/bibinlogcollect")
public class BiBinlogCollectController {

    private final BiMapper biMapper;

    @GetMapping
    @ApiOperation("查询bi")
    @PreAuthorize("@el.check('bi:list')")
    public ResponseEntity<Object> queryJob()  {
        Map<String, BiBean> map = biMapper.biCollect(BiMapper.ODS_BASE_SQL,"'binlog-kafka-bigdata','rt-kafka-bigdata-ods'");
        List<BiBean> list = new ArrayList<>();
        for (String key : map.keySet()) {
            BiBean biBean = map.get(key);
            list.add(biBean);
        }
        Map<String, Object> map1 = new LinkedHashMap<>(2);
        map1.put("content", list);
        map1.put("totalElements", list.size());
        return new ResponseEntity<>(map1, HttpStatus.OK);
    }


    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('bi:list')")
    public void exportReDataSourceMetadata(HttpServletResponse response) throws IOException {
        Map<String, BiBean> map1 = biMapper.biCollect(BiMapper.ODS_BASE_SQL,"'binlog-kafka-bigdata','rt-kafka-bigdata-ods'");
        List<BiBean> list = new ArrayList<>();
        for (String key : map1.keySet()) {
            BiBean biBean = map1.get(key);
            list.add(biBean);
        }

        List<Map<String, Object>> listResult = new ArrayList<>();
        for (BiBean biBean : list) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("一级主题域", biBean.getDomain1());
            map.put("二级主题域", biBean.getDomain2());
            map.put("增量资产数", biBean.getAdd_cnt());
            map.put("增量无效资产数", biBean.getAdd_invalid_cnt());
            map.put("增量资产平均下游数", biBean.getAdd_avg_consumer_cnt());
            map.put("存量资产数", biBean.getHistory_cnt());
            map.put("存量无效资产数", biBean.getHistory_invalid_cnt());
            map.put("存量资产平均下游数", biBean.getHistory_avg_consumer_cnt());
            listResult.add(map);
        }
        FileUtil.downloadExcel(listResult, response);
    }

}
