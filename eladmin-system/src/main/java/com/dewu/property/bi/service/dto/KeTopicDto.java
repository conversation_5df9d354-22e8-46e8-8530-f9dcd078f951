/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.bi.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-06-09
**/
@Data
public class KeTopicDto implements Serializable {


    /** 自增id */
    private Long id;

    /** topic */
    private String topic;

    /** description */
    private String description;

    /** cluster */
    private String cluster;

    /** status */
    private Integer status;

    /** partition */
    private Integer partition;

    /** repli */
    private Integer repli;

    /** username */
    private String username;

    /** created */
    private String created;

    /** modify */
    private String modify;

    /** namespace */
    private String namespace;

    /** 创建时间 */
    private Timestamp createTime;

    /** 创建者 */
    private String creator;

    /** 修改时间 */
    private Timestamp modifyTime;

    /** 修改者 */
    private String modifier;

    /** 显示的topic名称 */
    private String topicDisp;

    /** 占用磁盘空间 */
    private Long diskSize;

    /** 磁盘保存时间ms */
    private Long retentionMs;
}