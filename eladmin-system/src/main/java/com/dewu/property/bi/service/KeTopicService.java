/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.bi.service;

import com.dewu.property.bi.domain.KeTopic;
import com.dewu.property.bi.service.dto.KeTopicDto;
import com.dewu.property.bi.service.dto.KeTopicQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务接口
 * @date 2022-06-09
 **/
public interface KeTopicService {


    /**
     * 根据ID查询
     *
     * @param id ID
     * @return ReDataSourceTableFieldDto
     */
    KeTopic findById(Long id);


    KeTopic findTopic(String topic, String cluster);

    KeTopic findTopic(String topic, String cluster1, String cluster2);

    List<KeTopic> findByClusterAndTopic(String cluster, String topic);


    /**
     * 查询数据分页
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String,Object>
     */
    Map<String,Object> queryAll(KeTopicQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     * @param criteria 条件参数
     * @return List<KeTopicDto>
     */
    List<KeTopicDto> queryAll(KeTopicQueryCriteria criteria);

    /**
     * 创建
     * @param resources /
     * @return KeTopicDto
     */
    KeTopicDto create(KeTopic resources);

    /**
     * 编辑
     * @param resources /
     */
    void update(KeTopic resources);

    /**
     * 多选删除
     * @param ids /
     */
    void deleteAll(Long[] ids);

    /**
     * 导出数据
     * @param all 待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<KeTopicDto> all, HttpServletResponse response) throws IOException;
}