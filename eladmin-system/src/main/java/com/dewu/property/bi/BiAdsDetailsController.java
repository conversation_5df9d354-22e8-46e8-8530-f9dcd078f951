/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.bi;

import com.dewu.annotation.Log;
import com.dewu.property.bi.domain.BiBean;
import com.dewu.property.bi.domain.BiQueryCriteria;
import com.dewu.property.bi.domain.KeTopic;
import com.dewu.property.bi.mapstruct.BiMapper;
import com.dewu.property.bi.service.KeTopicService;
import com.dewu.utils.FileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-06-09
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "单流规则管理")
@RequestMapping("/api/biadsdetails")
public class BiAdsDetailsController {


    private final BiMapper biMapper;

    private final  KeTopicService keTopicService;

    @GetMapping
    @ApiOperation("查询bi")
    @PreAuthorize("@el.check('bi:list')")
    public ResponseEntity<Object> queryJob(BiQueryCriteria criteria) throws SQLException, ClassNotFoundException {
        List<BiBean> list = biMapper.biDetails(BiMapper.ADS_BASE_SQL,"'rt-kafka-bigdata-ads'",criteria.getIsIncrement());
        for (BiBean biBean : list) {
            KeTopic topic = keTopicService.findTopic(biBean.getTopic(), "rt-kafka-bigdata-ads");
            if(topic!=null) {
                biBean.setUsername(topic.getUsername());
                biBean.setCreate_time(topic.getCreateTime());
            }
        }

        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", list);
        map.put("totalElements", list.size());
        return new ResponseEntity<>(map, HttpStatus.OK);
    }


    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('bi:list')")
    public void exportReDataSourceMetadata(HttpServletResponse response, BiQueryCriteria criteria) throws IOException, SQLException, ClassNotFoundException {
        List<BiBean> list = biMapper.biDetails(BiMapper.ADS_BASE_SQL,"'rt-kafka-bigdata-ads'",criteria.getIsIncrement());

        List<Map<String, Object>> listResult = new ArrayList<>();
        for (BiBean biBean : list) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("资产名称", biBean.getTopic());
            map.put("一级主题域", biBean.getDomain1());
            map.put("二级主题域", biBean.getDomain2());
            map.put("下游数量", biBean.getTopic_consumer());
            listResult.add(map);
        }
        FileUtil.downloadExcel(listResult, response);
    }

}
