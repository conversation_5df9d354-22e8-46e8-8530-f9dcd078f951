package com.dewu.property.bi.domain;

import java.sql.Timestamp;

public class BiBean {

    private String cluster;

    private String topic;
    private String temp;
    private String domain1;
    private String domain2;

    //topic下游数量
    private long topic_consumer;

    //增量资产数
    private long add_cnt;

    //增量无效资产数
    private long add_invalid_cnt;

    //增量平均下游数量
    private long add_consumer_cnt;

    private String add_avg_consumer_cnt;


    private long history_cnt;

    private long history_invalid_cnt;

    private long history_consumer_cnt;
    private String history_avg_consumer_cnt;

    private Timestamp create_time;

    private String username;

    public Timestamp getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Timestamp create_time) {
        this.create_time = create_time;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }


    public String getCluster() {
        return cluster;
    }

    public void setCluster(String cluster) {
        this.cluster = cluster;
    }

    public String getDomain1() {
        return domain1;
    }

    public void setDomain1(String domain1) {
        this.domain1 = domain1;
    }

    public String getDomain2() {
        return domain2;
    }

    public void setDomain2(String domain2) {
        this.domain2 = domain2;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public long getHistory_consumer_cnt() {
        return history_consumer_cnt;
    }

    public void setHistory_consumer_cnt(long history_consumer_cnt) {
        this.history_consumer_cnt = history_consumer_cnt;
    }

    public long getAdd_consumer_cnt() {
        return add_consumer_cnt;
    }

    public void setAdd_consumer_cnt(long add_consumer_cnt) {
        this.add_consumer_cnt = add_consumer_cnt;
    }

    public long getTopic_consumer() {
        return topic_consumer;
    }

    public void setTopic_consumer(long topic_consumer) {
        this.topic_consumer = topic_consumer;
    }

    public long getAdd_cnt() {
        return add_cnt;
    }

    public void setAdd_cnt(long add_cnt) {
        this.add_cnt = add_cnt;
    }

    public long getAdd_invalid_cnt() {
        return add_invalid_cnt;
    }

    public void setAdd_invalid_cnt(long add_invalid_cnt) {
        this.add_invalid_cnt = add_invalid_cnt;
    }

    public String getAdd_avg_consumer_cnt() {
        return add_avg_consumer_cnt;
    }

    public void setAdd_avg_consumer_cnt(String add_avg_consumer_cnt) {
        this.add_avg_consumer_cnt = add_avg_consumer_cnt;
    }

    public long getHistory_cnt() {
        return history_cnt;
    }

    public void setHistory_cnt(long history_cnt) {
        this.history_cnt = history_cnt;
    }

    public long getHistory_invalid_cnt() {
        return history_invalid_cnt;
    }

    public void setHistory_invalid_cnt(long history_invalid_cnt) {
        this.history_invalid_cnt = history_invalid_cnt;
    }

    public String getHistory_avg_consumer_cnt() {
        return history_avg_consumer_cnt;
    }

    public void setHistory_avg_consumer_cnt(String history_avg_consumer_cnt) {
        this.history_avg_consumer_cnt = history_avg_consumer_cnt;
    }

    public String getTemp() {
        return temp;
    }

    public void setTemp(String temp) {
        this.temp = temp;
    }
}
