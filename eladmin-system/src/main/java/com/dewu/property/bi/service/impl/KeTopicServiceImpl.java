/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.bi.service.impl;

import com.dewu.property.bi.domain.KeTopic;
import com.dewu.property.bi.repository.KeTopicRepository;
import com.dewu.property.bi.service.KeTopicService;
import com.dewu.property.bi.service.dto.KeTopicDto;
import com.dewu.property.bi.service.dto.KeTopicQueryCriteria;
import com.dewu.property.bi.service.mapstruct.KeTopicMapper;
import com.dewu.utils.FileUtil;
import com.dewu.utils.PageUtil;
import com.dewu.utils.QueryHelp;
import com.dewu.utils.ValidationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-06-09
 **/
@Service
@RequiredArgsConstructor
public class KeTopicServiceImpl implements KeTopicService {


    private final KeTopicMapper keTopicMapper;

    private final KeTopicRepository keTopicRepository;

    @Override
    @Transactional
    public KeTopic findById(Long id) {
        KeTopic keTopic = keTopicRepository.findById(id).orElseGet(KeTopic::new);
        ValidationUtil.isNull(keTopic.getId(), "keTopic", "id", id);
        return keTopic;
    }


    @Override
    @Transactional
    public KeTopic findTopic(String topic, String cluster) {
        KeTopic keTopic = keTopicRepository.findTopic(topic,cluster);
        return keTopic;
    }

    @Override
    @Transactional
    public KeTopic findTopic(String topic, String cluster1,String cluster2) {
        KeTopic keTopic = keTopicRepository.findTopic(topic,cluster1,cluster2);
        return keTopic;
    }

    @Override
    public List<KeTopic> findByClusterAndTopic(String cluster, String topic) {
        return keTopicRepository.findByClusterAndTopic(cluster,topic);
    }

    @Override
    public Map<String,Object> queryAll(KeTopicQueryCriteria criteria, Pageable pageable){
        Page<KeTopic> page = keTopicRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(keTopicMapper::toDto));
    }

    @Override
    public List<KeTopicDto> queryAll(KeTopicQueryCriteria criteria){
        return keTopicMapper.toDto(keTopicRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public KeTopicDto create(KeTopic resources) {
        return keTopicMapper.toDto(keTopicRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(KeTopic resources) {
        KeTopic keTopic = keTopicRepository.findById(resources.getId()).orElseGet(KeTopic::new);
        ValidationUtil.isNull( keTopic.getId(),"KeTopic","id",resources.getId());
        keTopic.copy(resources);
        keTopicRepository.save(keTopic);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            keTopicRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<KeTopicDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (KeTopicDto keTopic : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("topic", keTopic.getTopic());
            map.put("description", keTopic.getDescription());
            map.put("cluster", keTopic.getCluster());
            map.put("status", keTopic.getStatus());
            map.put("partition", keTopic.getPartition());
            map.put("repli", keTopic.getRepli());
            map.put("username", keTopic.getUsername());
            map.put("created", keTopic.getCreated());
            map.put("modify", keTopic.getModify());
            map.put("namespace", keTopic.getNamespace());
            map.put("创建时间", keTopic.getCreateTime());
            map.put("创建者", keTopic.getCreator());
            map.put("修改时间", keTopic.getModifyTime());
            map.put("修改者", keTopic.getModifier());
            map.put("显示的topic名称", keTopic.getTopicDisp());
            map.put("占用磁盘空间", keTopic.getDiskSize());
            map.put("磁盘保存时间ms", keTopic.getRetentionMs());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}