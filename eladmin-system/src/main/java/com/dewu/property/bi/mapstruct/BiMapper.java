/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.bi.mapstruct;

import com.dewu.property.bi.domain.BiBean;
import com.dewu.utils.Constant;
import com.dewu.utils.TimeUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-06-09
 **/
@Slf4j
@Component
public class BiMapper {

    @Value("${db.url}")
    private String url;

    @Value("${db.user}")
    private String user;

    @Value("${db.password}")
    private String password;

    private DecimalFormat df = new DecimalFormat("0.0");//设置保留位数

    private  String whereCondition = "";

    public static String ODS_BASE_SQL = "SELECT topic as topic_a,\n" +
            "CASE WHEN instr(topic, 'ods_du_coupon')=1 or instr(topic, 'ods_dw_coupon')=1 or instr(topic, 'ods_du_marketing')=1 or instr(topic, 'ods_promo_user')=1 or\n" +
            "instr(topic, 'ods_promo_config')=1 or instr(topic, 'ods_promo_admin')=1 or instr(topic, 'ods_dw_delivery_marketing')=1 or instr(topic, 'ods_du_activity_raffle_merchant')=1 or\n" +
            "instr(topic, 'ods_promo_discount_config')=1  THEN '交易-营销'\n" +
            "\n" +
            "WHEN instr(topic, 'ods_du_trade_inventory')=1 or instr(topic, 'ods_dw_trade_price_db')=1 or instr(topic, 'ods_dw_trade_inventory')=1 \n" +
            "or instr(topic, 'ods_du_trade_bidding_db')=1 THEN '交易-出价'\n" +
            "\n" +
            "WHEN instr(topic, 'dw_finance_loan')=1 or instr(topic, 'ods_du_trade_order')=1 or instr(topic, 'ods_dw_trade_refund')=1 or instr(topic, 'ods_du_trade_deposit')=1 or\n" +
            "instr(topic, 'ods_du_trade_oversea')=1 or instr(topic, 'ods_dw_trade_auction')=1 or instr(topic, 'ods_dw_bounty_fq')=1 or instr(topic, 'ods_du_pay')=1 or instr(topic, 'ods_du_cashier')=1  \n" +
            "or instr(topic, 'ods_du_identify')=1  THEN '交易-订单'\n" +
            "\n" +
            "WHEN instr(topic, 'ods_du_trade_commodity_db')=1  or instr(topic, 'ods_dw_commodity_favorite')=1\n" +
            "or instr(topic, 'ods_du_commodity_brand')=1   THEN '交易-商品'\n" +
            "\n" +
            "WHEN instr(topic, 'ods_dw_user_tb')=1 or instr(topic, 'ods_du_member')=1 or instr(topic, 'ods_du_user_tb')=1\n" +
            "or instr(topic, 'ods_bookkeeping')=1  THEN '交易-用户'\n" +
            "    \n" +
            "    \n" +
            "WHEN instr(topic, 'ods_hupu_du_community')=1  or instr(topic, 'ods_dw_sns_creator')=1  or instr(topic, 'ods_dw_sns_cnt')=1\n" +
            "or instr(topic, 'ods_duapp_fans')=1  or instr(topic, 'ods_du_sns_merc')=1  or instr(topic, 'ods_dw_sns_itr')=1 or instr(topic, 'ods_dw_sns_use')=1  \n" +
            "or instr(topic, 'ods_dw_sns_rec')=1  or instr(topic, 'ods_dw_sns_event')=1 THEN '社区-社区'\n" +
            "WHEN instr(topic, 'ods_sns_live')=1  or instr(topic, 'ods_sns_currency')=1   THEN '社区-直播'\n" +
            "\n" +
            "\n" +
            "WHEN instr(topic, 'ods_hupu_du_kefu')=1  or instr(topic, 'ods_du_kefu')=1 or instr(topic, 'ods_kefu_bot')=1   THEN '客服-工单'\n" +
            "WHEN instr(topic, 'ods_du_tms')=1    THEN '客服-tms'\n" +
            "WHEN instr(topic, 'ods_du_im')=1  THEN '客服-im'\n" +
            "\n" +
            "\n" +
            "WHEN instr(topic, 'pink')=1 or instr(topic, 'ods_hupu_du_pink')=1   THEN '供应链-供应链'\n" +
            "\n" +
            "ELSE '其他-其他' END AS temp\n" +
            "FROM `ke_topic`\n";


    public static String DW_BASE_SQL = "SELECT  topic as topic_a,\n" +
            "    \n" +
            "CASE WHEN instr(topic, 'dwd_inventory')=1   THEN '交易-出价'\n" +
            "WHEN instr(topic, 'dwd_trade_sub_order')=1 or instr(topic, 'dwd_ord')=1 or instr(topic, 'dwd_sub_order')=1 \n" +
            "    or instr(topic, 'dwd_trade')=1 or instr(topic, 'dwd_refund_order')=1 THEN '交易-订单'   \n" +

            "WHEN instr(topic, 'dwd_pink')=1   THEN '供应链-供应链'    \n" +

            "WHEN instr(topic, 'dws_community_recommend')=1 or instr(topic, 'dwd_community_recommend')=1 or instr(topic, 'dw_recommend_label_community')=1 \n" +
            "    or instr(topic, 'dwd_recommend_label_community')=1 or instr(topic, 'dws_recommend_label_community')=1   THEN '社区-推荐'\n" +
            "    \n" +
            "WHEN instr(topic, 'dwd_live')=1  THEN '社区-直播'\n" +
            "    \n" +
            "WHEN instr(topic, 'dwd_community_sensors')>0 or instr(topic, 'dwd_community_interact')>0\n" +
            "    or instr(topic, 'dwd_community_search')>0 or instr(topic, 'dwd_community_contentmark')>0 THEN '社区-社区'\n" +
            "      \n" +
            "    \n" +
            "\n" +
            "WHEN instr(topic, 'dwd_dewu_algo_trade')>0 or instr(topic, 'dws_dewu_algo_trade')>0    THEN '流量-算法'  \n" +
            "    \n" +
            "WHEN instr(topic, 'dws_dewu_algo_community')>0 or instr(topic, 'dwd_dewu_algo_community')>0 \n" +
            "    or instr(topic, 'dw_dewu_algo_community')>0  THEN '流量-算法' \n" +
            "    \n" +
            "    \n" +
            "WHEN instr(topic, 'dws_algo_offline_portrait')>0 or instr(topic, 'dwd_algo_portrait')>0 \n" +
            "    or instr(topic, 'dwd_algo_user_portrait')>0 or instr(topic, 'dwd_user_portrait')>0   THEN '流量-算法'     \n" +
            "    \n" +
            "    \n" +
            "WHEN instr(topic, 'dwd_dewu_traffic')=1   THEN '流量-公共'     \n" +
            "    \n" +
            "WHEN instr(topic, 'dws_sensors_log')=1 or instr(topic, 'dwd_dewu_sensors_log')=1 or instr(topic, 'dw_dewu_algo_sensors')=1  THEN '流量-公共'    \n" +
            "    \n" +
            "WHEN instr(topic, 'dwd_dewu_algo_attribution')>0 or instr(topic, 'dwd_traffic_attribution')>0    THEN '流量-公共' \n" +
            "    \n" +
            "    \n" +
            "ELSE '其他-其他' END AS temp\n" +
            "FROM `ke_topic` \n";

    public static String ADS_BASE_SQL = "SELECT  topic as topic_a,\n" +
            "    \n" +
            "CASE WHEN instr(topic, 'inventory')>0   THEN '交易-出价'\n" +

            "WHEN instr(topic, 'user_birthday')>0 or  instr(topic, 'crash')>0  THEN '交易-用户'\n" +
            "\n" +
            "WHEN instr(topic, 'order')>0 or instr(topic, 'discount')>0 or instr(topic, 'trade')>0  THEN '交易-订单'   \n" +
            "    \n" +
            "WHEN instr(topic, 'user_features')>0 or  instr(topic, 'user_login_features')>0 or  instr(topic, 'portrait')>0   THEN '流量-画像'    \n" +
            "   \n" +
            "WHEN instr(topic, 'community')>0  THEN '社区-社区'\n" +
            "\n" +
            "ELSE '其他-其他' END AS temp\n" +
            "FROM `ke_topic` ";

    public static void main(String[] args)  {
        BiMapper biMapper = new BiMapper();
        biMapper.biCollect(BiMapper.ODS_BASE_SQL, "'binlog-kafka-bigdata','rt-kafka-bigdata-ods'");
    }


    /**
     * 功能描述
     *
     * @param month 代表复盘月份，9月复盘，增量8-9月，存量<8月
     * @return
     * <AUTHOR>
     * @date 2022/9/7
     */
    @SneakyThrows
    public Map<String, BiBean> biCollect(String exeSql, String cluster) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Date date = new Date();
        String nowMonth = sdf.format(date);
        System.out.println("本月：" + nowMonth);
        Date lastDate = TimeUtils.getLastDate(date);
        String beforeMonth = sdf.format(lastDate);
        System.out.println("上月" + sdf.format(lastDate));

        String B;
        beforeMonth = "'" + beforeMonth + "-01 00:00:00'";
        nowMonth = "'" + nowMonth + "-01 00:00:00'";
        exeSql = exeSql + " where cluster in(" + cluster + ") and `status`=2 and topic not like '%_libra' and  topic not like 'test_%'";
        whereCondition = "  and created<" + beforeMonth + "";// 存量资产

        Map<String, BiBean> mapAll = new HashMap<>();

        Class.forName("com.mysql.cj.jdbc.Driver");
        byte[] decodedBytes = Base64.getDecoder().decode(password);
        password = new String(decodedBytes);
        Connection connection = DriverManager.getConnection(url, user, password);

        //每个域下资产数量
        String JDBC_SQL = "select a.temp,count(1) from\n" +
                "("
                + exeSql + whereCondition
                + ")a\n" +
                "group by temp";
        Statement statement = connection.createStatement();
        ResultSet rs = statement.executeQuery(JDBC_SQL);
        List<BiBean> historyCntList = new ArrayList<>();
        while (rs.next()) {
            String temp = rs.getString(1);
            int count = rs.getInt(2);
            BiBean biBean = new BiBean();
            biBean.setHistory_cnt(count);
            biBean.setTemp(temp);
            historyCntList.add(biBean);
        }
        rs.close();
        statement.close();


        //每个域的资产数量------存量
        Map<String, Long> mapHistory = new HashMap<>();
        for (int i = 0; i < historyCntList.size(); i++) {
            BiBean biBean = historyCntList.get(i);
            String s = biBean.getTemp();
//            String s = name.split("-")[0];
            Long aLong = mapHistory.get(s);
            if (aLong == null) {
                mapHistory.put(s, biBean.getHistory_cnt());
            } else {
                mapHistory.put(s, biBean.getHistory_cnt() + aLong);
            }
        }

        for (String key : mapHistory.keySet()) {
            BiBean biBean = mapAll.get(key);
            if (biBean == null) {
                BiBean bean = new BiBean();
                bean.setHistory_cnt(mapHistory.get(key));
                mapAll.put(key, bean);
            } else {
                biBean.setHistory_cnt(mapHistory.get(key));
                mapAll.put(key, biBean);
            }
        }

        //每个域下无效资产数量------存量
        String A = "where b.topic is null ";
        JDBC_SQL = "select temp,count(1) from  \n" +
                "(\n" +
                "select  a.topic_a,a.temp  from  ("
                + exeSql + whereCondition
                + ")a\n" +
                "left join (select * from ke_consumer_group where cluster in(" + cluster + ") " +
                " and topic not like '%_libra' and  topic not like 'test_%' and `status`=0 " +
                " and `groups`<>'realtime-property-platform') b on a.topic_a=b.topic \n" +
                A + ") cd group by temp";

        log.info(JDBC_SQL);
        statement = connection.createStatement();
        rs = statement.executeQuery(JDBC_SQL);
        List<BiBean> invalidHistoryList = new ArrayList<>();
        while (rs.next()) {
            String temp = rs.getString(1);
            int count = rs.getInt(2);
            BiBean biBean = new BiBean();
            biBean.setHistory_invalid_cnt(count);
            biBean.setTemp(temp);
            invalidHistoryList.add(biBean);
        }
        rs.close();
        statement.close();

        Map<String, Long> mapInvalidHistory = new HashMap<>();
        for (int i = 0; i < invalidHistoryList.size(); i++) {
            BiBean biBean = invalidHistoryList.get(i);
            String s = biBean.getTemp();
//            String s = name.split("-")[0];
            Long aLong = mapInvalidHistory.get(s);
            if (aLong == null) {
                mapInvalidHistory.put(s, biBean.getHistory_invalid_cnt());
            } else {
                mapInvalidHistory.put(s, biBean.getHistory_invalid_cnt() + aLong);
            }
        }
        for (String key : mapInvalidHistory.keySet()) {
            BiBean biBean = mapAll.get(key);
            if (biBean == null) {
                BiBean bean = new BiBean();
                bean.setHistory_invalid_cnt(mapInvalidHistory.get(key));
                mapAll.put(key, bean);
            } else {
                biBean.setHistory_invalid_cnt(mapInvalidHistory.get(key));
                mapAll.put(key, biBean);
            }
        }


        //每个域下，下游数量------存量
        B = "where b.topic is not null ";
        JDBC_SQL = "select temp,count(1) from  \n" +
                "(\n" +
                "select  a.topic_a,a.temp  from  ("
                + exeSql + whereCondition
                + ")a\n" +
                "left join (select * from ke_consumer_group where cluster in(" + cluster + ") " +
                " and topic not like '%_libra' and  topic not like 'test_%' and `status`=0 " +
                " and `groups`<>'realtime-property-platform') b on a.topic_a=b.topic " +
                B + ") cd group by temp";

        log.info(JDBC_SQL);
        statement = connection.createStatement();
        rs = statement.executeQuery(JDBC_SQL);
        List<BiBean> historyConsumerList = new ArrayList<>();
        while (rs.next()) {
            String temp = rs.getString(1);
            int count = rs.getInt(2);
            BiBean biBean = new BiBean();
            biBean.setHistory_consumer_cnt(count);
            biBean.setTemp(temp);
            historyConsumerList.add(biBean);
        }
        rs.close();
        statement.close();

        Map<String, Long> historyConsumerMap = new HashMap<>();
        for (int i = 0; i < historyConsumerList.size(); i++) {
            BiBean biBean = historyConsumerList.get(i);
            String s = biBean.getTemp();
//            String s = name.split("-")[0];
            Long aLong = historyConsumerMap.get(s);
            if (aLong == null) {
                historyConsumerMap.put(s, biBean.getHistory_consumer_cnt());
            } else {
                historyConsumerMap.put(s, biBean.getHistory_consumer_cnt() + aLong);
            }
        }
        for (String key : historyConsumerMap.keySet()) {
            BiBean biBean = mapAll.get(key);
            if (biBean == null) {
                BiBean bean = new BiBean();
                bean.setHistory_consumer_cnt(historyConsumerMap.get(key));
                mapAll.put(key, bean);
            } else {
                biBean.setHistory_consumer_cnt(historyConsumerMap.get(key));
                mapAll.put(key, biBean);
            }
        }


        //---------------------------------------
        //每个域的资产数量------增量
        whereCondition = "and created>" + beforeMonth + " and created<" + nowMonth;// 增量资产

        JDBC_SQL = "select a.temp,count(1) from\n" +
                "("
                + exeSql + whereCondition
                + ")a\n" +
                "group by temp";

        log.info(JDBC_SQL);
        statement = connection.createStatement();
        rs = statement.executeQuery(JDBC_SQL);
        List<BiBean> addCntList = new ArrayList<>();
        while (rs.next()) {
            String temp = rs.getString(1);
            int count = rs.getInt(2);
            BiBean biBean = new BiBean();
            biBean.setAdd_cnt(count);
            biBean.setTemp(temp);
            addCntList.add(biBean);
        }
        rs.close();
        statement.close();

        //每个域的资产数量-----增量
        Map<String, Long> mapAdd = new HashMap<>();
        for (int i = 0; i < addCntList.size(); i++) {
            BiBean biBean = addCntList.get(i);
            String s = biBean.getTemp();
//            String s = name.split("-")[0];
            Long aLong = mapAdd.get(s);
            if (aLong == null) {
                mapAdd.put(s, biBean.getAdd_cnt());
            } else {
                mapAdd.put(s, biBean.getAdd_cnt() + aLong);
            }
        }

        for (String key : mapAdd.keySet()) {
            BiBean biBean = mapAll.get(key);
            if (biBean == null) {
                BiBean bean = new BiBean();
                bean.setAdd_cnt(mapAdd.get(key));
                mapAll.put(key, bean);
            } else {
                biBean.setAdd_cnt(mapAdd.get(key));
                mapAll.put(key, biBean);
            }
        }


        //每个域下无效资产数量------增量
        JDBC_SQL = "select temp,count(1) from  \n" +
                "(\n" +
                "select  a.topic_a,a.temp  from  ("
                + exeSql + whereCondition
                + ")a\n" +
                "left join (select * from ke_consumer_group where cluster in(" + cluster + ") " +
                " and topic not like '%_libra' and  topic not like 'test_%' and `status`=0" +
                " and `groups`<>'realtime-property-platform') b on a.topic_a=b.topic " +
                A + ") cd group by temp";
        log.info(JDBC_SQL);

        statement = connection.createStatement();
        rs = statement.executeQuery(JDBC_SQL);
        List<BiBean> invalidAddList = new ArrayList<>();
        while (rs.next()) {
            String temp = rs.getString(1);
            int count = rs.getInt(2);
            BiBean biBean = new BiBean();
            biBean.setAdd_invalid_cnt(count);
            biBean.setTemp(temp);
            invalidAddList.add(biBean);
        }
        rs.close();
        statement.close();


        Map<String, Long> mapInvalidAdd = new HashMap<>();
        for (int i = 0; i < invalidAddList.size(); i++) {
            BiBean biBean = invalidAddList.get(i);
            String s = biBean.getTemp();
//            String s = name.split("-")[0];
            Long aLong = mapInvalidAdd.get(s);
            if (aLong == null) {
                mapInvalidAdd.put(s, biBean.getAdd_invalid_cnt());
            } else {
                mapInvalidAdd.put(s, biBean.getAdd_invalid_cnt() + aLong);
            }
        }
        for (String key : mapInvalidAdd.keySet()) {
            BiBean biBean = mapAll.get(key);
            if (biBean == null) {
                BiBean bean = new BiBean();
                bean.setAdd_invalid_cnt(mapInvalidAdd.get(key));
                mapAll.put(key, bean);
            } else {
                biBean.setAdd_invalid_cnt(mapInvalidAdd.get(key));
                mapAll.put(key, biBean);
            }
        }


        //每个域下，下游数量------增量
        JDBC_SQL = "select temp,count(1) from  \n" +
                "(\n" +
                "select  a.topic_a,a.temp  from  ("
                + exeSql + whereCondition
                + ")a\n" +
                "left join (select * from ke_consumer_group where cluster in(" + cluster + ") " +
                " and topic not like '%_libra' and  topic not like 'test_%' and `status`=0" +
                " and `groups`<>'realtime-property-platform') b on a.topic_a=b.topic " +
                B + ") cd group by temp";
        log.info(JDBC_SQL);

        statement = connection.createStatement();
        rs = statement.executeQuery(JDBC_SQL);
        List<BiBean> addConsumerList = new ArrayList<>();
        while (rs.next()) {
            String temp = rs.getString(1);
            int count = rs.getInt(2);
            BiBean biBean = new BiBean();
            biBean.setAdd_consumer_cnt(count);
            biBean.setTemp(temp);
            addConsumerList.add(biBean);
        }
        rs.close();
        statement.close();

        Map<String, Long> addConsumerMap = new HashMap<>();
        for (int i = 0; i < addConsumerList.size(); i++) {
            BiBean biBean = addConsumerList.get(i);
            String s = biBean.getTemp();
//            String s = name.split("-")[0];
            Long aLong = addConsumerMap.get(s);
            if (aLong == null) {
                addConsumerMap.put(s, biBean.getAdd_consumer_cnt());
            } else {
                addConsumerMap.put(s, biBean.getAdd_consumer_cnt() + aLong);
            }
        }
        for (String key : addConsumerMap.keySet()) {
            BiBean biBean = mapAll.get(key);
            if (biBean == null) {
                BiBean bean = new BiBean();
                bean.setAdd_consumer_cnt(addConsumerMap.get(key));
                mapAll.put(key, bean);
            } else {
                biBean.setAdd_consumer_cnt(addConsumerMap.get(key));
                mapAll.put(key, biBean);
            }
        }

        for (String key : mapAll.keySet()) {
            BiBean biBean = mapAll.get(key);
            biBean.setDomain1(key.split("-")[0]);
            biBean.setDomain2(key.split("-")[1]);
            if (biBean.getHistory_cnt() - biBean.getHistory_invalid_cnt() != 0) {
                biBean.setHistory_avg_consumer_cnt(df.format((float) biBean.getHistory_consumer_cnt() / (biBean.getHistory_cnt() - biBean.getHistory_invalid_cnt())));
            }
            if (biBean.getAdd_cnt() - biBean.getAdd_invalid_cnt() != 0) {
                biBean.setAdd_avg_consumer_cnt(df.format((float) biBean.getAdd_consumer_cnt() / (biBean.getAdd_cnt() - biBean.getAdd_invalid_cnt())));
            }
        }
        return mapAll;
    }


    public List<BiBean> biDetails(String exeSql, String cluster, String isIncrement) throws ClassNotFoundException, SQLException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Date date = new Date();
        String nowMonth = sdf.format(date);
        nowMonth = "'" + nowMonth + "-01 00:00:00'";
        System.out.println("本月：" + nowMonth);
        if (Constant.BI.YES.equals(isIncrement)) {
            Date lastDate = TimeUtils.getLastDate(date);
            String beforeMonth = sdf.format(lastDate);
            beforeMonth = "'" + beforeMonth + "-01 00:00:00'";
            System.out.println("上月" + sdf.format(lastDate));
            whereCondition = " and created<" + nowMonth + " and created>" + beforeMonth;// 增量明细
        } else {
            //每个topic下游数量
            whereCondition = " and created<" + nowMonth;// 全量明细
        }

        exeSql = exeSql + " where cluster in (" + cluster + ") and `status`=2 and topic not like '%_libra' and  topic not like 'test_%'";

        Class.forName("com.mysql.cj.jdbc.Driver");
        byte[] decodedBytes = Base64.getDecoder().decode(password);
        password = new String(decodedBytes);
        Connection connection = DriverManager.getConnection(url, user, password);


        String C = "where b.topic is not null";
        String JDBC_SQL = "select topic_a,temp,count(1) from  \n" +
                "(\n" +
                "select  a.topic_a,a.temp  from  ("
                + exeSql + whereCondition
                + ")a\n" +
                "left join (select * from ke_consumer_group where cluster in(" + cluster + ") " +
                " and topic not like '%_libra' and  topic not like 'test_%' and `status`=0 " +
                " and `groups`<>'realtime-property-platform') b on a.topic_a=b.topic " +
                C + ") cd group by topic_a,temp order by temp";
        log.info(JDBC_SQL);
        Statement statement = connection.createStatement();
        ResultSet rs = statement.executeQuery(JDBC_SQL);
        List<BiBean> topicList = new ArrayList<>();
        while (rs.next()) {
            String topic = rs.getString(1);
            String temp = rs.getString(2);
            int count = rs.getInt(3);
            BiBean biBean = new BiBean();
            biBean.setTopic(topic);
            biBean.setDomain1(temp.split("-")[0]);
            biBean.setDomain2(temp.split("-")[1]);
            biBean.setTopic_consumer(count);
            topicList.add(biBean);
        }
        rs.close();
        statement.close();

        //无效topic
        C = "where b.topic is null";
        JDBC_SQL = "select topic_a,temp,count(1) from  \n" +
                "(\n" +
                "select  a.topic_a,a.temp  from  ("
                + exeSql + whereCondition
                + ")a\n" +
                "left join (select * from ke_consumer_group where cluster in(" + cluster + ") " +
                " and topic not like '%_libra' and  topic not like 'test_%' and `status`=0" +
                " and `groups`<>'realtime-property-platform') b on a.topic_a=b.topic " +
                C + ") cd group by topic_a,temp order by temp";
        log.info(JDBC_SQL);
        statement = connection.createStatement();
        rs = statement.executeQuery(JDBC_SQL);
        List<BiBean> topicInvalidList = new ArrayList<>();
        while (rs.next()) {
            String topic = rs.getString(1);
            String temp = rs.getString(2);
            BiBean biBean = new BiBean();
            biBean.setTopic(topic);
            biBean.setDomain1(temp.split("-")[0]);
            biBean.setDomain2(temp.split("-")[1]);
            biBean.setTopic_consumer(0);
            topicInvalidList.add(biBean);
            topicList.add(biBean);
        }
        rs.close();
        statement.close();
        return topicList;
    }
}