package com.dewu.property.stability;

import com.dewu.flink.template.job.domain.LibraTaskInfo;
import com.dewu.flink.template.job.service.LibraTaskInfoService;
import com.dewu.flink.template.utils.JsonUtil;
import com.dewu.utils.HttpClientUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 负责调度podkill事件
 * @author: zhiping.lin
 * @date: 2024/12/12
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class StabilityManager {

	private final LibraTaskInfoService libraTaskInfoService;

	private static final String POD_FETCH_URL = "https://kubeone-gw.shizhuang-inc.com/kubeadmin/api/v1/kubeTask/listNodeToFlinkPod";
	private static final String POD_KILL_URL = "";

	/**
	 * fetch pod list from kubeone
	 *
	 * @param cluster
	 * @param ecs_instances
	 * @return
	 * @throws Exception
	 */
	public List<DateTypePod> fetchPodFromCubeOne(String cluster, List<String> ecs_instances) throws Exception {
		Map<String, Object> params = new HashMap<>();
		params.put("cluster", cluster);
		params.put("offlineNodeNames", ecs_instances);
		String pStr = JsonUtil.writeString(params);
		HttpClientUtil.HttpClientResult httpClientResult = HttpClientUtil.doPost(POD_FETCH_URL, null, pStr);
		ListNodeToFlinkPodResult listNodeToFlinkPodResult = JsonUtil.parseObject(httpClientResult.getContent(), ListNodeToFlinkPodResult.class);
		if (listNodeToFlinkPodResult.getCode() != 200 || listNodeToFlinkPodResult.getErrorCode() != 200) {
			throw new RuntimeException("fetch pod list failed : " + listNodeToFlinkPodResult);
		}
		return buildPods(listNodeToFlinkPodResult.getData(), cluster);
	}

	private List<DateTypePod> fetchPod(String cluster, List<String> ecs_instances) throws Exception {
		List<DateTypePod> dateTypePods = fetchPodFromCubeOne(cluster, ecs_instances);
		recordIssuePodInfo(dateTypePods);
		return dateTypePods;
	}

	public void proLine(String cluster, List<String> ecs_instances) throws Exception {

		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		String currDay = simpleDateFormat.format(new Date());

		log.info("start pod kill process");
		List<DateTypePod> pods = fetchPod(cluster, ecs_instances);
		List<TaskPods> taskPods = buildTaskPods(pods);

		log.info("build task pods and send confirm msg.");
		schedulePodKillConfirmed(taskPods);

		log.info("schedule pods to kill");
		List<DateTypePod> pods2kill = fetchKillablePods(currDay);
		List<TaskPods> taskPods2kill = buildTaskPods(pods2kill);
		schedulePodKill(taskPods2kill);

		log.info("trigger pod kill");
		List<TaskPods> successTaskPods = new ArrayList<>();
		List<TaskPods> failedTaskPod = new ArrayList<>();
		taskPods2kill.forEach(tp -> {
			if (triggerPodKill(tp)) {
				successTaskPods.add(tp);
			} else {
				failedTaskPod.add(tp);
			}
		});
	}

	private List<DateTypePod> fetchKillablePods(String pt) throws Exception {
		// TODO fetch pods to kill from mysql
		return null;
	}

	/**
	 * build pods from data
	 *
	 * @param dateTypePods
	 * @return
	 */
	private List<TaskPods> buildTaskPods(List<DateTypePod> dateTypePods) {
		final List<TaskPods> taskPods = new ArrayList<>();
		dateTypePods.stream().collect(Collectors.groupingBy(dateTypePod -> dateTypePod.taskName)).forEach((key, value) -> taskPods.add(new TaskPods(key, value)));
		return taskPods;
	}

	/**
	 * build pods from data
	 *
	 * @param data
	 * @return
	 */
	private List<DateTypePod> buildPods(Map<String, Map<String, List<String>>> data, String cluster) {
		List<DateTypePod> dateTypePods = new ArrayList<>();
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		String currDay = simpleDateFormat.format(new Date());
		long ts = System.currentTimeMillis();
		data.forEach(
			(ecs, value) -> value.forEach(
				(taskName, podNames) -> podNames.forEach(
					podName -> {
						DateTypePod dateTypePod = new DateTypePod();
						if (!taskName.contains("/")) {
							dateTypePod.setTaskName(taskName);
						} else {
							String[] split = taskName.split("/");
							dateTypePod.setProjectName(split[0]);
							dateTypePod.setTaskName(split[1]);
							dateTypePod.setAccepted(true);
						}
						dateTypePod.setCluster(cluster);
						dateTypePod.setEcsInstance(ecs);
						dateTypePod.setPodName(podName);
						dateTypePod.setPt(currDay);
						dateTypePod.setExt(String.format("Triggered at %s on date %s", ts, currDay));
						dateTypePods.add(dateTypePod);
					})));
		return dateTypePods;
	}

	public void killPod(DateTypePod dateTypePod) throws InterruptedException {
		try {
			Map<String, String> params = new HashMap<>();
			// todo build params
			HttpClientUtil.doPost(POD_KILL_URL, null, "");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private boolean validateTask(LibraTaskInfo task) {
		return null != task && task.getStatus() == 3;
	}

	public boolean triggerPodKill(TaskPods taskPods) {
		String taskName = taskPods.getTaskName();
		log.info("trigger pod kill for task {}", taskName);
		LibraTaskInfo task = null;
		try {
			task = libraTaskInfoService.findTaskInfoByTaskName(taskName);
		} catch (Exception e) {
			e.printStackTrace();
		}
		if (validateTask(task)) {
			taskPods.getDateTypePods().forEach(dateTypePod -> {
				try {
					killPod(dateTypePod);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			});
			return true;
		}
		log.info("task {} is not ready to kill", taskName);
		return false;

	}

	public boolean validatePod() {
		// TODO check pod status with fetch pod.
		return true;
	}

	public void recordIssuePodInfo(List<DateTypePod> dateTypePods) {
		// TODO record issue pods to mysql, transactional.
		// design mysql table and insert data
	}

	public void schedulePodKillConfirmed(List<TaskPods> dateTypePods) throws InterruptedException {
		// TODO confirm pod kill with feishu
	}

	public void schedulePodKill(List<TaskPods> dateTypePods) throws InterruptedException {
		// trigger in 8 the next day.
	}

	public void fetchPodsToKill() {
		knowledgePodsToKill();
		// TODO fetch pods to kill from mysql
		// TODO fetch pods to kill from redis
	}

	public void knowledgePodsToKill() {
		// TODO knowledge pods to kill to users
	}


	public void taskStatusManager() {

	}

}

@Data
@AllArgsConstructor
class TaskPods {
	String taskName;
	List<DateTypePod> dateTypePods;
}

@Data
class DateTypePod {
	String cluster;
	String ecsInstance;
	String projectName;
	String taskName;
	String podName;
	String ext;
	String pt;
	boolean accepted;
}

@Data
class ListNodeToFlinkPodResult {
	int errorCode;
	int code;
	Map<String, Map<String, List<String>>> data;
}

class PodKillContext {
	String pt;
}

