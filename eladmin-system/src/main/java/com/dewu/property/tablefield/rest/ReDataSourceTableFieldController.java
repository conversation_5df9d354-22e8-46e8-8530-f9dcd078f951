/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.tablefield.rest;

import com.dewu.flink.template.meta.tablefield.domain.ReDataSourceTableField;
import com.dewu.flink.template.meta.tablefield.service.ReDataSourceTableFieldService;
import com.dewu.flink.template.meta.tablefield.service.dto.ReDataSourceTableFieldQueryCriteria;
import com.dewu.annotation.Log;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-06-09
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "表字段信息管理")
@RequestMapping("/api/reDataSourceTableField")
public class ReDataSourceTableFieldController {


    private final ReDataSourceTableFieldService reDataSourceTableFieldService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('reDataSourceTableField:list')")
    public void exportReDataSourceTableField(HttpServletResponse response, ReDataSourceTableFieldQueryCriteria criteria) throws IOException {
        reDataSourceTableFieldService.download(reDataSourceTableFieldService.queryAll(criteria), response);
    }

    @GetMapping
    @ApiOperation("查询表字段信息")
    @PreAuthorize("@el.check('reDataSourceTableField:list')")
    public ResponseEntity<Object> queryReDataSourceTableField(ReDataSourceTableFieldQueryCriteria criteria, Pageable pageable){
//        Long sourceTableId = criteria.getSourceTableId();
//        if(sourceTableId!=null) {
//            criteria.setReDataSourceMetadataId(sourceTableId);
//            criteria.setSourceTableId(null);
//        }
//        Long dimTableId = criteria.getDimTableId();
//        if(dimTableId!=null) {
//            criteria.setReDataSourceMetadataId(dimTableId);
//            criteria.setDimTableId(null);
//        }
        return new ResponseEntity<>(reDataSourceTableFieldService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增表字段信息")
    @ApiOperation("新增表字段信息")
    @PreAuthorize("@el.check('reDataSourceTableField:add')")
    public ResponseEntity<Object> createReDataSourceTableField(@Validated @RequestBody ReDataSourceTableField resources){
        return new ResponseEntity<>(reDataSourceTableFieldService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改表字段信息")
    @ApiOperation("修改表字段信息")
    @PreAuthorize("@el.check('reDataSourceTableField:edit')")
    public ResponseEntity<Object> updateReDataSourceTableField(@Validated @RequestBody ReDataSourceTableField resources){
        reDataSourceTableFieldService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除表字段信息")
    @ApiOperation("删除表字段信息")
    @PreAuthorize("@el.check('reDataSourceTableField:del')")
    public ResponseEntity<Object> deleteReDataSourceTableField(@RequestBody Long[] ids) {
        reDataSourceTableFieldService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
