/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.datasource.rest;

import com.dewu.annotation.Log;
import com.dewu.flink.template.meta.datasource.domain.ReDataSource;
import com.dewu.flink.template.meta.datasource.service.ReDataSourceService;
import com.dewu.flink.template.meta.datasource.service.dto.ReDataSourceDto;
import com.dewu.flink.template.meta.datasource.service.dto.ReDataSourceQueryCriteria;
import com.dewu.modules.system.domain.Dict;
import com.dewu.modules.system.domain.DictDetail;
import com.dewu.modules.system.repository.DictRepository;
import com.dewu.modules.system.service.DictDetailService;
import com.dewu.utils.TelnetUtils;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;


/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-06-09
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "数据源管理")
@RequestMapping("/api/reDataSource")
public class ReDataSourceController {

    private final ReDataSourceService reDataSourceService;
    private final DictRepository dictRepository;
    private final DictDetailService dictDetailService;

    @GetMapping
    @ApiOperation("查询数据源管理")
    @PreAuthorize("@el.check('reDataSource:list')")
    public ResponseEntity<Object> queryReDataSource(ReDataSourceQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(reDataSourceService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增数据源管理")
    @ApiOperation("新增数据源管理")
    @PreAuthorize("@el.check('reDataSource:add')")
    public ResponseEntity<Object> createReDataSource(@Validated @RequestBody ReDataSource resources) {
        ResponseEntity<Object> responseEntity = TelnetUtils.telnet(resources);
        if (responseEntity != null) return responseEntity;
        ReDataSourceDto dataSourceDto = reDataSourceService.create(resources);
        Dict dict = dictRepository.queryByName("数据源名称");
        Long dictId = dict.getId();
        DictDetail dictDetail = new DictDetail();
        dictDetail.setLabel(dataSourceDto.getDataSourceName());
        dictDetail.setValue(dataSourceDto.getId() + "");
        Dict d = new Dict();
        d.setId(dictId);
        dictDetail.setDict(d);
        dictDetailService.create(dictDetail);
        return new ResponseEntity<>(dataSourceDto, HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改数据源管理")
    @ApiOperation("修改数据源管理")
    @PreAuthorize("@el.check('reDataSource:edit')")
    public ResponseEntity<Object> updateReDataSource(@Validated @RequestBody ReDataSource resources) {
        ResponseEntity<Object> responseEntity = TelnetUtils.telnet(resources);
        if (responseEntity != null) return responseEntity;
        reDataSourceService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }


//    @DeleteMapping
//    @Log("删除数据源管理")
//    @ApiOperation("删除数据源管理")
//    @PreAuthorize("@el.check('reDataSource:del')")
//    public ResponseEntity<Object> deleteReDataSource(@RequestBody Long[] ids) {
//        reDataSourceService.deleteAll(ids);
//        return new ResponseEntity<>(HttpStatus.OK);
//    }




}
