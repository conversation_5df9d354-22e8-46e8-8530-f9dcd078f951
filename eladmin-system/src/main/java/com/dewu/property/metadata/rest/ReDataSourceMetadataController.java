/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.metadata.rest;

import com.dewu.annotation.Log;
import com.dewu.flink.template.meta.datasource.service.ReDataSourceService;
import com.dewu.flink.template.meta.datasource.service.dto.ReDataSourceDto;
import com.dewu.flink.template.meta.tablefield.service.ReDataSourceTableFieldService;
import com.dewu.flink.template.meta.tablefield.service.dto.ReDataSourceTableFieldDto;
import com.dewu.flink.template.meta.tableinfo.domain.ReDataSourceMetadata;
import com.dewu.flink.template.meta.tableinfo.service.dto.ReDataSourceMetadataQueryCriteria;
import com.dewu.property.metadata.service.ReDataSourceMetadataService;
import com.dewu.utils.Constant;
import com.dewu.utils.SecurityUtils;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-06-09
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "表管理管理")
@RequestMapping("/api/reDataSourceMetadata")
public class ReDataSourceMetadataController {

    private final ReDataSourceService reDataSourceService;

    private final ReDataSourceMetadataService reDataSourceMetadataService;

    private final ReDataSourceTableFieldService reDataSourceTableFieldService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('reDataSourceMetadata:list')")
    public void exportReDataSourceMetadata(HttpServletResponse response, ReDataSourceMetadataQueryCriteria criteria) throws IOException {
        reDataSourceMetadataService.download(reDataSourceMetadataService.queryAll(criteria), response);
    }

    @GetMapping
    @ApiOperation("表管理-查询")
    @PreAuthorize("@el.check('reDataSourceMetadata:list')")
    public ResponseEntity<Object> queryReDataSourceMetadata(ReDataSourceMetadataQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(reDataSourceMetadataService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @SneakyThrows
    @PostMapping
    @Log("表管理-新增表")
    @ApiOperation("表管理-新增表")
    @PreAuthorize("@el.check('reDataSourceMetadata:add')")
    public ResponseEntity<Object> createReDataSourceMetadata(@Validated @RequestBody ReDataSourceMetadata resources) {
        String mess = reDataSourceMetadataService.createTableTryBuildDDL(resources);
        if(StringUtils.isNotBlank(mess)) {
            return new ResponseEntity<>(HttpStatus.valueOf(mess));
        }
        return new ResponseEntity<>(HttpStatus.CREATED);
    }


    @PutMapping
    @Log("表管理-修改表")
    @ApiOperation("表管理-修改表")
    @PreAuthorize("@el.check('reDataSourceMetadata:edit')")
    public ResponseEntity<Object> updateReDataSourceMetadata(@Validated @RequestBody ReDataSourceMetadata resources) {
        List<ReDataSourceTableFieldDto> tableFieldDtos = reDataSourceTableFieldService.findByTableId(resources.getId());
        Set<String> dbTableField = new HashSet<>();
        for (int j = 0; j < tableFieldDtos.size(); j++) {
            ReDataSourceTableFieldDto reDataSourceTableFieldDto = tableFieldDtos.get(j);
            dbTableField.add(reDataSourceTableFieldDto.getFieldName());
        }
        ReDataSourceDto dataSourceDto = reDataSourceService.findById(resources.getReDataSourceId());
        //主要目的就是，Kafka数据源，如果表有ddl字段，那么修改唯一键时一定要校验唯一键是否存在，存在才可以修改
        //其他数据源直接放开修改条件
        if(dataSourceDto.getDataSourceType()== Constant.DataSourceType.KAFKA && (dbTableField.size()==0||StringUtils.isBlank(resources.getUniqueKey())||
                dbTableField.contains(resources.getUniqueKey()))) {
            reDataSourceMetadataService.update(resources);
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        } if(dataSourceDto.getDataSourceType()!=Constant.DataSourceType.KAFKA) {
            reDataSourceMetadataService.update(resources);
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        return new ResponseEntity<>(HttpStatus.valueOf("修改的唯一键字段在表的字段中不存在，请核实后再修改"));

    }

    @PutMapping(value = "/sync/{id}")
    @Log("表管理-同步表字段信息")
    @ApiOperation("表管理-同步表字段信息")
    @PreAuthorize("@el.check('reDataSourceMetadata:sync')")
    public ResponseEntity<Object> syncReDataSourceMetadataField(@PathVariable Long id) {
        String mess = reDataSourceMetadataService.syncKafkaTableField(id);
        return new ResponseEntity<>(mess, HttpStatus.OK);
    }

    @GetMapping(value = "/sync/twotable")
    @Log("表管理-同步表字段信息")
    @ApiOperation("表管理-同步表字段信息")
    @PreAuthorize("@el.check('reDataSourceMetadata:sync')")
    public ResponseEntity<Object> syncReDataSourceMetadataField(Long tableId1, Long tableId2) {
        String mess1 = reDataSourceMetadataService.syncKafkaTableField(tableId1);
        String mess2 = reDataSourceMetadataService.syncKafkaTableField(tableId2);
        return new ResponseEntity<>(mess1 + "," + mess2, HttpStatus.OK);
    }

    @GetMapping(value = "/sync/tables")
    @Log("表管理-同步表字段信息")
    @ApiOperation("表管理-同步表字段信息")
    @PreAuthorize("@el.check('reDataSourceMetadata:sync')")
    public ResponseEntity<Object> syncReDataSourceMetadataField(Long[] tableIds) {
        String mess = "";
        for (int i = 0; i < tableIds.length; i++) {
            Long tableId = tableIds[i];
            String messTmp = reDataSourceMetadataService.syncKafkaTableField(tableId);
            mess = StringUtils.join(mess, messTmp);
        }
        return new ResponseEntity<>(mess, HttpStatus.OK);
    }


//    @GetMapping(value = "/collect/tables")
//    @Log("表管理-输出表数据采集")
//    @ApiOperation("表管理-输出表数据采集")
//    @PreAuthorize("@el.check('reDataSourceMetadata:sync')")
//    public ResponseEntity<Object> consumerOutputMess(Long tableId,String jobName) {
//        return new ResponseEntity<>(reDataSourceMetadataService.consumerOutputMess(tableId,jobName), HttpStatus.OK);
//    }

    @GetMapping(value = "/collect/tables")
    @Log("表管理-输出表数据采集")
    @ApiOperation("表管理-输出表数据采集")
    @PreAuthorize("@el.check('reDataSourceMetadata:sync')")
    public ResponseEntity<Object> consumerOutputMess(Long[] tableIds) {
        return new ResponseEntity<>(reDataSourceMetadataService.consumerTemplateJobOutputMess(tableIds), HttpStatus.OK);
    }

    /**
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/9/28
     */
    @DeleteMapping
    @Log("表管理-删除表")
    @ApiOperation("表管理-删除表")
    @PreAuthorize("@el.check('reDataSourceMetadata:del')")
    public ResponseEntity<Object> deleteReDataSourceMetadata(@RequestBody Long[] ids) {
        if (ids.length > 1) {
            return new ResponseEntity<>(HttpStatus.valueOf("无法批量删除"));
        }
        if (!"admin".equalsIgnoreCase(SecurityUtils.getCurrentUsername())) {
            return new ResponseEntity<>(HttpStatus.valueOf("非管理员，没有权限删除"));
        }
        reDataSourceMetadataService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
