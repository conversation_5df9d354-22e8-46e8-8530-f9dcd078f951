/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.metadata.service.impl;

import com.dewu.flink.template.meta.datasource.domain.ReDataSource;
import com.dewu.flink.template.meta.datasource.repository.ReDataSourceRepository;
import com.dewu.flink.template.meta.datasource.service.ReDataSourceService;
import com.dewu.flink.template.meta.datasource.service.dto.ReDataSourceDto;
import com.dewu.flink.template.meta.tablefield.domain.ReDataSourceTableField;
import com.dewu.flink.template.meta.tablefield.repository.ReDataSourceTableFieldRepository;
import com.dewu.flink.template.meta.tablefield.service.ReDataSourceTableFieldService;
import com.dewu.flink.template.meta.tablefield.service.dto.ReDataSourceTableFieldDto;
import com.dewu.flink.template.meta.tableinfo.domain.ReDataSourceMetadata;
import com.dewu.flink.template.meta.tableinfo.domain.ReDataSourceMetadataSon;
import com.dewu.flink.template.meta.tableinfo.repository.ReDataSourceMetadataRepository;
import com.dewu.flink.template.meta.tableinfo.service.dto.ReDataSourceMetadataDto;
import com.dewu.flink.template.meta.tableinfo.service.dto.ReDataSourceMetadataQueryCriteria;
import com.dewu.flink.template.meta.tableinfo.service.mapstruct.ReDataSourceMetadataMapper;
import com.dewu.flink.template.utils.GalaxyUtil;
import com.dewu.plug.service.TePlugSwitchService;
import com.dewu.property.metadata.service.ReDataSourceMetadataService;
import com.dewu.utils.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.util.Preconditions;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-06-09
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ReDataSourceMetadataServiceImpl implements ReDataSourceMetadataService {

    private final ReDataSourceMetadataRepository reDataSourceMetadataRepository;

    private final ReDataSourceRepository reDataSourceRepository;

    private final ReDataSourceTableFieldRepository reDataSourceTableFieldRepository;

    private final ReDataSourceTableFieldService reDataSourceTableFieldService;

    private final ReDataSourceService reDataSourceService;

    private final ReDataSourceMetadataMapper reDataSourceMetadataMapper;

    private final TePlugSwitchService tePlugSwitchService;

    @Override
    public Map<String, Object> queryAll(ReDataSourceMetadataQueryCriteria criteria, Pageable pageable) {
        Page<ReDataSourceMetadata> page = reDataSourceMetadataRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);

        List<ReDataSourceMetadata> content = page.getContent();
        List<ReDataSourceMetadataSon> list = new ArrayList<>();
        for (int i = 0; i < content.size(); i++) {
            ReDataSourceMetadata dataSourceMetadata = content.get(i);
            Long reDataSourceId = dataSourceMetadata.getReDataSourceId();
            ReDataSourceDto reDataSource = reDataSourceService.findById(reDataSourceId);
            String dataSourceName = "";
            Integer dataSourceType = 0;
            Integer usePlace = 0;
            if (reDataSource != null) {
                dataSourceName = reDataSource.getDataSourceName();
                dataSourceType = reDataSource.getDataSourceType();
                usePlace = reDataSource.getUsePlace();
            }

            ReDataSourceMetadataSon dataSourceTableFieldSon = new ReDataSourceMetadataSon();
            BeanUtils.copyProperties(dataSourceMetadata, dataSourceTableFieldSon);
            dataSourceTableFieldSon.setDataSourceName(dataSourceName);
            dataSourceTableFieldSon.setDataSourceType(dataSourceType);
            dataSourceTableFieldSon.setDataSourceType(dataSourceType);
            dataSourceTableFieldSon.setUsePlace(usePlace);
            list.add(dataSourceTableFieldSon);
        }
        return PageUtil.toPageV1(page.map(reDataSourceMetadataMapper::toDto), list);

    }

    @Override
    public List<ReDataSourceMetadataDto> queryAll(ReDataSourceMetadataQueryCriteria criteria) {
        return reDataSourceMetadataMapper.toDto(reDataSourceMetadataRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    public List<ReDataSourceMetadataDto> queryAllKafkaTables(long dataSourceId) {
        ReDataSourceMetadataQueryCriteria criteria=new ReDataSourceMetadataQueryCriteria();
        criteria.setReDataSourceId(dataSourceId);
        return reDataSourceMetadataMapper.toDto(reDataSourceMetadataRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public ReDataSourceMetadataDto findById(Long id) {
        ReDataSourceMetadata reDataSourceMetadata = reDataSourceMetadataRepository.findById(id).orElseGet(ReDataSourceMetadata::new);
        return reDataSourceMetadataMapper.toDto(reDataSourceMetadata);
    }

    @Override
    @Transactional
    public ReDataSourceMetadata findByIdNoDto(Long id) {
        return reDataSourceMetadataRepository.findById(id).orElseGet(ReDataSourceMetadata::new);
    }


    @Override
    @Transactional
    public List<ReDataSourceMetadataDto> findTableWithoutDDLByDataSourceId(long reDataSourceId) {
        return reDataSourceMetadataMapper.toDto(reDataSourceMetadataRepository.findTableWithoutDDLByDataSourceId(reDataSourceId));
    }

    @Override
    public List<Map<String,Object>> findControlTableDataSourceInfo() {
        return reDataSourceMetadataRepository.findControlTableDataSourceInfo();
    }

    @Override
    public List<Map<String,Object>> findControlTableDataSourceInfo(String tableId) {
        return reDataSourceMetadataRepository.findControlTableDataSourceInfo(tableId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTableTryBuildDDL(ReDataSourceMetadata param) throws Exception {
        Long dataSourceId = param.getReDataSourceId();
        ReDataSource reDataSource = reDataSourceRepository.findById(dataSourceId).get();

        Integer usePlace = Integer.valueOf(reDataSource.getUsePlace());
        Integer dataSourceType = Integer.valueOf(reDataSource.getDataSourceType());
        String ip = reDataSource.getIp();
        String userName = reDataSource.getUserName();
        String password = reDataSource.getPassword();
        String dbName = reDataSource.getDbName();

        if (dataSourceType == Constant.DataSourceType.RDS) {
            param.setPartitionKey(null);
            param.setColumnFamily(null);
            ReDataSourceMetadataDto tableInfo = reDataSourceMetadataMapper.toDto(reDataSourceMetadataRepository.save(param));

            Class.forName(Constant.MYSQL_DRIVER);
            Connection connection = JdbcUtils.getConnection(dbName, ip, userName, password);
            LinkedHashMap<String, String> dbTableFieldAndInitData = JdbcUtils.getDbTableFieldAndInitData(tableInfo, connection);
            addTableField(reDataSourceTableFieldService, dbTableFieldAndInitData, tableInfo);

        } else if (dataSourceType == Constant.DataSourceType.KAFKA) {

            if (usePlace == Constant.UsePlace.INNER) {

                if(reDataSource.isBinlogSource() && StringUtils.isBlank(param.getUniqueKey())) {
                    param.setUniqueKey("id");
                }

                ReDataSourceMetadata tableInfo = reDataSourceMetadataRepository.save(param);
                Set<String> fieldNameList = SyncPropertyUtils.syncKafkaTableField(reDataSourceTableFieldService, reDataSource, reDataSourceMetadataMapper.toDto(tableInfo));
                if(fieldNameList.size()>0) {// && !fieldNameList.contains(tableInfo.getUniqueKey()) ) {
                    String uniqueKey = (tableInfo.getUniqueKey()==null)? "":tableInfo.getUniqueKey();
                    String trim = uniqueKey.trim();
                    String[] split = trim.split(",");
                    for (int i = 0; i < split.length; i++) {
                        if (!fieldNameList.contains(split[i])) {
                            tableInfo.setUniqueKey("");
                            this.update(tableInfo);
                            return "设置的唯一键字段在表的字段中不存在，系统将更改为空值，请核实后立刻去补充表的唯一键字段信息";
                        }
                    }
                }
            } else{
                reDataSourceMetadataMapper.toDto(reDataSourceMetadataRepository.save(param));
            }

        } else if (dataSourceType == Constant.DataSourceType.ODPS) {
            try {
                param.setColumnFamily(null);
                String odpsEngine = tePlugSwitchService.findOdpsEngine();
                ReDataSourceMetadataDto tableInfo = reDataSourceMetadataMapper.toDto(reDataSourceMetadataRepository.save(param));
                LinkedHashMap<String, String> dbTableFieldAndInitData;
                if(com.dewu.flink.template.utils.Constant.PLUG_SWITCH.SWITCH_ODPS.equalsIgnoreCase(odpsEngine)) {
                    Class.forName(Constant.ODPS_DRIVER);
                    Connection connection = DriverManager.getConnection("jdbc:odps:" + ip + "?project=" + dbName, userName, password);
                    dbTableFieldAndInitData = JdbcUtils.getDbTableFieldAndInitData(tableInfo, connection);
                    addTableField(reDataSourceTableFieldService, dbTableFieldAndInitData, tableInfo);
                }else  if(com.dewu.flink.template.utils.Constant.PLUG_SWITCH.SWITCH_GALAXY.equalsIgnoreCase(odpsEngine))  {
                    dbTableFieldAndInitData = GalaxyUtil.getTableInfo(dbName, tableInfo.getTableName(), userName, password);
                    addTableField(reDataSourceTableFieldService, dbTableFieldAndInitData, tableInfo);
                }
            } catch (Exception e) {
                log.warn("获取odps表信息失败",e);
                throw e;
            }
        } else if (dataSourceType == Constant.DataSourceType.HBASE) {
            param.setPartitionKey(null);
            reDataSourceMetadataMapper.toDto(reDataSourceMetadataRepository.save(param));
        }else {
            param.setPartitionKey(null);
            reDataSourceMetadataMapper.toDto(reDataSourceMetadataRepository.save(param));
        }
        return "";
    }


    /**
     * 功能描述
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/9/22
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReDataSourceMetadataDto create(ReDataSourceMetadata param) throws Exception {
        return reDataSourceMetadataMapper.toDto(reDataSourceMetadataRepository.save(param));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ReDataSourceMetadata param) {
        ReDataSourceMetadata tableInfo = reDataSourceMetadataRepository.findById(param.getId()).orElseGet(ReDataSourceMetadata::new);
        ValidationUtil.isNull(tableInfo.getId(),"tableInfo","id",tableInfo.getId());
        tableInfo.copy(param);
        reDataSourceMetadataRepository.save(param);
    }


    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            ReDataSourceMetadata reDataSourceMetadata = reDataSourceMetadataRepository.findById(id).get();
            Long tableId = reDataSourceMetadata.getId();
            //删除表字段信息
            List<ReDataSourceTableFieldDto> byTableId = reDataSourceTableFieldService.findByTableId(tableId);
            for (int i = 0; i < byTableId.size(); i++) {
                ReDataSourceTableFieldDto reDataSourceTableFieldDto = byTableId.get(i);
                reDataSourceTableFieldRepository.deleteById(reDataSourceTableFieldDto.getId());
            }
            //删除表信息
            reDataSourceMetadataRepository.deleteById(id);
            break;
        }
    }

    @Override
    public void download(List<ReDataSourceMetadataDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ReDataSourceMetadataDto reDataSourceMetadata : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("创建时间", reDataSourceMetadata.getCreateTime());
            map.put("修改时间", reDataSourceMetadata.getModifyTime());
            map.put("表名", reDataSourceMetadata.getTableName());
            map.put("表描述", reDataSourceMetadata.getTableDesc());
            map.put("数据容量", reDataSourceMetadata.getDataSize());
            map.put("数据条数", reDataSourceMetadata.getDataNum());
            map.put("所属数据源id", reDataSourceMetadata.getReDataSourceId());
            map.put("创建者", reDataSourceMetadata.getCreateBy());
            map.put("部门", reDataSourceMetadata.getDept());
            map.put("业务域", reDataSourceMetadata.getBusiness_1());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }


    /**
     * 功能描述 只同步Kafka的数据
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/9/28
     */
    @Override
    public String syncKafkaTableField(Long tableId) {
        ReDataSourceMetadata tableInfo = reDataSourceMetadataRepository.findById(tableId).get();
        List<ReDataSourceTableFieldDto> tableFieldDtoList = reDataSourceTableFieldService.findByTableId(tableId);
        ReDataSource dataSource = reDataSourceRepository.findById(tableInfo.getReDataSourceId()).get();
        if (Integer.valueOf(dataSource.getDataSourceType()) == Constant.DataSourceType.KAFKA) {
            Set<String> fieldNameList = SyncPropertyUtils.syncKafkaTableField(reDataSourceTableFieldService, dataSource, reDataSourceMetadataMapper.toDto(tableInfo));
            if(fieldNameList.size()>0 && !fieldNameList.contains(tableInfo.getUniqueKey()) ) {
                tableInfo.setUniqueKey("");
                this.update(tableInfo);// 返回消息，
                return "设置的唯一键字段在表的字段中不存在，系统将唯一键更改为空值，请核实后立刻去补充表的唯一键字段信息";
            }else {
                return tableInfo.getTableName()+"表已有"+tableFieldDtoList.size()+"字段,本次同步" + (fieldNameList.size()-tableFieldDtoList.size()) + "字段（字段数为0,请核查dts采集任务是否正确）";
            }
        } else {
            return tableInfo.getTableName()+"非Kafka表无需同步字段信息";
        }
    }


    @Override
    public String consumerTemplateJobOutputMess(Long tableId, String jobName) {
        ReDataSourceMetadata tableInfo = reDataSourceMetadataRepository.findById(tableId).get();
        ReDataSource dataSource = reDataSourceRepository.findById(tableInfo.getReDataSourceId()).get();
        if (Integer.valueOf(dataSource.getDataSourceType()) == Constant.DataSourceType.KAFKA) {
            if(dataSource.getDataSourceName().toLowerCase().indexOf("dcheck")>=0) {
                return SyncPropertyUtils.consumerOutputMess(jobName, dataSource, reDataSourceMetadataMapper.toDto(tableInfo));
            }else {
                return "非dcheck源下表不支持采集";
            }
        } else {
            return tableInfo.getTableName()+"非Kafka表";
        }
    }

    @Override
    public  List<Map<String, String>> consumerTemplateJobOutputMess(Long[] tableIds) {
        List<Map<String, String>> list=new LinkedList<>();
        for (int i = 0; i < tableIds.length; i++) {
            Long tableId = tableIds[i];
            Optional<ReDataSourceMetadata> optId = reDataSourceMetadataRepository.findById(tableId);
            Preconditions.checkState(optId.isPresent(),"tableId " + tableId +" not present.");
            ReDataSourceMetadata tableInfo = reDataSourceMetadataRepository.findById(tableId).get();
            ReDataSource dataSource = reDataSourceRepository.findById(tableInfo.getReDataSourceId()).get();
            if(!dataSource.getDataSourceType().equals("2")){
                continue;
            }
            Map<String, String> map=new HashMap<>();
            map.put("mess",SyncPropertyUtils.consumerByLatest(dataSource, reDataSourceMetadataMapper.toDto(tableInfo)));
            map.put("tableId",tableId+"");
            map.put("tableName", tableInfo.getTableName());
            list.add(map);
        }
        return list;
    }


    private void addTableField(ReDataSourceTableFieldService reDataSourceTableFieldService, LinkedHashMap<String, String> fieldNameAndTypeMap, ReDataSourceMetadataDto tableInfo) {
        Long tableId = tableInfo.getId();
        Iterator<Map.Entry<String, String>> iterator = fieldNameAndTypeMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> next = iterator.next();
            ReDataSourceTableField tableField = new ReDataSourceTableField();
            tableField.setFieldName(next.getKey());
            tableField.setDataType(next.getValue());
            tableField.setReDataSourceMetadataId(tableId);
            reDataSourceTableFieldService.create(tableField);
        }
    }


}
