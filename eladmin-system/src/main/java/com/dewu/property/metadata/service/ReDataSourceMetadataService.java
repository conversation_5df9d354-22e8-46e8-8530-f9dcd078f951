/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.metadata.service;

import com.dewu.flink.template.meta.tableinfo.domain.ReDataSourceMetadata;
import com.dewu.flink.template.meta.tableinfo.service.dto.ReDataSourceMetadataDto;
import com.dewu.flink.template.meta.tableinfo.service.dto.ReDataSourceMetadataQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-06-09
**/
public interface ReDataSourceMetadataService {
    /**
     * 查询数据分页
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String,Object>
     */
    Map<String,Object> queryAll(ReDataSourceMetadataQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     * @param criteria 条件参数
     * @return List<ReDataSourceMetadataDto>
     */
    List<ReDataSourceMetadataDto> queryAll(ReDataSourceMetadataQueryCriteria criteria);

    List<ReDataSourceMetadataDto> queryAllKafkaTables(long dataSourceId);

    /**
     * 根据ID查询
     * @param id ID
     * @return ReDataSourceMetadataDto
     */
    ReDataSourceMetadataDto findById(Long id);

    /**
     * 根据ID查询
     * @param id ID
     * @return ReDataSourceMetadataDto
     */
    ReDataSourceMetadata findByIdNoDto(Long id);

    /**
     * 根据ID查询
     * @param reDataSourceId reDataSourceId
     * @return ReDataSourceMetadataDto
     */
    List<ReDataSourceMetadataDto> findTableWithoutDDLByDataSourceId(long reDataSourceId);

    List<Map<String,Object>> findControlTableDataSourceInfo();

    List<Map<String,Object>> findControlTableDataSourceInfo(String tableId);

    /**
     * 创建
     * @param resources /
     * @return ReDataSourceMetadataDto
     */
    ReDataSourceMetadataDto create(ReDataSourceMetadata resources) throws Exception;

    /**
     * 创建
     * @param resources /
     * @return ReDataSourceMetadataDto
     */
    String createTableTryBuildDDL(ReDataSourceMetadata resources) throws Exception;

    /**
     * 编辑
     * @param resources /
     */
    void update(ReDataSourceMetadata resources);

    /**
     * 多选删除
     * @param ids /
     */
    void deleteAll(Long[] ids);

    /**
     * 导出数据
     * @param all 待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<ReDataSourceMetadataDto> all, HttpServletResponse response) throws IOException;

    /**
     *功能描述 同步Kafka表下的数据
     * <AUTHOR>
     * @date 2022/9/22
     * @param
     * @return
     */
    String syncKafkaTableField(Long tableId);

    String consumerTemplateJobOutputMess(Long tableId, String jobName);

    List<Map<String, String>> consumerTemplateJobOutputMess(Long[] tableId);
}