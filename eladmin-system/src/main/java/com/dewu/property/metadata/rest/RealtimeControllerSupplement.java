package com.dewu.property.metadata.rest;

import com.dewu.annotation.rest.AnonymousPostMapping;
import com.dewu.property.metadata.service.ReDataSourceMetadataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: WangLin
 * Date: 2023/6/19 下午6:01
 * Description: template-engine模块RealtimeController类的补充方法
 */
@RestController
@RequestMapping("/api")
public class RealtimeControllerSupplement {

	private static final Logger LOG = LoggerFactory.getLogger(RealtimeControllerSupplement.class);

	@Autowired
	private ReDataSourceMetadataService reDataSourceMetadataService;

	/**
	 * 后端返回前端的数据结构
	 *
	 * @param code    状态码
	 * @param message 提示消息
	 * @param data    数据
	 * @return 整个结构
	 */
	private Map<String, Object> createResult(String code, String message, Object data) {
		Map<String, Object> result = new HashMap<>();
		result.put("code", code);
		result.put("mes", message);
		result.put("data", data);
		return result;
	}


	/**
	 * 根据表ids查询表详细信息
	 */
	@AnonymousPostMapping("/job/tableMessage")
	public Object getTableMessage(@RequestBody Long[] tableIds) {
		try {
			List<Map<String, String>> maps = reDataSourceMetadataService.consumerTemplateJobOutputMess(tableIds);
			return createResult("200", "OK", maps);
		} catch (Exception e) {
			e.printStackTrace();
			LOG.info("获取表采样数据接口出错", e);
			return createResult("500", e.getMessage(), null);
		}
	}
}
