/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.lineage.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-07-14
**/
@Data
public class TableRelevanceDto implements Serializable {

    /** 自增id */
    private Long id;

    private Long tableId;

    /** 来源物理表 */
    private String physicsTable;

    /** 来源视图表 */
//    private String sourceViewTable;

    /** 来源数据库 */
    private String tableDatabase;

    /** 来源数据库类型 */
    private String tableDbType;

    private String tableInstance;

    /** 作业名称 */
//    private String jobName;

    /** 空间名称 */
//    private String projectName;

    /** 创建时间 */
    private Timestamp createTime;

    /** 修改时间 */
    private Timestamp modifyTime;

    /** 作业归属人 */
    private String creator;

    private boolean isDelete;

    private Integer upstreamJobNum;

    private Integer upstreamTableNum;

    private Integer downstreamJobNum;

    private Integer downstreamTableNum;

}