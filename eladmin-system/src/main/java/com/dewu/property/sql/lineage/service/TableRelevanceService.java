/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.sql.lineage.service;

import com.dewu.property.sql.lineage.domain.TableRelevance;
import com.dewu.property.sql.lineage.service.dto.TableRelevanceDto;
import com.dewu.property.sql.lineage.service.dto.TableRelevanceQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务接口
 * @date 2023-07-14
 **/
public interface TableRelevanceService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(TableRelevanceQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<FlinkSqlLineageDto>
     */
    List<TableRelevanceDto> queryAll(TableRelevanceQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return FlinkSqlLineageDto
     */
    TableRelevanceDto findById(Long id);

    /**
     * 创建
     *
     * @param resources /
     * @return FlinkSqlLineageDto
     */
    TableRelevanceDto create(TableRelevance resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(TableRelevance resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Long[] ids);

}