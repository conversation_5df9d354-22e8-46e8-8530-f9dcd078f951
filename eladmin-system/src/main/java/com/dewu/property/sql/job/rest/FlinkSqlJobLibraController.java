/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.job.rest;

import com.dewu.annotation.Log;
import com.dewu.property.sql.job.domain.FlinkSqlJobLibra;
import com.dewu.property.sql.job.service.FlinkSqlJobLibraService;
import com.dewu.property.sql.job.service.dto.FlinkSqlJobLibraQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-07-17
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "自建平台sql作业管理")
@RequestMapping("/api/flinkSqlJobLibra")
public class FlinkSqlJobLibraController {

    private final FlinkSqlJobLibraService flinkSqlJobLibraService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('flinkSqlJobLibra:list')")
    public void exportFlinkSqlJobLibra(HttpServletResponse response, FlinkSqlJobLibraQueryCriteria criteria) throws IOException {
        flinkSqlJobLibraService.download(flinkSqlJobLibraService.queryAll(criteria), response);
    }

    @GetMapping
    @ApiOperation("查询自建平台sql作业")
    @PreAuthorize("@el.check('flinkSqlJobLibra:list')")
    public ResponseEntity<Object> queryFlinkSqlJobLibra(FlinkSqlJobLibraQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(flinkSqlJobLibraService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增自建平台sql作业")
    @ApiOperation("新增自建平台sql作业")
    @PreAuthorize("@el.check('flinkSqlJobLibra:add')")
    public ResponseEntity<Object> createFlinkSqlJobLibra(@Validated @RequestBody FlinkSqlJobLibra resources){
        return new ResponseEntity<>(flinkSqlJobLibraService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改自建平台sql作业")
    @ApiOperation("修改自建平台sql作业")
    @PreAuthorize("@el.check('flinkSqlJobLibra:edit')")
    public ResponseEntity<Object> updateFlinkSqlJobLibra(@Validated @RequestBody FlinkSqlJobLibra resources){
        flinkSqlJobLibraService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除自建平台sql作业")
    @ApiOperation("删除自建平台sql作业")
    @PreAuthorize("@el.check('flinkSqlJobLibra:del')")
    public ResponseEntity<Object> deleteFlinkSqlJobLibra(@RequestBody Long[] ids) {
        flinkSqlJobLibraService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
