package com.dewu.property.sql.pipelineage;

import com.alibaba.fastjson.JSON;
import com.dewu.exception.LineageException;
import com.dewu.flink.template.utils.JsonUtil;
import com.dewu.rule.domain.MetricsTaskOperatorBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.Set;

@Slf4j
@Component
public class LineageKafkaWriter {

	@Value("${pipeline.metrics.kafka.bootstrap_server}")
	private String ip;

	@Value("${pipeline.metrics.kafka.topic}")
	private String assetDelayDetailTopic;

	public void sendMessMonitorStatus(Set<AssetDelayMetrics> metrics) {
		Properties props = new Properties();
		props.put("bootstrap.servers", ip);
		props.put("acks", "all");
		props.put("retries", 3);
		props.put("max.in.flight.requests.per.connection", 1);
		props.put("batch.size", 16384);
		props.put("linger.ms", 1);
		props.put("buffer.memory", 33554432);
		props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
		props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
		Producer<String, String> producer = new KafkaProducer<>(props);
		metrics.forEach(metric -> {
			try {
				String message = JsonUtil.writeString(metric);
				producer.send(new ProducerRecord<>(assetDelayDetailTopic, metric.getTableName(), message), new Callback() {
					@Override
					public void onCompletion(RecordMetadata metadata, Exception exception) {
						if (exception != null) {
							throw new LineageException("asset delay update failed.", exception);
						}
					}
				});
			} catch (JsonProcessingException e) {
				log.error("fail to send metric:" + metric, e);
				throw new LineageException("fail to send metric:" + metric, e);
			}
		});
		producer.close();
	}

}
