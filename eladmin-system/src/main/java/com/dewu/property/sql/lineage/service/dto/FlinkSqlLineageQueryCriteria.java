/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.lineage.service.dto;

import lombok.Data;
import com.dewu.annotation.Query;

import java.util.Collection;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-07-14
**/
@Data
public class FlinkSqlLineageQueryCriteria{

    @Query(type = Query.Type.EQUAL)
    String jobName;

    @Query(type = Query.Type.EQUAL)
    String targetTable;

    @Query(type = Query.Type.EQUAL)
    String sourceTable;

    @Query(type = Query.Type.INNER_LIKE)
    String sourceDatabase;

    @Query(type = Query.Type.INNER_LIKE)
    String sourceDbType;


    @Query(type = Query.Type.INNER_LIKE)
    String sourceInstance;

    @Query(type = Query.Type.INNER_LIKE)
    String targetDatabase;

    @Query(type = Query.Type.INNER_LIKE)
    String targetInstance;

    @Query(type = Query.Type.INNER_LIKE)
    String targetDbType;

    @Query(type = Query.Type.EQUAL)
    String isDelete;

    @Query(type = Query.Type.IN)
    Collection<String> projectName;

}