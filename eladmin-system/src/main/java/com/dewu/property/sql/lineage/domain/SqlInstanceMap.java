package com.dewu.property.sql.lineage.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Entity
@DynamicInsert
@DynamicUpdate
@Data
@Table(name="flink_sql_instance_map")
public class SqlInstanceMap implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    @ApiModelProperty(value = "自增id")
    private Long id;

    @Column(name = "`instance_all`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "")
    private String instanceAll;

    @Column(name = "`instance_unique`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "")
    private String instanceUnique;

    @Column(name = "`instance_type`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "")
    private String instanceType;


    public void copy(SqlInstanceMap source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}

