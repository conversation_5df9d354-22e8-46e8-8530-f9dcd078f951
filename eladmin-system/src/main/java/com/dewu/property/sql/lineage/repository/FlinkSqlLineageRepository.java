/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.lineage.repository;

import com.dewu.property.sql.lineage.domain.FlinkSqlLineage;
import org.apache.ibatis.annotations.Delete;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-07-14
**/
public interface FlinkSqlLineageRepository extends JpaRepository<FlinkSqlLineage, Long>, JpaSpecificationExecutor<FlinkSqlLineage> {

    @Query(value = "" +
            "SELECT distinct source_table_id as tableId,source_table as physicsTable,source_database as `tableDatabase`,source_db_type as tableDbType,source_instance as tableInstance FROM `flink_sql_lineage` where (source_table LIKE ?1 or `source_original_instance` LIKE ?1 ) and is_delete=0 " +
            "union  " +
            "SELECT distinct target_table_id as tableId, target_table as physicsTable,target_database as `tableDatabase`,target_db_type as tableDbType,target_instance as tableInstance FROM `flink_sql_lineage` where (target_table LIKE ?1 or `target_original_instance` LIKE ?1 ) and is_delete=0 " +
            "order by physicsTable limit 50", nativeQuery = true)
    List<Map<String,Object>> likeDistinctByTableName(String tableName);

    @Query(value = "" +
            "SELECT distinct source_table_id as tableId,source_table as physicsTable,source_database as `tableDatabase`,source_db_type as tableDbType,source_instance as tableInstance FROM `flink_sql_lineage` where (source_table LIKE ?1 or `source_original_instance` LIKE ?1 ) and is_delete=0 and project_name in ('tech-data-dw2','dw-rt-2')" +
            "union  " +
            "SELECT distinct target_table_id as tableId, target_table as physicsTable,target_database as `tableDatabase`,target_db_type as tableDbType,target_instance as tableInstance FROM `flink_sql_lineage` where (target_table LIKE ?1 or `target_original_instance` LIKE ?1 ) and is_delete=0 and project_name in ('tech-data-dw2','dw-rt-2')" +
            "order by physicsTable limit 50", nativeQuery = true)
    List<Map<String,Object>> likeDistinctByTableNameForAssertLink(String tableName);


    @Deprecated
    @Query(value = "SELECT distinct target_table as tableName,target_database as `database`,target_db_type as dbType FROM `flink_sql_lineage` where target_table LIKE ?1 and is_delete=0", nativeQuery = true)
    List<Map> likeDistinctByTargetTable(String tableName);


    @Query(value = "SELECT distinct source_table_id as sourceTableId,source_table as sourceTable,source_view_table as sourceViewTable,source_database as  sourceDatabase," +
            "source_db_type as sourceDbType,source_instance as sourceInstance," +
            "target_table_id as targetTableId,target_table  as targetTable,target_view_table  as targetViewTable,target_database  as targetDatabase," +
            "target_db_type as targetDbType, target_instance as targetInstance," +
//            "job_name as  jobName,project_name projectName," +
            "'downstream' as levelRelationship FROM `flink_sql_lineage` where source_table = ?1 and source_database=?2 and source_instance=?3 and source_db_type=?4 and is_delete=0", nativeQuery = true)
    List<Map> querySqlLineBySourceTable(String tableName, String database, String tableInstance, String dbType);

    @Query(value = "SELECT distinct source_table_id as sourceTableId,source_table as sourceTable,source_view_table as sourceViewTable,source_database as  sourceDatabase," +
            "source_db_type as sourceDbType,source_instance as sourceInstance," +
            "target_table_id as targetTableId,target_table  as targetTable,target_view_table  as targetViewTable,target_database  as targetDatabase," +
            "target_db_type as targetDbType, target_instance as targetInstance," +
//            "job_name as  jobName,project_name projectName," +
            "'upstream' as levelRelationship FROM `flink_sql_lineage` where target_table = ?1 and target_database=?2 and target_instance=?3 and target_db_type=?4 and is_delete=0", nativeQuery = true)
    List<Map> querySqlLineByTargetTable(String tableName, String database,String tableInstance,  String dbType);


    //下面两个去重，上面两个不去重，可能会导致不一致，即一个sql脚本里面同一个物理表创建了两次，但是临时表名不一致，在下面会被去重，上面则不会被去重 要去重，核心是物理表非临时表
    // 还有一个情况，同一个表 他物理表，临时表都一样，但是归属作业不一样，这种要在血缘中重复展示吗》--- 展示

    //查询上游表数量和名称
    @Query(value = "SELECT distinct source_table as tableName,source_database as `database`,source_db_type as dbType FROM `flink_sql_lineage` " +
            "where target_table = ?1 and target_database=?2 and target_db_type=?3", nativeQuery = true)
    List<Map> queryUpStreamTableByTargetTable(String tableName, String database, String dbType);

    //查询下游表数量和名称
    @Query(value = "SELECT distinct target_table as tableName,target_database as `database`,target_db_type as dbType" +
            " FROM `flink_sql_lineage` where source_table = ?1 and source_database=?2 and source_db_type=?3", nativeQuery = true)
    List<Map> queryDownStreamTableBySourceTable(String tableName, String database, String dbType);





    //查询上游任务数量和任务名
    @Query(value = "SELECT distinct job_name FROM `flink_sql_lineage` where target_table = ?1 and target_database=?2 and target_instance=?3 and target_db_type=?4 and is_delete=0", nativeQuery = true)
    List<String> queryUpStreamJobByTargetTable(String tableName, String database, String tableInstance,String dbType);

    //查询下游任务数量和任务名
    @Query(value = "SELECT distinct job_name FROM `flink_sql_lineage` where source_table = ?1 and source_database=?2 and source_instance=?3 and source_db_type=?4 and is_delete=0", nativeQuery = true)
    List<String> queryDownStreamJobBySourceTable(String tableName, String database,String tableInstance, String dbType);


    @Delete("DELETE FROM flink_sql_lineage  WHERE job_name = ?1 and project_name=?2")
    void deleteByJobNameAndProjectName(String jobName, String projectName);

    @Query(value ="update flink_sql_lineage set is_delete=1 where  job_name = ?1",nativeQuery = true)
    @Modifying
    void updateSqlLineIsDelete(String jobName);

    @Query(value = "SELECT * FROM `flink_sql_lineage` where job_name = ?1 ", nativeQuery = true)
    List<FlinkSqlLineage> querySqlLineByJobName(String jobName);


//    @Query(value ="insert into flink_sql_lineage(source_table,source_view_table,source_database,source_db_type,target_table,target_view_table,target_database,target_db_type,job_name,creator,project_name," +
//            "source_instance,target_instance,targer_original_instance,source_original_instance)" +
//            "value(?1,?2,?3,?4,?5,?6,?7,?8,?9,?10,?11,?12,?13,?14,?15)" +
//            " on duplicate key update " +
//            "source_table=?1, source_view_table=?2, source_database=?3, source_db_type=?4, target_table=?5," +
//            "target_view_table=?6, target_database=?7, target_db_type=?8, job_name=?9, creator=?10, project_name=?11," +
//            "source_instance=?12,target_instance=?13, targer_original_instance=?14, source_original_instance=?15" +
//            "",nativeQuery = true)
//    @Modifying
//    void upsetSqlLine(String jobName);


}