package com.dewu.property.sql.pipelineage;

import com.dewu.property.sql.lineage.service.FlinkSqlLineageService;
import com.dewu.property.sql.lineage.service.dto.FlinkSqlLineageDto;
import com.dewu.property.sql.lineage.service.dto.FlinkSqlLineageQueryCriteria;
import com.dewu.utils.HbaseUtils;
import com.dewu.utils.sr.FlinkJobMetricsVisitor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;
import org.apache.flink.api.java.tuple.Tuple2;
import org.jgrapht.Graph;
import org.jgrapht.GraphPath;
import org.jgrapht.alg.shortestpath.AllDirectedPaths;
import org.jgrapht.alg.shortestpath.DijkstraShortestPath;
import org.jgrapht.graph.DefaultDirectedGraph;
import org.jgrapht.graph.DefaultEdge;
import org.jgrapht.traverse.DepthFirstIterator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PipelineLineageServiceImpl implements PipelineLineageService {

	private final FlinkSqlLineageService flinkSqlLineageService;

	private final FlinkJobMetricsVisitor flinkJobMetricsVisitor;

	public static HashMap<String, Node> inputCache = new HashMap<>();
	public static HashMap<String, Node> outputCache = new HashMap<>();
	static Map<TableNode, TableNode> tables = new HashMap<>();
	static Map<TableEdge, TableEdge> tableEdges = new HashMap<>();
	static Map<JobNode, JobNode> jobs = new HashMap<>();

	@Value("${pipeline.metrics.hbase.zookeeperQuorum}")
	private String zookeeperQuorum;
	@Value("${pipeline.metrics.hbase.username}")
	private String username;
	@Value("${pipeline.metrics.hbase.password}")
	private String password;
	@Value("${pipeline.metrics.hbase.table}")
	private String table;

	@Override
	public String calc(String table, String instance, String database, String txt) {
		FlinkSqlLineageQueryCriteria flinkSqlLineageQueryCriteria = new FlinkSqlLineageQueryCriteria();
		flinkSqlLineageQueryCriteria.setIsDelete("0");
		List<FlinkSqlLineageDto> flinkSqlLineageDtos = flinkSqlLineageService.queryAll(flinkSqlLineageQueryCriteria);
		TableNode tableNode = TableNode.of(table, instance, database, -1);
		flinkJobMetricsVisitor.getTableColumnsMetaData(txt);
		return testLineage(flinkSqlLineageDtos, tableNode);
	}

	public static TableNode getTableNode(TableNode node) {
		if (tables.containsKey(node)) {
			return tables.get(node);
		}
		tables.put(node, node);
		return node;
	}

	public static TableEdge getTableEdge(TableEdge edge) {
		if (tableEdges.containsKey(edge)) {
			return tableEdges.get(edge);
		}
		tableEdges.put(edge, edge);
		return edge;
	}


	public static JobNode getJobNode(JobNode node) {
		if (jobs.containsKey(node)) {
			return jobs.get(node);
		}
		jobs.put(node, node);
		return node;
	}

	public String testLineage(List<FlinkSqlLineageDto> flinkSqlLineageDtos, TableNode tableNode) {
		AtomicInteger idGenerator = new AtomicInteger();
		HbaseUtils hbaseUtils = new HbaseUtils(username, password, zookeeperQuorum);
		Graph<TableNode, TableEdge> g = new DefaultDirectedGraph<>(TableEdge.class);
		DijkstraShortestPath<TableNode, TableEdge> dijkstraAlg = new DijkstraShortestPath<>(g);
		AllDirectedPaths<TableNode, TableEdge> allDirectedPaths = new AllDirectedPaths<>(g);
		TableNode root = TableNode.of("lineage_root_vertex", "root", "root", idGenerator.getAndIncrement());
		TableNode leaf = TableNode.of("lineage_leaf_vertex", "leaf", "leaf", idGenerator.getAndIncrement());
		g.addVertex(root);
		g.addVertex(leaf);
		for (FlinkSqlLineageDto flinkSqlLineageDto : flinkSqlLineageDtos) {
			if (flinkSqlLineageDto.getProjectName().equals("tech-data-dw2") || flinkSqlLineageDto.getProjectName().equals("dw-rt-2")) {
				TableNode source = getTableNode(TableNode.of(flinkSqlLineageDto.getSourceTable(), flinkSqlLineageDto.getSourceDatabase(), flinkSqlLineageDto.getSourceInstance(), idGenerator.getAndIncrement()));
				TableNode target = getTableNode(TableNode.of(flinkSqlLineageDto.getTargetTable(), flinkSqlLineageDto.getTargetDatabase(), flinkSqlLineageDto.getTargetInstance(), idGenerator.getAndIncrement()));
				JobNode job = getJobNode(new JobNode(flinkSqlLineageDto.getJobName(), flinkSqlLineageDto.getProjectName()));
				TableEdge tableEdge = getTableEdge(new TableEdge(job, source, target));
				if (!g.containsVertex(source)) {
					g.addVertex(source);
				}
				if (!g.containsVertex(target)) {
					g.addVertex(target);
				}
				if (!source.equals(target)) {
					g.addEdge(source, target, tableEdge);
				}
			}
		}

		g.vertexSet().forEach(v -> {
			try {
				if (g.inDegreeOf(v) == 0 && !v.equals(root) && !v.equals(leaf)) {
					g.addEdge(root, v, new TableEdge(null, root, v));
				}
				if (g.outDegreeOf(v) == 0 && !v.equals(root) && !v.equals(leaf)) {
					g.addEdge(v, leaf, new TableEdge(null, v, leaf));
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		});
		TableNode.setTableNum(g.vertexSet().size());
		Set<TableEdge> removeEdges = new HashSet<>();
		markDepthAndBreakCycleFromRoot(g, root, removeEdges);
		removeEdges.forEach(tableEdge -> System.out.println("rm edge " + tableEdge.getSource().getTableName() + " -> " + tableEdge.getTarget().getTableName()));
		removeEdges.forEach(g::removeEdge);
		try {
			return buildGraph(tableNode, leaf, root, allDirectedPaths);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return "";
	}

	public String buildGraph(TableNode node, TableNode leaf, TableNode root, AllDirectedPaths<TableNode, TableEdge> allDirectedPaths) throws JsonProcessingException {
		ObjectMapper objectMapper = new ObjectMapper();
		List<GraphPath<TableNode, TableEdge>> leaves = allDirectedPaths.getAllPaths(node, leaf, true, 100);
		List<GraphPath<TableNode, TableEdge>> roots = allDirectedPaths.getAllPaths(root, node, true, 100);
		Map<Integer, Set<TableNode>> depthNodes = new HashMap<>();
		Map<Integer, AtomicInteger> depthCount = new HashMap<>();
		Map<TableNode, Tuple2<Integer, Integer>> graphNodes = new HashMap<>();
		Set<TableEdge> edges = new HashSet<>();
		collectVertexAndEdge(leaves, depthNodes, edges);
		collectVertexAndEdge(roots, depthNodes, edges);
//		int graphWidth = depthNodes.values().stream().max(Comparator.comparingInt(Set::size)).get().size() * 900;
////		int graphHeight = depthNodes.size() * 300;

		int graphWidth = depthNodes.size() * 900;
		int graphHeight = depthNodes.values().stream().max(Comparator.comparingInt(Set::size)).get().size() * 300;
		if (graphHeight * 3 > graphWidth) {
			graphWidth = graphHeight * 3;
		} else {
			graphHeight = graphWidth / 3;
		}
		int heightGap = graphHeight / depthNodes.size();

		Graph<TableNode, TableEdge> tmp = buildTmpGraph(leaves, roots, leaf, root);
		DepthFirstIterator<TableNode, TableEdge> iterator = new DepthFirstIterator<>(tmp);
		while (iterator.hasNext()) {
			TableNode next = iterator.next();
			if (!root.equals(next) && !leaf.equals(next)) {
				int index = depthCount.computeIfAbsent(next.getDepth(), k -> new AtomicInteger()).incrementAndGet();
//				graphNodes.put(next, new Tuple2<>(index * graphWidth / (depthNodes.get(next.getDepth()).size() + 1), next.getDepth() * heightGap + (index % 2) * heightGap / 4));
				graphNodes.put(next, new Tuple2<>((next.getDepth() - 2) * graphWidth / depthNodes.size(), (index - 2) * graphHeight / (depthNodes.get(next.getDepth()).size() + 1)));
			}
		}
		SimpleUIGraph simpleUIGraph = new SimpleUIGraph();
		simpleUIGraph.setSearchNode(node.getSimpleString());
		List<GraphNode> nodes = new ArrayList<>();
		List<GraphEdge> links = new ArrayList<>();
		graphNodes.forEach((k, v) -> {
			if (!root.equals(k) && !leaf.equals(k)) {
				GraphNode graphNode = new GraphNode();
				graphNode.setX(v.f0);
				graphNode.setY(v.f1);
				graphNode.setName(k.getSimpleString());
				graphNode.getValue().put("delay", "100");
				graphNode.getValue().put("table", k.getUniqString());
				nodes.add(graphNode);
			}
		});
		String ctime = new SimpleDateFormat("yyyy-MM-dd HH:mm").format(new Date()) + ":00";
		edges.forEach(edge -> {
			if (!root.equals(edge.getSource()) && !leaf.equals(edge.getTarget())) {
				GraphEdge graphEdge = new GraphEdge();
				graphEdge.setSource(edge.getSource().getSimpleString());
				graphEdge.setTarget(edge.getTarget().getSimpleString());
				graphEdge.getEdgeValue().put("job", edge.getJob().getUniqString());
				graphEdge.getEdgeValue().put("delay", String.valueOf(getDelay(edge.getJob().getJobName(), ctime)));
				links.add(graphEdge);
			}
		});
		simpleUIGraph.setLinks(links);
		simpleUIGraph.setNodes(nodes);
		return objectMapper.writeValueAsString(simpleUIGraph);
	}

	private Double getDelay(String jobName, String ctime) {
		List<Map<String, Object>> jobDelay = flinkJobMetricsVisitor.getJobDelay(jobName, ctime);
		if (jobDelay.size() > 0) {
			return jobDelay.stream().map(map -> Double.parseDouble(map.get("value").toString())).reduce(Math::max).get();
		}
		return 0.0;
	}

	public static Graph<TableNode, TableEdge> buildTmpGraph(List<GraphPath<TableNode, TableEdge>> leaves, List<GraphPath<TableNode, TableEdge>> roots, TableNode leaf, TableNode root) {
		Graph<TableNode, TableEdge> tmp = new DefaultDirectedGraph<>(TableEdge.class);
		Map<TableNode, TableNode> tmpNodes = new HashMap<>();
		List<GraphPath<TableNode, TableEdge>> allEdges = new ArrayList<>();
		allEdges.addAll(roots);
		allEdges.addAll(leaves);
		allEdges.forEach(path -> path.getEdgeList().forEach(edge -> {
			TableNode source = tmpNodes.computeIfAbsent(edge.getSource(), k -> edge.getSource().copyTmp());
			TableNode target = tmpNodes.computeIfAbsent(edge.getTarget(), k -> edge.getTarget().copyTmp());
			TableEdge tmpEdge = new TableEdge(edge.getJob(), source, target);
			tmp.addVertex(source);
			tmp.addVertex(target);
			tmp.addEdge(source, target, tmpEdge);
		}));
		markDepthAndBreakCycleFromRoot(tmp, root, new HashSet<>());
		return tmp;
	}

	private static void collectVertexAndEdge(List<GraphPath<TableNode, TableEdge>> roots, Map<Integer, Set<TableNode>> depthNodes, Set<TableEdge> edges) {
		roots.forEach(path -> path.getEdgeList().forEach(edge -> {
			depthNodes.computeIfAbsent(edge.getTarget().getDepth(), k -> new HashSet<>()).add(edge.getTarget());
			depthNodes.computeIfAbsent(edge.getSource().getDepth(), k -> new HashSet<>()).add(edge.getSource());
			edges.add(edge);
		}));
	}

	public static void markDepthAndBreakCycleFromRoot(Graph<TableNode, TableEdge> g, TableNode node, Set<TableEdge> removeEdges) {
		if (!node.isInit()) {
			node.initMemSet();
		}
		if (g.outDegreeOf(node) == 0) {
			return;
		}
		g.outgoingEdgesOf(node).forEach(e -> {
			TableNode target = e.getTarget();
			if (!target.isInit()) {
				target.initMemSet();
			}
			if (node.isInMemSet(target.id)) {
				removeEdges.add(e);
			} else {
				target.setMemSet(node.getMemSet());
				target.setMemSet(node.getId());
				if (target.getDepth() <= node.getDepth()) {
					target.setDepth(node.getDepth() + 1);
				}
				markDepthAndBreakCycleFromRoot(g, target, removeEdges);
			}
		});
	}

	public static void main(String[] args) {
//		new PipelineLineageService().calc();
//		Graph<String, DefaultEdge> g = new DirectedAcyclicGraph<>(DefaultEdge.class);
//
//		String google0 = new String("http://www.google0.com");
//		String google = new String("http://www.google.com");
//		String wikipedia = new String("http://www.wikipedia.org");
//		String jgrapht = new String("http://www.jgrapht.org");
//		String jgrapht1 = new String("http://www.jgrapht1.org");
//		String jgrapht2 = new String("http://www.jgrapht2.org");
//		String jgrapht3 = new String("http://www.jgrapht3.org");
//
//		// add the vertices
//		g.addVertex(google);
//		g.addVertex(wikipedia);
//		g.addVertex(jgrapht);
//		g.addVertex(jgrapht1);
//		g.addVertex(jgrapht2);
//		g.addVertex(jgrapht3);
//		g.addVertex(google0);
//
//		// add edges to create linking structure
//		g.addEdge(jgrapht, wikipedia);
//		g.addEdge(google0, google);
//		g.addEdge(google, jgrapht);
//		g.addEdge(google, wikipedia);
//		g.addEdge(wikipedia, jgrapht1);
//		g.addEdge(wikipedia, jgrapht2);
//		g.addEdge(jgrapht1, jgrapht3);
//
//		Node lineage = findLineage(g, new Node((google)));
//		System.out.println(lineage);
	}

	/**
	 * 寻找上下游血缘，并计算权重
	 *
	 * @param g    图
	 * @param node 定位节点
	 * @return node 定位节点
	 */
	public static Node findLineage(Graph<String, DefaultEdge> g, Node node) {
		node.getOutputs().addAll(g.outgoingEdgesOf(node.getName()).stream().map(g::getEdgeTarget).map(e -> findOutputLineage(g, new Node(e))).collect(Collectors.toList()));
		node.getInputs().addAll(g.incomingEdgesOf(node.getName()).stream().map(g::getEdgeSource).map(e -> findInputLineage(g, new Node(e))).collect(Collectors.toList()));
		node.calcWeight();
		return node;
	}

	/**
	 * 寻找输出节点，并计算权重
	 *
	 * @param g    图
	 * @param node 定位节点
	 * @return node 定位节点
	 */
	public static Node findOutputLineage(Graph<String, DefaultEdge> g, Node node) {
		if (outputCache.containsKey(node.getName())) {
			return node;
		}
		node.getOutputs().addAll(g.outgoingEdgesOf(node.getName()).stream().map(g::getEdgeTarget).map(e -> findOutputLineage(g, new Node(e))).collect(Collectors.toList()));
		outputCache.put(node.getName(), node);
		node.calcWeight();
		return node;
	}

	/**
	 * 寻找输入节点，并计算权重
	 *
	 * @param g    图
	 * @param node 定位节点
	 * @return node 定位节点
	 */
	public static Node findInputLineage(Graph<String, DefaultEdge> g, Node node) {
		if (inputCache.containsKey(node.getName())) {
			return node;
		}
		node.getInputs().addAll(g.incomingEdgesOf(node.getName()).stream().map(g::getEdgeSource).map(e -> findInputLineage(g, new Node(e))).collect(Collectors.toList()));
		inputCache.put(node.getName(), node);
		node.calcWeight();
		return node;
	}

	@Data
	public static class Node {
		String name; // 节点名
		int weight = 1; // 节点权重
		float score; // 稳定分
		long delay; // 延迟
		Set<Node> inputs = new HashSet<>(); // 上游节点
		Set<Node> outputs = new HashSet<>(); // 下游节点

		public Node(String name) {
			this.name = name;
		}

		@Override
		public boolean equals(Object o) {
			if (this == o) return true;
			if (o == null || getClass() != o.getClass()) return false;
			Node node = (Node) o;
			return Objects.equals(name, node.name);
		}

		@Override
		public int hashCode() {
			return Objects.hash(name);
		}

		public void calcWeight() {
			int inputWeight = inputs.stream().map(Node::getWeight).reduce(Integer::sum).orElse(1);
			int outputWeight = outputs.stream().map(Node::getWeight).reduce(Integer::sum).orElse(1);
			weight = Math.max(inputWeight, outputWeight);
		}
	}


	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class TableNode {
		String tableName;
		String database;
		String instance;
		int depth;
		int id;
		BitSet memSet;

		static int tableNum = 0;

		public static TableNode of(String tableName, String database, String instance, int id) {
			TableNode node = new TableNode();
			node.tableName = null == tableName ? "" : tableName;
			node.database = null == database ? "" : database;
			node.instance = null == instance ? "" : instance;
			node.id = id;
			return node;
		}

		public static void setTableNum(int num) {
			tableNum = num;
		}

		public void initMemSet() {
			memSet = new BitSet(tableNum);
		}

		public boolean isInMemSet(int id) {
			return memSet.get(id);
		}

		public void setMemSet(int id) {
			memSet.set(id);
		}

		public void setMemSet(BitSet bitSet) {
			memSet.or(bitSet);
		}

		public boolean isInit() {
			return memSet != null;
		}

		@Override
		public boolean equals(Object o) {
			if (this == o) return true;
			if (o == null || getClass() != o.getClass()) return false;
			TableNode tableNode = (TableNode) o;
			return Objects.equals(tableName, tableNode.tableName) && Objects.equals(database, tableNode.database) && Objects.equals(instance, tableNode.instance);
		}

		@Override
		public int hashCode() {
			return Objects.hash(tableName, database, instance);
		}

		public TableNode copyTmp() {
			return TableNode.of(tableName, database, instance, id);
		}

		public String getUniqString() {
			return tableName + "(" + instance + "/" + database + ")";
		}

		public String getSimpleString() {
			return id + ":" + tableName;
		}
	}


	@Data
	@EqualsAndHashCode
	@AllArgsConstructor
	public static class JobNode {
		String jobName;
		String projectName;

		public String getUniqString() {
			return projectName + "/" + jobName;
		}
	}


	@Data
	@EqualsAndHashCode
	public static class TableEdge {
		JobNode job;
		TableNode source;
		TableNode target;
		Double delay;

		public TableEdge(JobNode job, TableNode source, TableNode target) {
			this.job = job;
			this.source = source;
			this.target = target;
		}
	}


	@Data
	@EqualsAndHashCode
	@AllArgsConstructor
	@NoArgsConstructor
	public static class GraphNode {
		String name;
		Map<String, String> value = new HashMap<>();
		int x;
		int y;
	}

	@Data
	@EqualsAndHashCode
	@AllArgsConstructor
	@NoArgsConstructor
	public static class GraphEdge {
		String source;
		String target;
		Map<String, String> EdgeValue = new HashMap<>();
	}

	@Data
	@EqualsAndHashCode
	@AllArgsConstructor
	@NoArgsConstructor
	public static class SimpleUIGraph {
		String searchNode;
		List<GraphNode> nodes;
		List<GraphEdge> links;
	}
}