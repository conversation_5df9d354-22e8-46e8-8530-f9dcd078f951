/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.sql.lineage.rest;

import com.dewu.annotation.AnonymousAccess;
import com.dewu.annotation.Log;
import com.dewu.flink.template.job.authorization.BotAuthorizationAnnotation;
import com.dewu.property.sql.lineage.domain.FlinkSqlLineage;
import com.dewu.property.sql.lineage.service.FlinkSqlLineageService;
import com.dewu.property.sql.lineage.service.dto.FlinkSqlLineageQueryCriteria;
import com.dewu.property.sql.lineage.service.dto.SqlLineageExtDto;
import com.dewu.property.sql.lineage.service.dto.TableLineageDto;
import com.dewu.property.sql.pipelineage.LineageGraphManager;
import com.dewu.property.sql.pipelineage.LineageUtils;
import com.dewu.property.sql.pipelineage.PipelineLineageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date 2023-07-14
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "数据血缘管理")
@RequestMapping("/api/flink/sql/lineage")
public class FlinkSqlLineageController {

	private final LineageGraphManager lineageGraphManager;

	private final FlinkSqlLineageService flinkSqlLineageService;

	private final PipelineLineageService pipelineLineageService;

	@GetMapping(value = "/search")
	@Log("血缘表模糊查询接口")
	@ApiOperation("血缘表模糊查询接口")
	@AnonymousAccess
	@BotAuthorizationAnnotation(isLogin = true)
	public ResponseEntity<Object> searchFlinkSqlLineage(String tableName) {
		try {
			List<TableLineageDto> responses = flinkSqlLineageService.likeDistinctByTableName("%" + tableName + "%");
			Map<String, Object> map = new HashMap();
			map.put("code", "200");
			map.put("data", responses);
			return new ResponseEntity<>(map, HttpStatus.OK);
		} catch (Exception e) {
			log.error("", e);
			Map<String, Object> map = new HashMap();
			map.put("code", "500");
			map.put("data", "拼命加载中");
			return new ResponseEntity<>(map, HttpStatus.OK);
		}

	}

	@PostMapping(value = "/assetsStatusPersist")
	@AnonymousAccess
	@BotAuthorizationAnnotation(isLogin = false)
	public Object assetsPersist(@RequestHeader HttpHeaders headers) {
		try {
			Map<String, Object> returnMap = new HashMap<>();
			String ts = headers.getFirst("X-DJob-Schedule-Timestamp");
			log.info("Meet X-DJob-Schedule-Timestamp[{}] ", ts);
			if (StringUtils.isEmpty(ts)) {
				throw new RuntimeException("Djob ts 获取为空。无法执行assets定时任务。");
			}
			// flush 5 分钟前的assets状态
			String ctimeBucket = LineageUtils.getTimeBucket(Long.parseLong(ts) - 600);
			lineageGraphManager.reCalcDelayForGraph(ctimeBucket);
			log.info("Finish assets[{}] status persist. X-DJob-Schedule-Timestamp[{}] ", ctimeBucket, ts);
			returnMap.put("status", "200");
			returnMap.put("message", "OK");
			return returnMap;
		} catch (Exception e) {
			log.error("Failed assets status persist. ", e);
			throw new RuntimeException("Failed assets status persist. ", e);
		}
	}


	@GetMapping(value = "/assetsPipeline")
	@AnonymousAccess
	@BotAuthorizationAnnotation(isLogin = false)
	public Object assetsPipeline(@RequestParam String tableName,
	                             @RequestParam String db,
	                             @RequestParam String instance,
	                             @RequestParam String ctime) {
		try {
			LineageGraphManager.TableNode tableNode = LineageGraphManager.TableNode.of(tableName, db, instance,0);
			return lineageGraphManager.fetchGraph(tableNode, ctime);
		} catch (Exception e) {
			log.error("Failed assets status persist. ", e);
			throw new RuntimeException("Failed assets status persist. ", e);
		}
	}

	@PostMapping(value = "/assetsStatusPersistHis/{ts}")
	@AnonymousAccess
	@BotAuthorizationAnnotation(isLogin = false)
	public Object assetsPersistHis(@RequestHeader HttpHeaders headers, @PathVariable String ts) {
		try {
			Map<String, Object> returnMap = new HashMap<>();
			log.info("Meet his re-calc from time[{}] ", ts);
			if (StringUtils.isEmpty(ts)) {
				throw new RuntimeException("re-calc ts。无法执行assets定时任务。");
			}
			long from = Long.parseLong(ts);
			long curr = System.currentTimeMillis() / 1000;
			if (from > curr) {
				throw new RuntimeException("re-calc ts早于当前时间。无法执行assets定时任务。");
			}
			if (curr - from > 86400 * 8) {
				throw new RuntimeException("re-calc ts过小，仅支持近7天刷数。无法执行assets定时任务。");
			}
			for (long t = from; t < curr; t = t + 300) {
				// flush 5 分钟前的assets状态
				String ctimeBucket = LineageUtils.getTimeBucket(t - 300);
				lineageGraphManager.reCalcDelayForGraph(ctimeBucket);
				log.info("Finish assets[{}] status persist from [{}].", ctimeBucket, ts);
			}
			returnMap.put("status", "200");
			returnMap.put("message", "OK");
			return returnMap;
		} catch (Exception e) {
			log.error("Failed assets status persist. ", e);
			throw new RuntimeException("Failed assets status persist. ", e);
		}
	}

	@PostMapping(value = "/assetsStatusPersistHisRange/{startTs}/{endTs}")
	@AnonymousAccess
	@BotAuthorizationAnnotation(isLogin = false)
	public Object assetsPersistHisRange(@RequestHeader HttpHeaders headers, @PathVariable String startTs, @PathVariable String endTs) {
		try {
			Map<String, Object> returnMap = new HashMap<>();
			log.info("Meet his re-calc from time[{}] ", startTs);
			if (StringUtils.isEmpty(startTs)) {
				throw new RuntimeException("re-calc ts。无法执行assets定时任务。");
			}
			long from = Long.parseLong(startTs);
			long end = Long.parseLong(endTs);
			if (end == 0) {
				end = System.currentTimeMillis() / 1000;
			}
			if (from > end) {
				throw new RuntimeException("re-calc ts早于当前时间。无法执行assets定时任务。");
			}
			if (end - from > 86400 * 8) {
				throw new RuntimeException("re-calc ts过小，仅支持7天范围刷数。无法执行assets定时任务。");
			}
			ExecutorService executorService = Executors.newFixedThreadPool(5);
			List<CompletableFuture<Boolean>> futures = new ArrayList<>();
			for (long t = from; t < end; t = t + 300) {
				final long tmpts = t;
				CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
					// flush 5 分钟前的assets状态
					String ctimeBucket = LineageUtils.getTimeBucket(tmpts - 300);
					lineageGraphManager.reCalcDelayForGraph(ctimeBucket);
					log.info("Finish assets[{}] status persist from [{}].", ctimeBucket, startTs);
					return true;
				}, executorService);
				futures.add(future);
			}

			futures.forEach(f -> f.whenComplete((o, throwable) -> {
				if (Objects.nonNull(throwable)) {
					log.error("Failed assets status persist for {}", startTs, throwable);
				}
			}));
			executorService.shutdown();
			returnMap.put("status", "200");
			returnMap.put("message", "OK");
			return returnMap;
		} catch (Exception e) {
			log.error("Failed assets status persist. ", e);
			throw new RuntimeException("Failed assets status persist. ", e);
		}
	}

	@GetMapping(value = "/like")
	@Log("血缘表模糊查询接口")
	@ApiOperation("血缘表模糊查询接口")
	@AnonymousAccess
	@BotAuthorizationAnnotation(isLogin = true)
	public ResponseEntity<Object> likeFlinkSqlLineage(String tableName) {
		try {
			List<TableLineageDto> responses;
			if (StringUtils.isBlank(tableName)) {
				responses = flinkSqlLineageService.likeDistinctByTableName("%order%");
			} else {
				responses = flinkSqlLineageService.likeDistinctByTableName("%" + tableName + "%");
			}
			Map<String, Object> map = new HashMap();
			map.put("code", "200");
			map.put("data", responses);
			return new ResponseEntity<>(map, HttpStatus.OK);
		} catch (Exception e) {
			log.error("", e);
			Map<String, Object> map = new HashMap();
			map.put("code", "500");
			map.put("data", "拼命加载中");
			return new ResponseEntity<>(map, HttpStatus.OK);
		}

	}

	/**
	 * 功能描述
	 * 得到下游表信息
	 * SELECT *  FROM `flink_sql_lineage` where source_table = '%name%' and  source_database='' and source_db_type='rds/jdbc kafka/upsert-kafka'
	 * <p>
	 * 得到上游表信息
	 * SELECT *  FROM `flink_sql_lineage` where target_table = '%name%' and target_database='' and target_db_type=''
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 * @date 2023/7/14
	 */
	@GetMapping(value = "/relationship")
	@Log("血缘表关系查询接口")
	@ApiOperation("血缘表关系查询接口")
	@AnonymousAccess
	@BotAuthorizationAnnotation(isLogin = true)
	public ResponseEntity<Object> queryFlinkSqlLineage(String physicsTable, String tableDatabase, String tableDbType, String tableInstance, String levelRelationship) {
		try {
			Map<String, Object> map = new HashMap();
			map.put("code", "200");
			List<SqlLineageExtDto> responses;
			if ("upstream".equalsIgnoreCase(levelRelationship)) {
				responses = flinkSqlLineageService.querySqlLineByTargetTable(physicsTable, tableDatabase, tableInstance, tableDbType);
			} else if ("downstream".equalsIgnoreCase(levelRelationship)) {
				responses = flinkSqlLineageService.querySqlLineBySourceTable(physicsTable, tableDatabase, tableInstance, tableDbType);
			} else if (StringUtils.isBlank(levelRelationship)) {
				responses = flinkSqlLineageService.querySqlLine(physicsTable, tableDatabase, tableInstance, tableDbType);
			} else {
				map.put("data", "levelRelationship参数非法");
				return new ResponseEntity<>(map, HttpStatus.OK);
			}
			map.put("data", responses);
			return new ResponseEntity<>(map, HttpStatus.OK);
		} catch (Exception e) {
			log.error("", e);
			Map<String, Object> map = new HashMap();
			map.put("code", "500");
			map.put("data", "拼命加载中");
			return new ResponseEntity<>(map, HttpStatus.OK);
		}
	}

	@GetMapping(value = "/details")
	@Log("血缘表详情接口")
	@ApiOperation("血缘表详情接口")
	@AnonymousAccess
	@BotAuthorizationAnnotation(isLogin = true)
	public ResponseEntity<Object> queryTabledetails(String physicsTable, String tableDatabase, String tableDbType, String tableInstance) {
		try {
			List<String> upstreamJobName = flinkSqlLineageService.queryUpStreamJobByTargetTable(physicsTable, tableDatabase, tableInstance, tableDbType);
			List<String> downstreamJobName = flinkSqlLineageService.queryDownStreamJobBySourceTable(physicsTable, tableDatabase, tableInstance, tableDbType);
			List<SqlLineageExtDto> upstreamTableName = flinkSqlLineageService.querySqlLineByTargetTable(physicsTable, tableDatabase, tableInstance, tableDbType);
			List<SqlLineageExtDto> downstreamTableName = flinkSqlLineageService.querySqlLineBySourceTable(physicsTable, tableDatabase, tableInstance, tableDbType);

			Map<String, Object> detailsMap = new HashMap<>();
			detailsMap.put("upstreamJobNum", upstreamJobName.size());
			detailsMap.put("downstreamJobNum", downstreamJobName.size());
			detailsMap.put("upstreamJobNames", upstreamJobName);
			detailsMap.put("downstreamJobNames", downstreamJobName);
			detailsMap.put("upstreamTableNum", upstreamTableName.size());
			detailsMap.put("downstreamTableNum", downstreamTableName.size());

			Map<String, Object> map = new HashMap();
			map.put("code", "200");
			map.put("data", detailsMap);
			return new ResponseEntity<>(map, HttpStatus.OK);
		} catch (Exception e) {
			Map<String, Object> map = new HashMap();
			map.put("code", "500");
			map.put("data", "拼命加载中");
			return new ResponseEntity<>(map, HttpStatus.OK);
		}
	}

	@GetMapping(value = "/job/relationship")
	@Log("血缘表关系查询接口")
	@ApiOperation("血缘表关系查询接口")
	@AnonymousAccess
	@BotAuthorizationAnnotation(isLogin = true)
	public ResponseEntity<Object> queryFlinkJobLineage(String jobName, String levelRelationship) {
		try {
			Map<String, Object> map = new HashMap();
			map.put("code", "200");
			List<FlinkSqlLineage> responses;
			if ("upstream".equalsIgnoreCase(levelRelationship)) {
				responses = flinkSqlLineageService.queryJobLineByTargetJobName(jobName);
			} else if ("downstream".equalsIgnoreCase(levelRelationship)) {
				responses = flinkSqlLineageService.queryJobLineBySourceJobName(jobName);
			} else if (StringUtils.isBlank(levelRelationship)) {
				responses = flinkSqlLineageService.queryJobLine(jobName);
			} else {
				map.put("data", "levelRelationship参数非法");
				return new ResponseEntity<>(map, HttpStatus.OK);
			}
			map.put("data", responses);
			return new ResponseEntity<>(map, HttpStatus.OK);
		} catch (Exception e) {
			log.error("", e);
			Map<String, Object> map = new HashMap();
			map.put("code", "500");
			map.put("data", "拼命加载中");
			return new ResponseEntity<>(map, HttpStatus.OK);
		}
	}


	@PutMapping(value = "/pipeline")
	@Log("计算链路数据血缘")
	@AnonymousAccess
	@BotAuthorizationAnnotation(isLogin = false)
	public ResponseEntity<String> updateFlinkPipelineLineage(String table, String instance, String database, String txt) {
		return new ResponseEntity<>(pipelineLineageService.calc(table, instance, database, txt), HttpStatus.OK);
	}

	@GetMapping
	@Log("查询数据血缘")
	@ApiOperation("查询数据血缘")
	@PreAuthorize("@el.check('flinkSqlLineage:list')")
	public ResponseEntity<Object> queryFlinkSqlLineage(FlinkSqlLineageQueryCriteria criteria, Pageable pageable) {
		criteria.setIsDelete("0");
		return new ResponseEntity<>(flinkSqlLineageService.queryAll(criteria, pageable), HttpStatus.OK);
	}

	@PostMapping
	@Log("新增数据血缘")
	@ApiOperation("新增数据血缘")
	@PreAuthorize("@el.check('flinkSqlLineage:add')")
	public ResponseEntity<Object> createFlinkSqlLineage(@Validated @RequestBody FlinkSqlLineage resources) {
		return new ResponseEntity<>(flinkSqlLineageService.create(resources), HttpStatus.CREATED);
	}

	@PutMapping
	@Log("修改数据血缘")
	@ApiOperation("修改数据血缘")
	@PreAuthorize("@el.check('flinkSqlLineage:edit')")
	public ResponseEntity<Object> updateFlinkSqlLineage(@Validated @RequestBody FlinkSqlLineage resources) {
		flinkSqlLineageService.update(resources);
		return new ResponseEntity<>(HttpStatus.NO_CONTENT);
	}

	@DeleteMapping
	@Log("删除数据血缘")
	@ApiOperation("删除数据血缘")
	@PreAuthorize("@el.check('flinkSqlLineage:del')")
	public ResponseEntity<Object> deleteFlinkSqlLineage(@RequestBody Long[] ids) {
		flinkSqlLineageService.deleteAll(ids);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
