package com.dewu.property.sql.pipelineage;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.dewu.property.sql.pipelineage.LineageUtils.buildJobInfoKey;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2024/7/3
 */
@Service
@Data
@Slf4j
public class JobInfoHbaseVisitor {
	private final JobStatusMetricsService jobStatusMetricsService;

	public List<Tuple2<String, Double>> getDelay(Set<String> jobs, String ctime) {
		List<Get> jobGets = jobs.stream().map(job -> buildJobInfoKey(job, ctime)).map(jobName -> new Get(jobName.getBytes())).collect(Collectors.toList());
		Result[] results = jobStatusMetricsService.batchGet(jobGets);
		List<Tuple2<String, Double>> tmp = new ArrayList<>();
		for (Result result : results) {
			byte[] delay;
			byte[] job;
			try {
				job = result.getValue("cf".getBytes(), "jobName".getBytes());
				delay = result.getValue("cf".getBytes(), "delay".getBytes());
				if (job != null && delay != null) {
//					log.info("LGM : found record [{},{}]",Bytes.toString(job), Bytes.toString(delay));
					tmp.add(new Tuple2<>(Bytes.toString(job), Double.parseDouble(Bytes.toString(delay))));
				}
			} catch (Exception e) {
				byte[] bytes = result.getValue("cf".getBytes(), "jobName".getBytes());
				if (bytes == null) {
					log.error("LGM : found empty record.", e);
				} else {
					log.error("LGM : Get {} err.", Bytes.toString(bytes), e);
				}
			}
		}
		log.info("LGM : Get job[{}] delay.", tmp.size());
		return tmp;
	}

	public Double getDelay(String job, String ctime) {
		String key = buildJobInfoKey(job, ctime);
		Get get = new Get(key.getBytes(StandardCharsets.UTF_8));
		try {
			String delay = Bytes.toString(jobStatusMetricsService.singleGet(get).getValue("cf".getBytes(StandardCharsets.UTF_8), "delay".getBytes(StandardCharsets.UTF_8)));
//			log.info("LGM : Get {} delay {}.", key, delay);
			return Double.parseDouble(delay);
		} catch (Exception e) {
			log.error("LGM : Get {} err.", key, e);
			return 0.0;
		}
	}


}
