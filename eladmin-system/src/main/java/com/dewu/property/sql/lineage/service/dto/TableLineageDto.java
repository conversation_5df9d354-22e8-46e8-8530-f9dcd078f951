/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.lineage.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-07-14
**/
@Data
public class TableLineageDto implements Serializable {


    /** 来源物理表 */
    private String tableId;

    private String physicsTable;

    /** 来源数据库 */
    private String tableDatabase;

    private String tableInstance;

    /** 来源数据库类型 */
    private String tableDbType;


}