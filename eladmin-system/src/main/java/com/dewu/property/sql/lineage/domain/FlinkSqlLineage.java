/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.lineage.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-07-14
**/
@Entity
@DynamicInsert
@DynamicUpdate
@Data
@Table(name="flink_sql_lineage")
public class FlinkSqlLineage implements Serializable {

    public FlinkSqlLineage(String sourceViewTable, String targetViewTable) {
        this.sourceViewTable = sourceViewTable;
        this.targetViewTable = targetViewTable;
    }

    public FlinkSqlLineage() {
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    @ApiModelProperty(value = "自增id")
    private Long id;

    @Column(name = "`source_table_id`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "来源表ID")
    private Long sourceTableId;

    @Column(name = "`source_table`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "来源物理表")
    private String sourceTable;

    @Column(name = "`source_view_table`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "来源视图表")
    private String sourceViewTable;

    @Column(name = "`source_database`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "来源数据库")
    private String sourceDatabase;

    @Column(name = "`source_db_type`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "来源数据库类型")
    private String sourceDbType;

    @Column(name = "`source_instance`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "来源实例")
    private String sourceInstance;

    @Column(name = "`source_original_instance`",nullable = false)
    @ApiModelProperty(value = "来源目标实例")
    private String sourceOriginalInstance;

    @Column(name = "`target_table_id`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "模板表ID")
    private Long targetTableId;

    @Column(name = "`target_table`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "目标表")
    private String targetTable;

    @Column(name = "`target_view_table`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "目标视图表")
    private String targetViewTable;

    @Column(name = "`target_database`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "目标数据库")
    private String targetDatabase;

    @Column(name = "`target_instance`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "目标实例")
    private String targetInstance;

    @Column(name = "`target_original_instance`",nullable = false)
    @ApiModelProperty(value = "目标原始实例")
    private String targetOriginalInstance;

    @Column(name = "`target_db_type`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "目标数据库类型")
    private String targetDbType;

    @Column(name = "`job_name`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "作业名称")
    private String jobName;

    @Column(name = "`project_name`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "空间名称")
    private String projectName;

    @Column(name = "`create_time`",nullable = false)
//    @NotNull
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @Column(name = "`modify_time`",nullable = false)
//    @NotNull
    @ApiModelProperty(value = "修改时间")
    private Timestamp modifyTime;

    @Column(name = "`creator`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "作业归属人")
    private String creator;

    @Column(name = "`is_delete`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "是否删除")
    private boolean isDelete;

    public void copy(FlinkSqlLineage source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
