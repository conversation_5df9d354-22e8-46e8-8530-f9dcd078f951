/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.instance.map.service.impl;

import com.dewu.property.sql.instance.map.domain.FlinkSqlInstanceMap;
import com.dewu.utils.ValidationUtil;
import com.dewu.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.dewu.property.sql.instance.map.repository.FlinkSqlInstanceMapRepository;
import com.dewu.property.sql.instance.map.service.FlinkSqlInstanceMapService;
import com.dewu.property.sql.instance.map.service.dto.FlinkSqlInstanceMapDto;
import com.dewu.property.sql.instance.map.service.dto.FlinkSqlInstanceMapQueryCriteria;
import com.dewu.property.sql.instance.map.service.mapstruct.FlinkSqlInstanceMapMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.dewu.utils.PageUtil;
import com.dewu.utils.QueryHelp;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-08-04
**/
@Service
@RequiredArgsConstructor
public class FlinkSqlInstanceMapServiceImpl implements FlinkSqlInstanceMapService {

    private final FlinkSqlInstanceMapRepository flinkSqlInstanceMapRepository;
    private final FlinkSqlInstanceMapMapper flinkSqlInstanceMapMapper;

    @Override
    public Map<String,Object> queryAll(FlinkSqlInstanceMapQueryCriteria criteria, Pageable pageable){
        Page<FlinkSqlInstanceMap> page = flinkSqlInstanceMapRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(flinkSqlInstanceMapMapper::toDto));
    }

    @Override
    public List<FlinkSqlInstanceMapDto> queryAll(FlinkSqlInstanceMapQueryCriteria criteria){
        return flinkSqlInstanceMapMapper.toDto(flinkSqlInstanceMapRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public FlinkSqlInstanceMapDto findById(Long id) {
        FlinkSqlInstanceMap flinkSqlInstanceMap = flinkSqlInstanceMapRepository.findById(id).orElseGet(FlinkSqlInstanceMap::new);
        ValidationUtil.isNull(flinkSqlInstanceMap.getId(),"FlinkSqlInstanceMap","id",id);
        return flinkSqlInstanceMapMapper.toDto(flinkSqlInstanceMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FlinkSqlInstanceMapDto create(FlinkSqlInstanceMap resources) {
        return flinkSqlInstanceMapMapper.toDto(flinkSqlInstanceMapRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(FlinkSqlInstanceMap resources) {
        FlinkSqlInstanceMap flinkSqlInstanceMap = flinkSqlInstanceMapRepository.findById(resources.getId()).orElseGet(FlinkSqlInstanceMap::new);
        ValidationUtil.isNull( flinkSqlInstanceMap.getId(),"FlinkSqlInstanceMap","id",resources.getId());
        flinkSqlInstanceMap.copy(resources);
        flinkSqlInstanceMapRepository.save(flinkSqlInstanceMap);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            flinkSqlInstanceMapRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<FlinkSqlInstanceMapDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (FlinkSqlInstanceMapDto flinkSqlInstanceMap : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("实例地址集合", flinkSqlInstanceMap.getInstanceAll());
            map.put("实例地址唯一编码", flinkSqlInstanceMap.getInstanceUnique());
            map.put("实例类型", flinkSqlInstanceMap.getInstanceType());
            map.put("创建时间", flinkSqlInstanceMap.getCreateTime());
            map.put("修改时间", flinkSqlInstanceMap.getModifyTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}