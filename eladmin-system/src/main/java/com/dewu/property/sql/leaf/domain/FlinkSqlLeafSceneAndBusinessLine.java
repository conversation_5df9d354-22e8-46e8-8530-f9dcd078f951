/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.leaf.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
import javax.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-03-12
**/
@Entity
@Data
@Table(name="flink_sql_leaf_scene_and_business_line")
public class FlinkSqlLeafSceneAndBusinessLine implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    @ApiModelProperty(value = "自增id")
    private Long id;

    @Column(name = "`job_name`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "作业名称")
    private String jobName;

    @Column(name = "`business_line`",nullable = false)
    @ApiModelProperty(value = "业务线")
    private String businessLine;

    @Column(name = "`scene`",nullable = false)
    @ApiModelProperty(value = "场景")
    private String scene;

    @Column(name = "`create_time`",nullable = false)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @Column(name = "`modify_time`",nullable = false)
    @ApiModelProperty(value = "修改时间")
    private Timestamp modifyTime;

    @Column(name = "`creator`",nullable = false)
    @ApiModelProperty(value = "作业归属人")
    private String creator;

    @Column(name = "`project_name`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "空间名称")
    private String projectName;

    @Column(name = "`is_delete`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @Column(name = "`is_grc`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "是否合规，0无效，1不合规，2 合规")
    private Integer isGrc;

    public void copy(FlinkSqlLeafSceneAndBusinessLine source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
