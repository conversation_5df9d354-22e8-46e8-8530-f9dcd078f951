/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.sql.job.repository;

import com.dewu.property.sql.job.domain.FlinkSqlJobLibra;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2023-07-17
 **/
public interface FlinkSqlJobLibraRepository extends JpaRepository<FlinkSqlJobLibra, Long>, JpaSpecificationExecutor<FlinkSqlJobLibra> {

    @Query(value = "update flink_sql_job_libra set source_table_num = ?3,target_table_num=?4,parser_status=?5,sql_content=?6 where task_name=?1 and project_name=?2", nativeQuery = true)
    @Modifying
    void updateParserStatus(String taskName, String projectName, Integer sourceTableNum, Integer targetTableNum, String parserStatus,String sqlContent);


    @Query(value = "SELECT * FROM flink_sql_job_libra  " +
            "WHERE parser_status = ?1 and status <> 3", nativeQuery = true)
    List<FlinkSqlJobLibra> queryOfflineJobLineage(String parserStatus);


    @Query(value = "SELECT * FROM flink_sql_job_libra  " +
            "WHERE task_name in(:jobNames) ", nativeQuery = true)
    List<FlinkSqlJobLibra> queryControlJob(@Param("jobNames") List<String> jobNames);

}