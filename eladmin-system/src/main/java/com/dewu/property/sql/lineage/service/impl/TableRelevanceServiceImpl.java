/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.sql.lineage.service.impl;

import com.dewu.property.sql.lineage.domain.TableRelevance;
import com.dewu.property.sql.lineage.repository.TableRelevanceRepository;
import com.dewu.property.sql.lineage.service.TableRelevanceService;
import com.dewu.property.sql.lineage.service.dto.TableRelevanceDto;
import com.dewu.property.sql.lineage.service.dto.TableRelevanceQueryCriteria;
import com.dewu.property.sql.lineage.service.mapstruct.TableRelevanceMapper;
import com.dewu.utils.PageUtil;
import com.dewu.utils.QueryHelp;
import com.dewu.utils.ValidationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-07-14
 **/
@Service
@RequiredArgsConstructor
public class TableRelevanceServiceImpl implements TableRelevanceService {

    private final TableRelevanceRepository tableRelevanceRepository;
    private final TableRelevanceMapper tableRelevanceMapper;

    @Override
    public Map<String, Object> queryAll(TableRelevanceQueryCriteria criteria, Pageable pageable) {
        Page<TableRelevance> page = tableRelevanceRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(tableRelevanceMapper::toDto));
    }

    @Override
    public List<TableRelevanceDto> queryAll(TableRelevanceQueryCriteria criteria) {
        return tableRelevanceMapper.toDto(tableRelevanceRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public TableRelevanceDto findById(Long id) {
        TableRelevance flinkSqlLineage = tableRelevanceRepository.findById(id).orElseGet(TableRelevance::new);
        ValidationUtil.isNull(flinkSqlLineage.getId(), "FlinkSqlLineage", "id", id);
        return tableRelevanceMapper.toDto(flinkSqlLineage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TableRelevanceDto create(TableRelevance resources) {
        return tableRelevanceMapper.toDto(tableRelevanceRepository.save(resources));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TableRelevance resources) {
        TableRelevance flinkSqlLineage = tableRelevanceRepository.findById(resources.getId()).orElseGet(TableRelevance::new);
        ValidationUtil.isNull(flinkSqlLineage.getId(), "FlinkSqlLineage", "id", resources.getId());
        flinkSqlLineage.copy(resources);
        tableRelevanceRepository.save(flinkSqlLineage);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            tableRelevanceRepository.deleteById(id);
        }
    }

}