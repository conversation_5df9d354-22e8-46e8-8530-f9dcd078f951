package com.dewu.property.sql.pipelineage;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2024/7/8
 */
public class LineageUtils {

	public static String getTimeBucket(String currMin) {
		if (currMin.length() > 16) {
			currMin = currMin.substring(0, 16);
		}
		int flag = Integer.parseInt(currMin.substring(currMin.length() - 1));
		if (flag < 5 && flag >= 0) {
			currMin = currMin.substring(0, currMin.length() - 1) + "0";
		} else {
			currMin = currMin.substring(0, currMin.length() - 1) + "5";
		}
		return currMin;
	}

	public static String getTimeBucket(long timestamp) {
		return getTimeBucket(getCurrentMinute(timestamp));
	}

	public static String buildJobInfoKey(String job, String ctime) {
		return job + "_" + ctime;
	}

	/**
	 * 获取当前日期和时间的分钟级别描述,格式为"yyyy-MM-dd HH:mm"。
	 * @return 当前日期和时间的字符串表示，格式为"yyyy-MM-dd HH:mm"。
	 */
	public static String getCurrentMinute(long timestamp) {
		Date date = new Date(timestamp * 1000);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
		sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
		return  sdf.format(date);
	}

	/**
	 * 获取当前日期和时间的分钟级别描述,格式为"yyyy-MM-dd HH:mm"。
	 * @return 当前日期和时间的字符串表示，格式为"yyyy-MM-dd HH:mm"。
	 */
	public static String getCurrentMinute() {
		return new SimpleDateFormat("yyyy-MM-dd HH:mm").format(new Date());
	}

	public static String getCurrentHour() {
		return new SimpleDateFormat("yyyy-MM-dd HH").format(new Date());
	}

}
