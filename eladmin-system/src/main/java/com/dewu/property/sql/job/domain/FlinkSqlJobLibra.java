/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.job.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
import javax.validation.constraints.*;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-07-17
**/
@Entity
@Data
@Table(name="flink_sql_job_libra")
public class FlinkSqlJobLibra implements Serializable {

    @Column(name = "`project_name`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "空间名称")
    private String projectName;

    @Column(name = "`task_name`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "作业名称")
    private String taskName;

    @Column(name = "`task_desc`",nullable = false)
    @ApiModelProperty(value = "作业描述")
    private String taskDesc;

    @Column(name = "`username`",nullable = false)
    @ApiModelProperty(value = "负责人")
    private String username;

    @Column(name = "`creator`",nullable = false)
    @ApiModelProperty(value = "创建人")
    private String creator;

    @Column(name = "`operator`",nullable = false)
    @ApiModelProperty(value = "修改人")
    private String operator;

    @Column(name = "`status`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "作业状态")
    private String status;

    @Id
    @Column(name = "`id`")
    @ApiModelProperty(value = "自增主键")
    private Long id;

    @Column(name = "`sql_script`")
    @ApiModelProperty(value = "脚本")
    private String sqlScript;

    @Column(name = "`sql_content`",columnDefinition = "mediumtext", length = 65535)
    @ApiModelProperty(value = "脚本")
    private String sqlContent;

    @Column(name = "`parser_status`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "解析状态")
    private String parserStatus;


    @Column(name = "`source_table_num`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "表数量")
    private Integer sourceTableNum;

    @Column(name = "`target_table_num`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "目标表数量")
    private Integer targetTableNum;

    @Column(name = "`task_id`",nullable = false)
    @ApiModelProperty(value = "目标表数量")
    private String taskId;

    @Column(name = "`udf_jars`",nullable = false)
    @ApiModelProperty(value = "UDX")
    private String udfJars;

    @Column(name = "`is_grc`",nullable = false)
    @ApiModelProperty(value = "is_grc")
    private String isGrc;

    @Column(name = "`engine_name`",nullable = false)
    @ApiModelProperty(value = "engine_name")
    private String engineName;


    public void copy(FlinkSqlJobLibra source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
