/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.job.service.impl;

import com.dewu.property.sql.job.domain.FlinkSqlJobLibra;
import com.dewu.utils.ValidationUtil;
import com.dewu.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.dewu.property.sql.job.repository.FlinkSqlJobLibraRepository;
import com.dewu.property.sql.job.service.FlinkSqlJobLibraService;
import com.dewu.property.sql.job.service.dto.FlinkSqlJobLibraDto;
import com.dewu.property.sql.job.service.dto.FlinkSqlJobLibraQueryCriteria;
import com.dewu.property.sql.job.service.mapstruct.FlinkSqlJobLibraMapper;
import org.apache.flink.util.CollectionUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.dewu.utils.PageUtil;
import com.dewu.utils.QueryHelp;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-07-17
**/
@Service
@RequiredArgsConstructor
public class FlinkSqlJobLibraServiceImpl implements FlinkSqlJobLibraService {

    private final FlinkSqlJobLibraRepository flinkSqlJobLibraRepository;
    private final FlinkSqlJobLibraMapper flinkSqlJobLibraMapper;

    @Override
    public Map<String,Object> queryAll(FlinkSqlJobLibraQueryCriteria criteria, Pageable pageable){
        Page<FlinkSqlJobLibra> page = flinkSqlJobLibraRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(flinkSqlJobLibraMapper::toDto));
    }

    @Override
    public List<FlinkSqlJobLibraDto> queryAll(FlinkSqlJobLibraQueryCriteria criteria){
        return flinkSqlJobLibraMapper.toDto(flinkSqlJobLibraRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public FlinkSqlJobLibraDto findById(Long id) {
        FlinkSqlJobLibra flinkSqlJobLibra = flinkSqlJobLibraRepository.findById(id).orElseGet(FlinkSqlJobLibra::new);
        ValidationUtil.isNull(flinkSqlJobLibra.getId(),"FlinkSqlJobLibra","id",id);
        return flinkSqlJobLibraMapper.toDto(flinkSqlJobLibra);
    }

    @Override
    public FlinkSqlJobLibraDto findByTaskName(String taskName) {
        FlinkSqlJobLibraQueryCriteria criteria = new FlinkSqlJobLibraQueryCriteria();
        criteria.setTaskName(taskName);
        List<FlinkSqlJobLibraDto> jobs = this.queryAll(criteria);
        if(CollectionUtil.isNullOrEmpty(jobs)){
            return null;
        }
        return jobs.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FlinkSqlJobLibraDto create(FlinkSqlJobLibra resources) {
        Snowflake snowflake = IdUtil.createSnowflake(1, 1);
        resources.setId(snowflake.nextId()); 
        return flinkSqlJobLibraMapper.toDto(flinkSqlJobLibraRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(FlinkSqlJobLibra resources) {
        FlinkSqlJobLibra flinkSqlJobLibra = flinkSqlJobLibraRepository.findById(resources.getId()).orElseGet(FlinkSqlJobLibra::new);
        ValidationUtil.isNull( flinkSqlJobLibra.getId(),"FlinkSqlJobLibra","id",resources.getId());
        flinkSqlJobLibra.copy(resources);
        flinkSqlJobLibraRepository.save(flinkSqlJobLibra);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            flinkSqlJobLibraRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<FlinkSqlJobLibraDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (FlinkSqlJobLibraDto flinkSqlJobLibra : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("空间名称", flinkSqlJobLibra.getProjectName());
            map.put("作业名称", flinkSqlJobLibra.getTaskName());
            map.put("作业描述", flinkSqlJobLibra.getTaskDesc());
            map.put("负责人", flinkSqlJobLibra.getUsername());
            map.put("创建人", flinkSqlJobLibra.getCreator());
            map.put("修改人", flinkSqlJobLibra.getOperator());
            map.put("作业状态", flinkSqlJobLibra.getStatus());
            map.put("脚本", flinkSqlJobLibra.getSqlScript());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateParserStatus(FlinkSqlJobLibraDto flinkSqlJobLibraDto) {
        flinkSqlJobLibraRepository.updateParserStatus(flinkSqlJobLibraDto.getTaskName(),flinkSqlJobLibraDto.getProjectName(),
                flinkSqlJobLibraDto.getSourceTableNum(),flinkSqlJobLibraDto.getTargetTableNum(),
                flinkSqlJobLibraDto.getParserStatus(),flinkSqlJobLibraDto.getSqlContent());
    }


    @Override
    public List<FlinkSqlJobLibra> queryOfflineJobLineage(String parserStatus) {
       return flinkSqlJobLibraRepository.queryOfflineJobLineage(parserStatus);
    }

    @Override
    public List<FlinkSqlJobLibra> queryControlJob(List<String> parserStatus) {
        return flinkSqlJobLibraRepository.queryControlJob(parserStatus);
    }

}