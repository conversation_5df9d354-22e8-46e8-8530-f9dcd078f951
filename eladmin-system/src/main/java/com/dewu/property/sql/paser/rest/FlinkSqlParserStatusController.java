/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.paser.rest;

import com.dewu.annotation.Log;
import com.dewu.property.sql.paser.domain.FlinkSqlParserStatus;
import com.dewu.property.sql.paser.service.FlinkSqlParserStatusService;
import com.dewu.property.sql.paser.service.dto.FlinkSqlParserStatusQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-07-18
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "血缘解析状态管理")
@RequestMapping("/api/flinkSqlParserStatus")
public class FlinkSqlParserStatusController {

    private final FlinkSqlParserStatusService flinkSqlParserStatusService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('flinkSqlParserStatus:list')")
    public void exportFlinkSqlParserStatus(HttpServletResponse response, FlinkSqlParserStatusQueryCriteria criteria) throws IOException {
        flinkSqlParserStatusService.download(flinkSqlParserStatusService.queryAll(criteria), response);
    }

    @GetMapping
    @ApiOperation("查询血缘解析状态")
    @PreAuthorize("@el.check('flinkSqlParserStatus:list')")
    public ResponseEntity<Object> queryFlinkSqlParserStatus(FlinkSqlParserStatusQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(flinkSqlParserStatusService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增血缘解析状态")
    @ApiOperation("新增血缘解析状态")
    @PreAuthorize("@el.check('flinkSqlParserStatus:add')")
    public ResponseEntity<Object> createFlinkSqlParserStatus(@Validated @RequestBody FlinkSqlParserStatus resources){
        return new ResponseEntity<>(flinkSqlParserStatusService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改血缘解析状态")
    @ApiOperation("修改血缘解析状态")
    @PreAuthorize("@el.check('flinkSqlParserStatus:edit')")
    public ResponseEntity<Object> updateFlinkSqlParserStatus(@Validated @RequestBody FlinkSqlParserStatus resources){
        flinkSqlParserStatusService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除血缘解析状态")
    @ApiOperation("删除血缘解析状态")
    @PreAuthorize("@el.check('flinkSqlParserStatus:del')")
    public ResponseEntity<Object> deleteFlinkSqlParserStatus(@RequestBody Long[] ids) {
        flinkSqlParserStatusService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
