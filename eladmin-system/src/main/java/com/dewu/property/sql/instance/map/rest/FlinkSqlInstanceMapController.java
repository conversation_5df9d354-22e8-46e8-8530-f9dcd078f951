/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.instance.map.rest;

import com.dewu.annotation.Log;
import com.dewu.property.sql.instance.map.domain.FlinkSqlInstanceMap;
import com.dewu.property.sql.instance.map.service.FlinkSqlInstanceMapService;
import com.dewu.property.sql.instance.map.service.dto.FlinkSqlInstanceMapQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-08-04
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "url映射管理")
@RequestMapping("/api/flinkSqlInstanceMap")
public class FlinkSqlInstanceMapController {

    private final FlinkSqlInstanceMapService flinkSqlInstanceMapService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('flinkSqlInstanceMap:list')")
    public void exportFlinkSqlInstanceMap(HttpServletResponse response, FlinkSqlInstanceMapQueryCriteria criteria) throws IOException {
        flinkSqlInstanceMapService.download(flinkSqlInstanceMapService.queryAll(criteria), response);
    }

    @GetMapping
    @ApiOperation("查询url映射")
    @PreAuthorize("@el.check('flinkSqlInstanceMap:list')")
    public ResponseEntity<Object> queryFlinkSqlInstanceMap(FlinkSqlInstanceMapQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(flinkSqlInstanceMapService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增url映射")
    @ApiOperation("新增url映射")
    @PreAuthorize("@el.check('flinkSqlInstanceMap:add')")
    public ResponseEntity<Object> createFlinkSqlInstanceMap(@Validated @RequestBody FlinkSqlInstanceMap resources){
        return new ResponseEntity<>(flinkSqlInstanceMapService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改url映射")
    @ApiOperation("修改url映射")
    @PreAuthorize("@el.check('flinkSqlInstanceMap:edit')")
    public ResponseEntity<Object> updateFlinkSqlInstanceMap(@Validated @RequestBody FlinkSqlInstanceMap resources){
        flinkSqlInstanceMapService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除url映射")
    @ApiOperation("删除url映射")
    @PreAuthorize("@el.check('flinkSqlInstanceMap:del')")
    public ResponseEntity<Object> deleteFlinkSqlInstanceMap(@RequestBody Long[] ids) {
        flinkSqlInstanceMapService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
