package com.dewu.property.sql.pipelineage;

import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @description: /
 * @Author: lmh
 * @date: 2022/11/17 16:25
 */
@Component
@EnableScheduling
@RequiredArgsConstructor
public class PipelineSchedulingManager {
	private final LineageGraphManager lineageGraphManager;

	/**
	 * 1h更新一次血缘Graph
	 */
	@Scheduled(fixedRate = 1000 * 60 * 60)
	void updateGraph() {
		lineageGraphManager.initOrReloadGraph();
	}


	/**
	 * 1分钟更新一次Graph当前延迟
	 */
	@Scheduled(fixedRate = 1000 * 60 )
	void updateDelay() {
		lineageGraphManager.reCalcDelayForGraph();
	}


	/**
//	 * 5 分钟落地一次延迟数据
//	 */
//	@Scheduled(fixedRate = 1000 * 60 * 5)
//	void recordAssetsStatus() {
//		lineageGraphManager.reCalcDelayForGraph(LineageUtils.getCurrentMinute());
//	}
}
