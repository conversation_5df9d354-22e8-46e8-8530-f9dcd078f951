/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.leaf.rest;

import com.dewu.annotation.Log;
import com.dewu.property.cost.RealtimeDataWareHouseCost;
import com.dewu.property.sql.leaf.domain.FlinkSqlLeafSceneAndBusinessLine;
import com.dewu.property.sql.leaf.service.FlinkSqlLeafSceneAndBusinessLineService;
import com.dewu.property.sql.leaf.service.dto.FlinkSqlLeafSceneAndBusinessLineQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-03-12
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "sql叶子管理")
@RequestMapping("/api/flinkSqlLeafSceneAndBusinessLine")
public class FlinkSqlLeafSceneAndBusinessLineController {

    private final FlinkSqlLeafSceneAndBusinessLineService flinkSqlLeafSceneAndBusinessLineService;

    private final RealtimeDataWareHouseCost realtimeDataWareHouseCost;


//    @Log("导出数据")
//    @ApiOperation("导出数据")
//    @GetMapping(value = "/download")
//    @PreAuthorize("@el.check('flinkSqlLeafSceneAndBusinessLine:list')")
//    public void exportFlinkSqlLeafSceneAndBusinessLine(HttpServletResponse response, FlinkSqlLeafSceneAndBusinessLineQueryCriteria criteria) throws IOException {
//        flinkSqlLeafSceneAndBusinessLineService.download(flinkSqlLeafSceneAndBusinessLineService.queryAll(criteria), response);
//    }

//    @GetMapping
//    @ApiOperation("查询sql叶子")
//    @PreAuthorize("@el.check('flinkSqlLeafSceneAndBusinessLine:list')")
//    public ResponseEntity<Object> queryFlinkSqlLeafSceneAndBusinessLine(FlinkSqlLeafSceneAndBusinessLineQueryCriteria criteria, Pageable pageable){
//        return new ResponseEntity<>(flinkSqlLeafSceneAndBusinessLineService.queryAll(criteria,pageable),HttpStatus.OK);
//    }


//    @GetMapping(value = "/query/newLeaf")
//    public ResponseEntity<Object> getNewDayLeafNode() throws Exception{
//        return new ResponseEntity<>(realtimeDataWareHouseCost.getNewDayLeafNode(),HttpStatus.OK);
//    }
//
//    @GetMapping(value = "/get/newLeaf/day/cost")
//    public ResponseEntity<Object> getNewDayLeafNodeCost(String date, boolean isSend) throws Exception {
//        return new ResponseEntity<>(realtimeDataWareHouseCost.getNewDayLeafNodeCost(date, isSend), HttpStatus.OK);
//    }
//
//    @GetMapping(value = "/get/newLeaf/month/cost")
//    public ResponseEntity<Object> getNewMonthLeafNodeCost(String date, boolean isSend) throws Exception {
//        return new ResponseEntity<>(realtimeDataWareHouseCost.getNewMonthLeafNodeCost(date,isSend), HttpStatus.OK);
//    }
//
//
//    @GetMapping(value = "/manual/update/day/cost")
//    public ResponseEntity<Object> manualUpdateDayOfJobCost() throws Exception{
//        realtimeDataWareHouseCost.scheduleJobCost("");
//        return new ResponseEntity<>(HttpStatus.OK);
//    }

    @GetMapping
    @ApiOperation("查询sql叶子")
    public ResponseEntity<Object> queryFlinkSqlLeafSceneAndBusinessLine(FlinkSqlLeafSceneAndBusinessLineQueryCriteria criteria){
        return new ResponseEntity<>(flinkSqlLeafSceneAndBusinessLineService.queryAll(criteria),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增sql叶子")
    @ApiOperation("新增sql叶子")
    public ResponseEntity<Object> createFlinkSqlLeafSceneAndBusinessLine(@Validated @RequestBody FlinkSqlLeafSceneAndBusinessLine resources){
        return new ResponseEntity<>(flinkSqlLeafSceneAndBusinessLineService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改sql叶子")
    @ApiOperation("修改sql叶子")
    public ResponseEntity<Object> updateFlinkSqlLeafSceneAndBusinessLine(@Validated @RequestBody FlinkSqlLeafSceneAndBusinessLine resources){
        flinkSqlLeafSceneAndBusinessLineService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PutMapping(value = "/update/all")
    public ResponseEntity<Object> updateFlinkSqlLeafSceneAndBusinessLine(@Validated @RequestBody Set<FlinkSqlLeafSceneAndBusinessLine> resources){
        flinkSqlLeafSceneAndBusinessLineService.updateByUk(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }



    @DeleteMapping
    @Log("删除sql叶子")
    @ApiOperation("删除sql叶子")
    public ResponseEntity<Object> deleteFlinkSqlLeafSceneAndBusinessLine(@RequestBody Long[] ids) {
        flinkSqlLeafSceneAndBusinessLineService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
