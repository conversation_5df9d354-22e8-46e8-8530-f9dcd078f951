package com.dewu.property.sql.pipelineage;

import com.dewu.utils.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2024/7/8
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class JobInfoCache {
	private final JobInfoHbaseVisitor jobInfoHbaseVisitor;
	private JobInfoMetricsCache currCache = new JobInfoMetricsCache();
	private Map<String, JobInfoMetricsCache> tmpCache = new HashMap<>();

	public void initCache(Set<String> jobs, String ctimeBucket) {
		long curr = System.currentTimeMillis();
		List<Tuple2<String, Double>> delay = jobInfoHbaseVisitor.getDelay(jobs, ctimeBucket);
		log.info("LGM : Load tmp jobInfo[{}] takes about {} ms. ", ctimeBucket, System.currentTimeMillis() - curr);
		Map<String, JobInfoMetrics> tmp = new HashMap<>();
		delay.forEach(tuple2 -> tmp.put(tuple2.f0, new JobInfoMetrics(tuple2.f0, tuple2.f1, curr)));
		JobInfoMetricsCache cache = new JobInfoMetricsCache();
		cache.setInnerCache(tmp);
		cache.setLastCtimeBucket(ctimeBucket);
		tmpCache.put(ctimeBucket, cache);
		log.info("LGM : finished Load tmp jobInfo[{}]. ", ctimeBucket);
	}

	public void releaseCache(String ctimeBucket) {
		log.info("LGM : release tmp jobInfo[{}]. ", ctimeBucket);
		tmpCache.remove(ctimeBucket);
	}

	public void refreshAll(Set<String> jobs, String ctimeBucket) {
		long curr = System.currentTimeMillis();
		List<Tuple2<String, Double>> delay = jobInfoHbaseVisitor.getDelay(jobs, ctimeBucket);
		log.info("LGM : Load jobInfo[{}] takes about {} ms. ", ctimeBucket, System.currentTimeMillis() - curr);
		Map<String, JobInfoMetrics> tmp = new HashMap<>();
		delay.forEach(tuple2 -> tmp.put(tuple2.f0, new JobInfoMetrics(tuple2.f0, tuple2.f1, curr)));
		JobInfoMetricsCache cache = new JobInfoMetricsCache();
		cache.setInnerCache(tmp);
		cache.setLastCtimeBucket(ctimeBucket);
		currCache = cache;
		log.info("LGM : finished Load jobInfo[{}]. ", ctimeBucket);
	}

	public double getDelay(JobInfoMetricsCache cache, String jobName, String ctimeBucket) {
		if (cache.getLastCtimeBucket().equals(ctimeBucket)) {
			if (!cache.containsKey(jobName)) {
				return 0.0;
			}
			Double delay = cache.get(jobName).getDelay();
//			log.info("LGM : find inMem jobInfo[{}.{}] delay {}. ", jobName, ctimeBucket, delay);
			return delay;
		} else if (tmpCache.containsKey(ctimeBucket)) {
			JobInfoMetricsCache tmp = tmpCache.get(ctimeBucket);
			if (tmp.containsKey(jobName)) {
				Double delay = tmp.get(jobName).getDelay();
//				log.info("LGM : find inTmp jobInfo[{}.{}] delay {}. ", jobName, ctimeBucket, delay);
				return delay;
			}
			return 0.0;
		}
		Double delay = jobInfoHbaseVisitor.getDelay(jobName, ctimeBucket);
		log.warn("LGM : find offMem jobInfo[{}.{} delay {}]. ", jobName, ctimeBucket, delay);
		return delay;
	}

	public double getDelay(String jobName, String ctimeBucket) {
//		return 10.0;
		return getDelay(currCache, jobName, ctimeBucket);
	}

	public double getDelay(String jobName) {
		if (StringUtils.isEmpty(currCache.getLastCtimeBucket())) {
			return 0.0;
		}
		return getDelay(currCache, jobName, currCache.getLastCtimeBucket());
	}

	@Data
	@NoArgsConstructor
	class JobInfoMetricsCache {
		private Map<String, JobInfoMetrics> innerCache = new HashMap<>();
		private String lastCtimeBucket = "";

		public JobInfoMetrics get(String key) {
			return innerCache.get(key);
		}

		public boolean containsKey(String key) {
			return innerCache.containsKey(key);
		}

	}
}
