/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.leaf.service.impl;

import com.dewu.property.sql.leaf.domain.FlinkSqlLeafSceneAndBusinessLine;
import com.dewu.utils.ValidationUtil;
import com.dewu.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.dewu.property.sql.leaf.repository.FlinkSqlLeafSceneAndBusinessLineRepository;
import com.dewu.property.sql.leaf.service.FlinkSqlLeafSceneAndBusinessLineService;
import com.dewu.property.sql.leaf.service.dto.FlinkSqlLeafSceneAndBusinessLineDto;
import com.dewu.property.sql.leaf.service.dto.FlinkSqlLeafSceneAndBusinessLineQueryCriteria;
import com.dewu.property.sql.leaf.service.mapstruct.FlinkSqlLeafSceneAndBusinessLineMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.dewu.utils.PageUtil;
import com.dewu.utils.QueryHelp;

import java.util.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-03-12
**/
@Service
@RequiredArgsConstructor
public class FlinkSqlLeafSceneAndBusinessLineServiceImpl implements FlinkSqlLeafSceneAndBusinessLineService {

    private final FlinkSqlLeafSceneAndBusinessLineRepository flinkSqlLeafSceneAndBusinessLineRepository;
    private final FlinkSqlLeafSceneAndBusinessLineMapper flinkSqlLeafSceneAndBusinessLineMapper;

    @Override
    public Map<String,Object> queryAll(FlinkSqlLeafSceneAndBusinessLineQueryCriteria criteria, Pageable pageable){
        Page<FlinkSqlLeafSceneAndBusinessLine> page = flinkSqlLeafSceneAndBusinessLineRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(flinkSqlLeafSceneAndBusinessLineMapper::toDto));
    }

    @Override
    public List<FlinkSqlLeafSceneAndBusinessLineDto> queryAll(FlinkSqlLeafSceneAndBusinessLineQueryCriteria criteria){
        return flinkSqlLeafSceneAndBusinessLineMapper.toDto(flinkSqlLeafSceneAndBusinessLineRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public FlinkSqlLeafSceneAndBusinessLineDto findById(Long id) {
        FlinkSqlLeafSceneAndBusinessLine flinkSqlLeafSceneAndBusinessLine = flinkSqlLeafSceneAndBusinessLineRepository.findById(id).orElseGet(FlinkSqlLeafSceneAndBusinessLine::new);
        ValidationUtil.isNull(flinkSqlLeafSceneAndBusinessLine.getId(),"FlinkSqlLeafSceneAndBusinessLine","id",id);
        return flinkSqlLeafSceneAndBusinessLineMapper.toDto(flinkSqlLeafSceneAndBusinessLine);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FlinkSqlLeafSceneAndBusinessLineDto create(FlinkSqlLeafSceneAndBusinessLine resources) {
        return flinkSqlLeafSceneAndBusinessLineMapper.toDto(flinkSqlLeafSceneAndBusinessLineRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(FlinkSqlLeafSceneAndBusinessLine resources) {
        FlinkSqlLeafSceneAndBusinessLine flinkSqlLeafSceneAndBusinessLine = flinkSqlLeafSceneAndBusinessLineRepository.findById(resources.getId()).orElseGet(FlinkSqlLeafSceneAndBusinessLine::new);
        ValidationUtil.isNull( flinkSqlLeafSceneAndBusinessLine.getId(),"FlinkSqlLeafSceneAndBusinessLine","id",resources.getId());
        flinkSqlLeafSceneAndBusinessLine.copy(resources);
        flinkSqlLeafSceneAndBusinessLineRepository.save(flinkSqlLeafSceneAndBusinessLine);
    }

    @Override
    public void updateAll(Set<FlinkSqlLeafSceneAndBusinessLine> resources) {
        flinkSqlLeafSceneAndBusinessLineRepository.saveAll(resources);
    }

    public void updateByUk(Set<FlinkSqlLeafSceneAndBusinessLine> resources){
        for (FlinkSqlLeafSceneAndBusinessLine resource : resources) {
            flinkSqlLeafSceneAndBusinessLineRepository.updateBusinessLineAndSceneByProjectNameAndJobName(
                    resource.getProjectName()
                    ,resource.getJobName()
                    ,resource.getBusinessLine()
                    ,resource.getScene());
        }
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            flinkSqlLeafSceneAndBusinessLineRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<FlinkSqlLeafSceneAndBusinessLineDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (FlinkSqlLeafSceneAndBusinessLineDto flinkSqlLeafSceneAndBusinessLine : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("作业名称", flinkSqlLeafSceneAndBusinessLine.getJobName());
            map.put("业务线", flinkSqlLeafSceneAndBusinessLine.getBusinessLine());
            map.put("场景", flinkSqlLeafSceneAndBusinessLine.getScene());
            map.put("创建时间", flinkSqlLeafSceneAndBusinessLine.getCreateTime());
            map.put("修改时间", flinkSqlLeafSceneAndBusinessLine.getModifyTime());
            map.put("作业归属人", flinkSqlLeafSceneAndBusinessLine.getCreator());
            map.put("空间名称", flinkSqlLeafSceneAndBusinessLine.getProjectName());
            map.put("是否删除", flinkSqlLeafSceneAndBusinessLine.getIsDelete());
            map.put("是否合规，0无效，1不合规，2 合规", flinkSqlLeafSceneAndBusinessLine.getIsGrc());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}