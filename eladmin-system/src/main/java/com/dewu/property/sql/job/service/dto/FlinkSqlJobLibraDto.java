/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.job.service.dto;

import lombok.Data;
import java.io.Serializable;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-07-17
**/
@Data
public class FlinkSqlJobLibraDto implements Serializable {

    /** 空间名称 */
    private String projectName;

    /** 作业名称 */
    private String taskName;

    /** 作业描述 */
    private String taskDesc;

    /** 负责人 */
    private String username;

    /** 创建人 */
    private String creator;

    /** 任务负责人 */
    private String operator;

    /** 作业状态 */
    private String status;

    /** 自增主键 */
    /** 防止精度丢失 */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /** 脚本 */
    private String sqlScript;

    /** 记录解析异常线程栈 */
    private String sqlContent="";

    private String parserStatus;

    private Integer sourceTableNum;

    private Integer targetTableNum;

    private String taskId;

    private String udfJars;

    private String isGrc;

    private String engineName;
}