/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.lineage.service;

import com.dewu.property.sql.job.domain.FlinkSqlJobLibra;
import com.dewu.property.sql.job.service.dto.FlinkSqlJobLibraDto;
import com.dewu.property.sql.lineage.domain.FlinkSqlLineage;
import com.dewu.property.sql.lineage.service.dto.*;
import org.springframework.data.domain.Pageable;

import java.util.Map;
import java.util.List;
import java.io.IOException;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2023-07-14
**/
public interface FlinkSqlLineageService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(FlinkSqlLineageQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<FlinkSqlLineageDto>
    */
    List<FlinkSqlLineageDto> queryAll(FlinkSqlLineageQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return FlinkSqlLineageDto
     */
    FlinkSqlLineageDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return FlinkSqlLineageDto
    */
    FlinkSqlLineageDto create(FlinkSqlLineage resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(FlinkSqlLineage resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    void deleteByJobNameAndProjectName(String jobName,String projectName);

    void updateSqlLineStrategy(List<SqlInstanceMapDto> sqlInstanceMapDtos, FlinkSqlJobLibraDto flinkSqlJobLibra, Set<FlinkSqlLineage> list);

    void updateSqlLineIsDelete(String jobName);

    void updateSqlLineStatus(FlinkSqlJobLibra flinkSqlJobLibra);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<FlinkSqlLineageDto> all, HttpServletResponse response) throws IOException;

    List<TableLineageDto> likeDistinctByTableName(String tableName) throws Exception;

    List<TableLineageDto> likeDistinctByTableNameForAssertLink(String tableName) throws Exception;

    List<Map> likeDistinctByTargetTable(String tableName);

    List<SqlLineageExtDto> querySqlLine(String tableName, String database,String tableInstance, String dbType) throws Exception;

    List<SqlLineageExtDto> querySqlLineBySourceTable(String tableName, String database, String tableInstance,String dbType) throws Exception;

    List<SqlLineageExtDto> querySqlLineByTargetTable(String tableName, String database,String tableInstance, String dbType) throws Exception;

    List<String> queryUpStreamJobByTargetTable(String tableName, String database,String tableInstance, String dbType);

    List<String> queryDownStreamJobBySourceTable(String tableName, String database,String tableInstance, String dbType);

    void isGrcCheck(String tableId);

    void isGrcCheck() throws Exception;

    List<FlinkSqlLineage> queryJobLine(String jobName);

    List<FlinkSqlLineage> querySqlLineByJobName(String jobName);

    List<FlinkSqlLineage> queryJobLineByTargetJobName(String jobName);

    List<FlinkSqlLineage> queryJobLineBySourceJobName(String jobName);
}