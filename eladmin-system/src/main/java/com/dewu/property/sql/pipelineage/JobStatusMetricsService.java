package com.dewu.property.sql.pipelineage;


import com.dewu.exception.HbaseIOException;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.ServerName;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.List;


@Slf4j
@RequiredArgsConstructor
@Service
public class JobStatusMetricsService implements Serializable {

	@Value("${pipeline.metrics.hbase.zookeeperQuorum}")
	private String zookeeperQuorum;
	@Value("${pipeline.metrics.hbase.username}")
	private String username;
	@Value("${pipeline.metrics.hbase.password}")
	private String password;
	@Value("${pipeline.metrics.hbase.table}")
	private String nameSpaceTableName;
	private Configuration configuration;
	private Connection connection;

	public static final String DATA_CELL_NAME = "cf";
	final Object lock = new Object();

	public void telnet() throws IOException {
		Admin admin = this.getConnection().getAdmin();
		ServerName master = admin.getMaster();
		System.out.println(master);
		admin.close();
		close();
	}

	public Connection getConnection() {
		log.info("尝试HbaseUtils实例初始化");
		if (connection == null || connection.isClosed()) {
			synchronized (lock) {
				if (connection == null || connection.isClosed()) {
					try {
						this.configuration = HBaseConfiguration.create();
						this.configuration.set("hbase.zookeeper.quorum", zookeeperQuorum);
						this.configuration.set("zookeeper.recovery.retry", "3");
						this.configuration.set("zookeeper.session.timeout", "10000");
						if (StringUtils.isNotBlank(username)) {
							this.configuration.set("hbase.client.username", username);
						}
						if (StringUtils.isNotBlank(password)) {
							this.configuration.set("hbase.client.password", password);
						}
						configuration.set("hbase.client.ipc.pool.size", "10");
						this.connection = ConnectionFactory.createConnection(configuration);
						log.info("HbaseUtils实例初始化success");
					} catch (IOException e) {
						log.error("HbaseUtils实例初始化失败！错误信息为：" + e.getMessage(), e);
						throw new HbaseIOException("HbaseUtils实例初始化失败！",e);
					}
				}
			}
		}
		return connection;
	}

	public static void main(String[] args) throws ClassNotFoundException, SQLException, IOException {
		JobStatusMetricsService hbaseUtils = new JobStatusMetricsService();
		hbaseUtils.telnet();
	}

	public Result singleGet(Get get) {
		return this.singleGet(nameSpaceTableName, get);
	}

	/**
	 * 获取某个rowKey的所有列簇所有列值
	 *
	 * @param tableName hbase表名
	 * @param get       只指定了rowKey的get
	 * @return 返回result
	 */
	public Result singleGet(String tableName, Get get) {
		Result result = null;
		try (Table table = getConnection().getTable(TableName.valueOf(tableName))) {
			result = table.get(get);
		} catch (IOException e) {
			closeWithException(String.format("singleGet rowKey:%s get failed", new String(get.getRow())), e);
		}
		return result;
	}


	public Result[] batchGet(List<Get> gets) {
		return this.batchGet(nameSpaceTableName, gets);
	}

	/**
	 * 批量获取
	 *
	 * @param tableName 表名
	 * @param gets      get列表
	 * @return
	 */
	public Result[] batchGet(String tableName, List<Get> gets) {
		Result[] results = null;
		log.info("LGM : Batch gets[{}] for table[{}] begin.", gets.size(), tableName);
		try (Table table = getConnection().getTable(TableName.valueOf(tableName))) {
			results = table.get(gets);
		} catch (IOException e) {
			closeWithException("batchGets get failed", e);
		}
		log.info("LGM : Batch gets[{}] for table[{}] finished with results[{}].", gets.size(), tableName, results == null ? 0 : results.length);
		return results;
	}


	public void putData(Put put) throws IOException {
		this.putData(nameSpaceTableName, put);
	}


	/**
	 * 向hbase表插入数据
	 *
	 * @param tableName hbase表名
	 * @param put       要插入的put，需指定列簇和列
	 */
	public void putData(String tableName, Put put) throws IOException {
		try (Table table = connection.getTable(TableName.valueOf(tableName))) {
			table.put(put);
		} catch (IOException e) {
			closeWithException(String.format("rowKey:%s put failed", new String(put.getRow())), e);
		}
	}

	public void putBatchData(List<Put> puts) throws IOException {
		this.putBatchData(nameSpaceTableName, puts);
	}

	/**
	 * 向hbase表批量插入数据
	 *
	 * @param tableName hbase表名
	 * @param puts      要插入的puts，需指定列簇和列
	 */
	public void putBatchData(String tableName, List<Put> puts) throws IOException {
		try (Table table = getConnection().getTable(TableName.valueOf(tableName))) {
			table.put(puts);
		} catch (IOException e) {
			closeWithException("put batch data failed", e);
		}
	}

	public void closeWithException(String message, Exception e) {
		try {
			close();
		} catch (IOException ex) {
			log.error("Close connection failed", ex);
		}
		throw new HbaseIOException(message, e);
	}

	public void close() throws IOException {
		getConnection().close();
	}
}
