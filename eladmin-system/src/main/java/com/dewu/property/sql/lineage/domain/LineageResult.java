package com.dewu.property.sql.lineage.domain;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class LineageResult {

    private String sourceCatalog;

    private String sourceDatabase;

    private String sourceTable;

    private String sourceColumn;

    private String targetCatalog;

    private String targetDatabase;

    private String targetTable;

    private String targetColumn;

    /**
     * Stores the expression for data conversion,
     * which source table fields are transformed by which expression the target field
     */
    private String transform;

    public LineageResult(String sourceTablePath, String sourceColumn, String targetTablePath, String targetColumn,
                         String transform) {
        String[] sourceItems = sourceTablePath.split("\\" + ".");
        String[] targetItems = targetTablePath.split("\\" + ".");

        this.sourceCatalog = sourceItems[0];
        this.sourceDatabase = sourceItems[1];
        this.sourceTable = sourceItems[2];
        this.sourceColumn = sourceColumn;
        this.targetCatalog = targetItems[0];
        this.targetDatabase = targetItems[1];
        this.targetTable = targetItems[2];
        this.targetColumn = targetColumn;
        this.transform = transform;
    }

    public LineageResult(String catalog, String database, String sourceTable, String sourceColumn, String targetTable,
                         String targetColumn) {
        this.sourceCatalog = catalog;
        this.sourceDatabase = database;
        this.sourceTable = sourceTable;
        this.sourceColumn = sourceColumn;
        this.targetCatalog = catalog;
        this.targetDatabase = database;
        this.targetTable = targetTable;
        this.targetColumn = targetColumn;
    }

    public static List<LineageResult> buildResult(String catalog, String database, String[][] expectedArray) {
        return Stream.of(expectedArray)
                .map(e -> {
                    LineageResult result = new LineageResult(catalog, database, e[0], e[1], e[2], e[3]);
                    // transform field is optional
                    if (e.length == 5) {
                        result.setTransform(e[4]);
                    }
                    return result;
                }).collect(Collectors.toList());
    }

    public LineageResult(
            String sourceCatalog,
            String sourceDatabase,
            String sourceTable,
            String sourceColumn,
            String targetCatalog,
            String targetDatabase,
            String targetTable,
            String targetColumn,
            String transform) {
        this.sourceCatalog = sourceCatalog;
        this.sourceDatabase = sourceDatabase;
        this.sourceTable = sourceTable;
        this.sourceColumn = sourceColumn;
        this.targetCatalog = targetCatalog;
        this.targetDatabase = targetDatabase;
        this.targetTable = targetTable;
        this.targetColumn = targetColumn;
        this.transform = transform;
    }

    public String getSourceCatalog() {
        return sourceCatalog;
    }

    public void setSourceCatalog(String sourceCatalog) {
        this.sourceCatalog = sourceCatalog;
    }

    public String getSourceDatabase() {
        return sourceDatabase;
    }

    public void setSourceDatabase(String sourceDatabase) {
        this.sourceDatabase = sourceDatabase;
    }

    public String getSourceTable() {
        return sourceTable;
    }

    public void setSourceTable(String sourceTable) {
        this.sourceTable = sourceTable;
    }

    public String getSourceColumn() {
        return sourceColumn;
    }

    public void setSourceColumn(String sourceColumn) {
        this.sourceColumn = sourceColumn;
    }

    public String getTargetCatalog() {
        return targetCatalog;
    }

    public void setTargetCatalog(String targetCatalog) {
        this.targetCatalog = targetCatalog;
    }

    public String getTargetDatabase() {
        return targetDatabase;
    }

    public void setTargetDatabase(String targetDatabase) {
        this.targetDatabase = targetDatabase;
    }

    public String getTargetTable() {
        return targetTable;
    }

    public void setTargetTable(String targetTable) {
        this.targetTable = targetTable;
    }

    public String getTargetColumn() {
        return targetColumn;
    }

    public void setTargetColumn(String targetColumn) {
        this.targetColumn = targetColumn;
    }

    public String getTransform() {
        return transform;
    }

    public void setTransform(String transform) {
        this.transform = transform;
    }


    @Override
    public String toString() {
        return "LineageResult{" +
                "sourceCatalog='" + sourceCatalog + '\'' +
                ", sourceDatabase='" + sourceDatabase + '\'' +
                ", sourceTable='" + sourceTable + '\'' +
                ", sourceColumn='" + sourceColumn + '\'' +
                ", targetCatalog='" + targetCatalog + '\'' +
                ", targetDatabase='" + targetDatabase + '\'' +
                ", targetTable='" + targetTable + '\'' +
                ", targetColumn='" + targetColumn + '\'' +
                ", transform='" + transform + '\'' +
                '}';
    }
}
