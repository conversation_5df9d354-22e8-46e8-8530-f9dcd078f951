/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.paser.service.impl;

import com.dewu.property.sql.job.domain.FlinkSqlJobLibra;
import com.dewu.property.sql.paser.domain.FlinkSqlParserStatus;
import com.dewu.utils.ValidationUtil;
import com.dewu.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.dewu.property.sql.paser.repository.FlinkSqlParserStatusRepository;
import com.dewu.property.sql.paser.service.FlinkSqlParserStatusService;
import com.dewu.property.sql.paser.service.dto.FlinkSqlParserStatusDto;
import com.dewu.property.sql.paser.service.dto.FlinkSqlParserStatusQueryCriteria;
import com.dewu.property.sql.paser.service.mapstruct.FlinkSqlParserStatusMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.dewu.utils.PageUtil;
import com.dewu.utils.QueryHelp;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-07-18
**/
@Service
@RequiredArgsConstructor
public class FlinkSqlParserStatusServiceImpl implements FlinkSqlParserStatusService {

    private final FlinkSqlParserStatusRepository flinkSqlParserStatusRepository;
    private final FlinkSqlParserStatusMapper flinkSqlParserStatusMapper;

    @Override
    public Map<String,Object> queryAll(FlinkSqlParserStatusQueryCriteria criteria, Pageable pageable){
        Page<FlinkSqlParserStatus> page = flinkSqlParserStatusRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(flinkSqlParserStatusMapper::toDto));
    }

    @Override
    public List<FlinkSqlParserStatusDto> queryAll(FlinkSqlParserStatusQueryCriteria criteria){
        return flinkSqlParserStatusMapper.toDto(flinkSqlParserStatusRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public FlinkSqlParserStatusDto findById(Long id) {
        FlinkSqlParserStatus flinkSqlParserStatus = flinkSqlParserStatusRepository.findById(id).orElseGet(FlinkSqlParserStatus::new);
        ValidationUtil.isNull(flinkSqlParserStatus.getId(),"FlinkSqlParserStatus","id",id);
        return flinkSqlParserStatusMapper.toDto(flinkSqlParserStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FlinkSqlParserStatusDto create(FlinkSqlParserStatus resources) {
        return flinkSqlParserStatusMapper.toDto(flinkSqlParserStatusRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(FlinkSqlParserStatus resources) {
        FlinkSqlParserStatus flinkSqlParserStatus = flinkSqlParserStatusRepository.findById(resources.getId()).orElseGet(FlinkSqlParserStatus::new);
        ValidationUtil.isNull( flinkSqlParserStatus.getId(),"FlinkSqlParserStatus","id",resources.getId());
        flinkSqlParserStatus.copy(resources);
        flinkSqlParserStatusRepository.save(flinkSqlParserStatus);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            flinkSqlParserStatusRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<FlinkSqlParserStatusDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (FlinkSqlParserStatusDto flinkSqlParserStatus : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("表数量", flinkSqlParserStatus.getTableNum());
            map.put("目标表数量", flinkSqlParserStatus.getTargetTableNum());
            map.put("作业名称", flinkSqlParserStatus.getJobName());
            map.put("创建时间", flinkSqlParserStatus.getCreateTime());
            map.put("修改时间", flinkSqlParserStatus.getModifyTime());
            map.put("作业归属人", flinkSqlParserStatus.getCreator());
            map.put("空间名称", flinkSqlParserStatus.getProjectName());
            map.put("作业id", flinkSqlParserStatus.getJobId());
            map.put("解析状态", flinkSqlParserStatus.getParserStatus());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

}