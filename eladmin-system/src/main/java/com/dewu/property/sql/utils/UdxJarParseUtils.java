package com.dewu.property.sql.utils;

import com.google.common.base.CaseFormat;
import com.google.common.collect.ImmutableMap;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.functions.TableAggregateFunction;
import org.apache.flink.table.functions.TableFunction;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.lang.reflect.Method;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.stream.Collectors;

public class UdxJarParseUtils {


    private static final Map<String, String> FUNCTION_SUFFIX_MAP = ImmutableMap.of(
            ScalarFunction.class.getName(), "udf",
            TableFunction.class.getName(), "udtf",
            AggregateFunction.class.getName(), "udaf",
            TableAggregateFunction.class.getName(), "udtaf");


    public  List<FunctionInfo> parseFunction(File file) throws IOException, ClassNotFoundException {
        List<FunctionInfo> resultList = new ArrayList<>();
        URL url = file.toURI().toURL();

        try (
//                URLClassLoader classLoader = new URLClassLoader(new URL[]{url}, getClass().getClassLoader());
                URLClassLoader classLoader = new URLClassLoader(new URL[]{url}, Thread.currentThread().getContextClassLoader());
             JarFile jarFile = new JarFile(file)
        ) {

            Thread.currentThread().setContextClassLoader(classLoader);

            Enumeration<JarEntry> entries = jarFile.entries();
            while (entries.hasMoreElements()) {
                String entryName = entries.nextElement().getName();
                if (entryName.endsWith(".class")) {
                    String className = entryName.replace("/", ".").substring(0, entryName.length() - 6);
                    // filter commons-compress.jar because of asm
//                    if (!className.startsWith("org.apache.commons.compress.harmony")
//                            && !className.startsWith("org.apache.flink.table.functions")) {
                        if(className.indexOf("StreamLimiter")>-1){
                        Class<?> clazz = classLoader.loadClass(className);

                            Object obj = clazz.newInstance();  // 创建 MyClass 类的实例
                            FileOutputStream fout = new FileOutputStream("/Users/<USER>/Desktop/data.bin");
                            ObjectOutputStream oos = new ObjectOutputStream(fout);
                            oos.writeObject(obj);  // 序列化 MyClass 类的实例
                            oos.close();
                            fout.close();


                        System.out.println("className："+className);
                        System.out.println("clazz.getSuperclass()："+clazz.getSuperclass());
                        if(clazz.getSuperclass() != null) {
                            System.out.println("clazz.getSuperclass().getName()："+clazz.getSuperclass().getName());
                        }

                        if (clazz.getSuperclass() != null
                                && FUNCTION_SUFFIX_MAP.containsKey(clazz.getSuperclass().getName())) {
                            resultList.add(parseUserDefinedFunction(clazz));
                        }
                    }
                }
            }
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return resultList;
    }

    public FunctionInfo parseUserDefinedFunction(Class<?> clazz) {
        String functionClass = clazz.getName();
        String className = searchClassName(functionClass);
        String functionName = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, className);
        // replace function with udf
        functionName = functionName.replace("function", FUNCTION_SUFFIX_MAP.get(clazz.getSuperclass().getName()));
        // consider only the case of one eval function
        Optional<Method> methodOptional = Arrays.stream(clazz.getDeclaredMethods())
                .filter(e -> "eval".equals(e.getName()))
                .findFirst();

        FunctionInfo result = new FunctionInfo();
        result.setClassName(functionClass);
        result.setFunctionName(functionName);

        if (methodOptional.isPresent()) {
            Method method = methodOptional.get();
            AtomicInteger atomicInteger = new AtomicInteger(1);
            String parameters = Arrays.stream(method.getParameters())
                    .map(parameter -> searchClassName(parameter.getType().getName() + atomicInteger.getAndIncrement()))
                    .collect(Collectors.joining(","));

            // set functionFormat
            result.setInvocation(String.format("%s(%s)", functionName, parameters));

            // use function return type as description
            result.setDescr(buildFunctionReturnType(clazz, method));
        }
        return result;
    }

    private String searchClassName(String value) {
        return value.contains(".") ? value.substring(value.lastIndexOf(".") + 1) : value;
    }

    private String buildFunctionReturnType(Class<?> clazz, Method method) {
        if (clazz.getSuperclass().isAssignableFrom(TableFunction.class)
                && clazz.isAnnotationPresent(FunctionHint.class)) {
            return "return " + clazz.getAnnotation(FunctionHint.class).output().value();
        }
        return "return " + searchClassName(method.getReturnType().getName());
    }

}
