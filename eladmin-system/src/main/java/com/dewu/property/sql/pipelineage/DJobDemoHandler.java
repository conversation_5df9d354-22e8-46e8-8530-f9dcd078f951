//package com.dewu.property.sql.pipelineage;
//import com.poizon.djob.annotation.DJobComponent;
//import com.shizhuang.jgs.djob.client.DJobInitConfig;
//import com.shizhuang.jgs.djob.client.TaskContext;
//import com.shizhuang.jgs.djob.client.TaskHandler;
//import com.shizhuang.jgs.djob.client.TaskResult;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * <AUTHOR>
// * @date 2024/7/1
// */
//@Slf4j
//@DJobComponent(name = "DJobDemoHandler", desc = "demo任务")
//public class DJobDemoHandler implements TaskHandler {
//	@Override
//	public TaskResult execute(TaskContext taskContext) throws Exception {
//		long expectScheduleTsSec = taskContext.getExpectScheduleTsSec();
//		log.info("");
//		return TaskResult.success();
//	}
//}
