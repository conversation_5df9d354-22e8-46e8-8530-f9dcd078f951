package com.dewu.property.sql.lineage.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.sql.Timestamp;

@Entity
@DynamicInsert
@DynamicUpdate
@Data
@Table(name="flink_sql_table_relevance")
public class TableRelevance implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    @ApiModelProperty(value = "自增id")
    private Long id;

    @Column(name = "`table_id`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "来源表ID")
    private Long tableId;

    @Column(name = "`physics_table`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "来源物理表")
    private String physicsTable;

//    @Column(name = "`view_table`",nullable = false)
//    @NotBlank
//    @ApiModelProperty(value = "来源视图表")
//    private String viewTable;

    @Column(name = "`table_database`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "来源数据库")
    private String tableDatabase;

    @Column(name = "`table_db_type`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "来源数据库类型")
    private String tableDbType;

    @Column(name = "`table_instance`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "来源实例")
    private String tableInstance;

    @Column(name = "`create_time`",nullable = false)
//    @NotNull
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @Column(name = "`modify_time`",nullable = false)
//    @NotNull
    @ApiModelProperty(value = "修改时间")
    private Timestamp modifyTime;

    @Column(name = "`creator`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "作业归属人")
    private String creator;

    @Column(name = "`is_delete`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "是否删除")
    private boolean isDelete;


    @Column(name = "`upstream_job_num`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "上游作业数量")
    private Integer upstreamJobNum;

    @Column(name = "`downstream_job_num`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "下游作业数量")
    private Integer downstreamJobNum;

    @Column(name = "`upstream_table_num`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "上游表数量")
    private Integer upstreamTableNum;

    @Column(name = "`downstream_table_num`",nullable = false)
//    @NotBlank
    @ApiModelProperty(value = "下游表数量")
    private Integer downstreamTableNum;


    public void copy(TableRelevance source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}

