/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.instance.map.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-08-04
**/
@Entity
@DynamicInsert
@DynamicUpdate
@Data
@Table(name="flink_sql_instance_map")
public class FlinkSqlInstanceMap implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    @ApiModelProperty(value = "自增id")
    private Long id;

    @Column(name = "`instance_all`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "实例地址集合")
    private String instanceAll;

    @Column(name = "`instance_unique`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "实例地址唯一编码")
    private String instanceUnique;

    @Column(name = "`instance_type`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "实例类型")
    private String instanceType;

    @Column(name = "`create_time`",nullable = false)
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @Column(name = "`modify_time`",nullable = false)
    @ApiModelProperty(value = "修改时间")
    private Timestamp modifyTime;

    public void copy(FlinkSqlInstanceMap source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
