/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.sql.lineage.service.impl;

import com.alibaba.fastjson.JSON;
import com.dewu.property.metadata.service.ReDataSourceMetadataService;
import com.dewu.property.sql.job.domain.FlinkSqlJobLibra;
import com.dewu.property.sql.job.service.FlinkSqlJobLibraService;
import com.dewu.property.sql.job.service.dto.FlinkSqlJobLibraDto;
import com.dewu.property.sql.lineage.domain.FlinkSqlLineage;
import com.dewu.property.sql.lineage.repository.FlinkSqlLineageRepository;
import com.dewu.property.sql.lineage.service.FlinkSqlLineageService;
import com.dewu.property.sql.lineage.service.SqlInstanceService;
import com.dewu.property.sql.lineage.service.dto.*;
import com.dewu.utils.ValidationUtil;
import com.dewu.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.dewu.property.sql.lineage.service.mapstruct.FlinkSqlLineageMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.dewu.utils.PageUtil;
import com.dewu.utils.QueryHelp;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-07-14
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class FlinkSqlLineageServiceImpl implements FlinkSqlLineageService {

    private final FlinkSqlLineageRepository flinkSqlLineageRepository;
    private final FlinkSqlJobLibraService flinkSqlJobLibraService;
    private final FlinkSqlLineageMapper flinkSqlLineageMapper;
    private final SqlInstanceService sqlInstanceService;
    private final ReDataSourceMetadataService reDataSourceMetadataService;

    @Value("${kafka.alarm.ip}")
    private String ip;

    @Value("${kafka.alarm.topic}")
    private String alarmTopic;

    @Override
    public Map<String, Object> queryAll(FlinkSqlLineageQueryCriteria criteria, Pageable pageable) {
        Page<FlinkSqlLineage> page = flinkSqlLineageRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(flinkSqlLineageMapper::toDto));
    }

    @Override
    public List<FlinkSqlLineageDto> queryAll(FlinkSqlLineageQueryCriteria criteria) {
        return flinkSqlLineageMapper.toDto(flinkSqlLineageRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public FlinkSqlLineageDto findById(Long id) {
        FlinkSqlLineage flinkSqlLineage = flinkSqlLineageRepository.findById(id).orElseGet(FlinkSqlLineage::new);
        ValidationUtil.isNull(flinkSqlLineage.getId(), "FlinkSqlLineage", "id", id);
        return flinkSqlLineageMapper.toDto(flinkSqlLineage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FlinkSqlLineageDto create(FlinkSqlLineage resources) {
        return flinkSqlLineageMapper.toDto(flinkSqlLineageRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(FlinkSqlLineage resources) {
        FlinkSqlLineage flinkSqlLineage = flinkSqlLineageRepository.findById(resources.getId()).orElseGet(FlinkSqlLineage::new);
        ValidationUtil.isNull(flinkSqlLineage.getId(), "FlinkSqlLineage", "id", resources.getId());
        flinkSqlLineage.copy(resources);
        flinkSqlLineageRepository.save(flinkSqlLineage);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            flinkSqlLineageRepository.deleteById(id);
        }
    }

    @Transactional
    @Override
    public void deleteByJobNameAndProjectName(String jobName, String projectName) {
        flinkSqlLineageRepository.deleteByJobNameAndProjectName(jobName, projectName);
    }

    @Transactional
    @Override
    public void updateSqlLineIsDelete(String jobName) {
        flinkSqlLineageRepository.updateSqlLineIsDelete(jobName);
    }

    @Transactional
    @Override
    public void updateSqlLineStatus(FlinkSqlJobLibra flinkSqlJobLibra) {
        flinkSqlJobLibraService.update(flinkSqlJobLibra);
        //软删除
        this.updateSqlLineIsDelete(flinkSqlJobLibra.getTaskName());
    }


    @Transactional
    @Override
    public void updateSqlLineStrategy(List<SqlInstanceMapDto> sqlInstanceMapDtos, FlinkSqlJobLibraDto flinkSqlJobLibra, Set<FlinkSqlLineage> list) {
        this.deleteByJobNameAndProjectName(flinkSqlJobLibra.getTaskName(), flinkSqlJobLibra.getProjectName());
        //add
        Iterator<FlinkSqlLineage> iterator = list.iterator();
        while (iterator.hasNext()) {
            FlinkSqlLineage flinkSqlLineage = iterator.next();
            if (flinkSqlLineage.getTargetDbType().equalsIgnoreCase("print")
                    || flinkSqlLineage.getSourceDbType().equalsIgnoreCase("print")) {
                continue;
            }

            String targetOriginalInstance = flinkSqlLineage.getTargetOriginalInstance();
            String s = urlUnify(sqlInstanceMapDtos, targetOriginalInstance.replaceAll("\n", ""));
            if (s.length() > 199) {
                s = s.substring(0, 199);
            }
            if (targetOriginalInstance.length() > 499) {
                flinkSqlLineage.setTargetOriginalInstance(targetOriginalInstance.substring(0, 499));
            }
            flinkSqlLineage.setTargetInstance(s);

            String sourceOriginalInstance = flinkSqlLineage.getSourceOriginalInstance();
            s = urlUnify(sqlInstanceMapDtos, sourceOriginalInstance.replaceAll("\n", ""));
            if (s.length() > 199) {
                s = s.substring(0, 199);
            }
            if (sourceOriginalInstance.length() > 499) {
                flinkSqlLineage.setSourceOriginalInstance(sourceOriginalInstance.substring(0, 499));
            }
            flinkSqlLineage.setSourceInstance(s);

            flinkSqlLineage.setCreator(flinkSqlJobLibra.getOperator());
            flinkSqlLineage.setJobName(flinkSqlJobLibra.getTaskName());
            flinkSqlLineage.setProjectName(flinkSqlJobLibra.getProjectName());
            this.create(flinkSqlLineage);
        }
    }

    @Transactional
    @Override
    public void isGrcCheck(String tableId) {
        List<SqlInstanceMapDto> sqlInstanceMapList = sqlInstanceService.queryAll(new SqlInstanceMapQueryCriteria());

        // 拿到不合规表的信息
        List<Map<String, Object>> controlTableDataSourceInfo;
        if (StringUtils.isNotBlank(tableId)) {
            controlTableDataSourceInfo = reDataSourceMetadataService.findControlTableDataSourceInfo(tableId);
        } else {
            controlTableDataSourceInfo = reDataSourceMetadataService.findControlTableDataSourceInfo();
        }

        for (int i = 0; i < controlTableDataSourceInfo.size(); i++) {
            Map<String, Object> map = controlTableDataSourceInfo.get(i);
            String url = urlUnify(sqlInstanceMapList, (String) map.get("ip"));
            String tableName = (String) map.get("table_name");

            log.info("查询管控topic下游作业情况:tableName:{},url:{}", tableName, url);
            List<String> jobNames = this.queryDownStreamJobBySourceTable(tableName, "", url, "kafka");
            log.info("查询管控topic下游作业情况:jobNames:{}", jobNames);
            //查询这些作业是否合规，不合规则告警。
            List<FlinkSqlJobLibra> libraList = flinkSqlJobLibraService.queryControlJob(jobNames);
            for (int j = 0; j < libraList.size(); j++) {
                FlinkSqlJobLibra flinkSqlJobLibra = libraList.get(j);
                String isGrc = flinkSqlJobLibra.getIsGrc();
                log.info("检测作业是否合规:{},isGrc;{}", flinkSqlJobLibra.getTaskName(), isGrc);
                if (!"1".equals(isGrc)) {
                    //不合规，向kafka发出告警信息
                    String mess = "任务:" + flinkSqlJobLibra.getTaskName() + " 使用管控表:" + tableName + " 的数据，请任务负责人"+flinkSqlJobLibra.getCreator()+"确认该任务是否合规，合规请去模板平台更改任务合规状态";
                    sendAlarmToKafka(mess);

                    if ("2".equals(isGrc)) {

                    } else {
                        // 然后设置为2
                        flinkSqlJobLibra.setIsGrc("2");
                        flinkSqlJobLibraService.update(flinkSqlJobLibra);
                    }
                }
            }
        }
    }

    @Transactional
    @Override
    public void isGrcCheck() throws Exception {
        List<SqlInstanceMapDto> sqlInstanceMapList = sqlInstanceService.queryAll(new SqlInstanceMapQueryCriteria());

        // 拿到人工管控的表，然后查询他的下游所有表
        List<Map<String, Object>> controlTableDataSourceInfo = reDataSourceMetadataService.findControlTableDataSourceInfo();

        for (int i = 0; i < controlTableDataSourceInfo.size(); i++) {
            Map<String, Object> map = controlTableDataSourceInfo.get(i);
            String url = urlUnify(sqlInstanceMapList, (String) map.get("ip"));
             url = "rt-kafka-bigdata-dwd-v2";
            String tableName = (String) map.get("table_name");
             tableName = "dwd_sub_order_with_dims";
            log.info("查询管控topic下游作业情况:tableName:{},url:{}", tableName, url);

            HashSet<String> hashSet = new HashSet<>();
            HashSet<String> jobNames = new HashSet<>();
            HashSet<String> targetTables = new HashSet<>();
            //查询管控表的下游表
            recursionDownTable(url, tableName, hashSet, jobNames, targetTables);

            //查询这些作业是否合规，不合规则告警。
            List<FlinkSqlJobLibra> libraList = flinkSqlJobLibraService.queryControlJob(new ArrayList<>(jobNames));
            for (int j = 0; j < libraList.size(); j++) {
                FlinkSqlJobLibra flinkSqlJobLibra = libraList.get(j);
                String taskName = flinkSqlJobLibra.getTaskName();
                String isGrc = flinkSqlJobLibra.getIsGrc();
                if (!"1".equals(isGrc)) {
                    //不合规，向kafka发出告警信息
                    String mess = "任务:" + taskName + " 使用管控表:" + tableName + " 的数据，请任务负责人"+flinkSqlJobLibra.getCreator()+"确认该任务是否合规，合规请去模板平台更改任务合规状态";
                    sendAlarmToKafka(mess);

                    if ("2".equals(isGrc)) {

                    } else {
                        // 然后设置为2
                        flinkSqlJobLibra.setIsGrc("2");
                        flinkSqlJobLibraService.update(flinkSqlJobLibra);
                    }
                }
            }
        }
    }


    public void sendAlarmToKafka(String mess) {
        Map<String,Object> map=new HashMap(3);
        map.put("timestamp",System.currentTimeMillis());
        map.put("identifier","realtime-platform-quartz-task");
        Map<String,Object> m=new HashMap(1);
        m.put("warn",mess);
        map.put("carrier",m);
        System.out.println(map);
        log.info(map.toString());

        Properties props = new Properties();
        props.put("bootstrap.servers", ip);
        props.put("acks", "all");
        props.put("retries", 3);
        props.put("max.in.flight.requests.per.connection", 1);
        props.put("batch.size", 16384);
        props.put("linger.ms", 1);
        props.put("buffer.memory", 33554432);
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        Producer<String, String> producer = new KafkaProducer<>(props);
        producer.send(new ProducerRecord<String, String>(alarmTopic, "jobName", JSON.toJSONString(map)), new Callback() {
            @Override
            public void onCompletion(RecordMetadata metadata, Exception exception) {
                if (exception == null) {
                    log.info(" success-> offset:" + metadata.offset() + "  partiton:" + metadata.partition());
                } else {
                    exception.printStackTrace();
                }
            }
        });
        producer.close();
    }


    private void recursionDownTable(String url, String tableName, HashSet<String> hashSet, HashSet<String> jobNames, HashSet<String> targetTables) throws Exception {
        List<SqlLineageExtDto> kafka = this.querySqlLineBySourceTable(tableName, "", url, "kafka");
        for (int j = 0; j < kafka.size(); j++) {
            SqlLineageExtDto sqlLineageExtDto = kafka.get(j);
            String targetInstance = sqlLineageExtDto.getTargetInstance();
            String targetTable = sqlLineageExtDto.getTargetTable();
            String jobName = sqlLineageExtDto.getJobName();
            if("ads_interface_hard_direct_uuid_activate_metric".equalsIgnoreCase(jobName))
            {
                System.out.println("-");
            }
            String targetDbType = sqlLineageExtDto.getTargetDbType();
            if (targetDbType.equals("kafka")) {
                //ads_realtime_merchant_spu_metrics rt-kafka-bigdata-ads 循环依赖了
//                if (targetInstance.equals("rt-kafka-bigdata-ads")) {
//                    continue;
//                }
//                if(tableName.equals())

                String join = StringUtils.join(targetTable, " :=: ", targetInstance);
                if (targetTables.contains(join)) {
                    continue;
                }

                hashSet.add(StringUtils.join(jobName, " :=: ", targetTable, " :=: ", targetInstance));
                targetTables.add(StringUtils.join(targetTable, " :=: ", targetInstance));
                jobNames.add(jobName);
                log.info(StringUtils.join(jobName, " :=: ", targetTable, " :=: ", targetInstance));
//                if(targetTable.equals("dwd_increasea_media_direct_business_attribution_device")) {
//                if(targetTable.equals("dwd_increasea_media_business_attribution_device")) {
//                if(targetTable.equals("dwd_dewu_increase_action_device")) {
                if(targetTable.equals("dwd_adv_new_device_action")) {
//                if(targetTable.equals("dwd_traffic_attribution_trade_order")) {
                    System.out.println("-");
                }
                recursionDownTable(targetInstance, targetTable, hashSet, jobNames, targetTables);
            }
        }
    }


    public String urlUnify(List<SqlInstanceMapDto> sqlInstanceMaps, String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }
        String[] split = url.split(",");
        for (int i = 0; i < sqlInstanceMaps.size(); i++) {
            SqlInstanceMapDto sqlInstanceMap = sqlInstanceMaps.get(i);
            String instanceAll = sqlInstanceMap.getInstanceAll();
            for (int j = 0; j < split.length; j++) {
                if (instanceAll.toLowerCase().trim().contains(split[j].toLowerCase().trim())) {
                    return sqlInstanceMap.getInstanceUnique();
                }
            }
        }
        return url;
    }

    @Override
    public void download(List<FlinkSqlLineageDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (FlinkSqlLineageDto flinkSqlLineage : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("来源物理表", flinkSqlLineage.getSourceTable());
            map.put("来源视图表", flinkSqlLineage.getSourceViewTable());
            map.put("来源数据库", flinkSqlLineage.getSourceDatabase());
            map.put("来源数据库类型", flinkSqlLineage.getSourceDbType());
            map.put("目标表", flinkSqlLineage.getTargetTable());
            map.put("目标视图表", flinkSqlLineage.getTargetViewTable());
            map.put("目标数据库", flinkSqlLineage.getTargetDatabase());
            map.put("目标数据库类型", flinkSqlLineage.getTargetDbType());
            map.put("作业名称", flinkSqlLineage.getJobName());
            map.put("创建时间", flinkSqlLineage.getCreateTime());
            map.put("作业归属人", flinkSqlLineage.getCreator());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<TableLineageDto> likeDistinctByTableName(String tableName) throws Exception {
        List<Map<String, Object>> list = flinkSqlLineageRepository.likeDistinctByTableName(tableName);
        List<TableLineageDto> beanList = new ArrayList<>();
        for (Map<String, Object> map : list) {
            TableLineageDto bean = new TableLineageDto();
            BeanUtils.populate(bean, map);
            beanList.add(bean);
        }
        return beanList;
    }

    @Override
    public List<TableLineageDto> likeDistinctByTableNameForAssertLink(String tableName) throws Exception {
        List<Map<String, Object>> list = flinkSqlLineageRepository.likeDistinctByTableNameForAssertLink(tableName);
        List<TableLineageDto> beanList = new ArrayList<>();
        for (Map<String, Object> map : list) {
            TableLineageDto bean = new TableLineageDto();
            BeanUtils.populate(bean, map);
            beanList.add(bean);
        }
        return beanList;
    }

    @Override
    public List<Map> likeDistinctByTargetTable(String tableName) {
        return flinkSqlLineageRepository.likeDistinctByTargetTable(tableName);
    }

    @Override
    public List<SqlLineageExtDto> querySqlLine(String tableName, String database, String tableInstance, String dbType) throws Exception {
        List<SqlLineageExtDto> list1 = this.querySqlLineBySourceTable(tableName, database, tableInstance, dbType);
        List<SqlLineageExtDto> list2 = this.querySqlLineByTargetTable(tableName, database, tableInstance, dbType);
        list1.addAll(list2);
        return list1;

    }

    @Override
    public List<SqlLineageExtDto> querySqlLineBySourceTable(String tableName, String database, String tableInstance, String dbType) throws Exception {
        List<Map> list = flinkSqlLineageRepository.querySqlLineBySourceTable(tableName, database, tableInstance, dbType);
        List<SqlLineageExtDto> beanList = getSqlLineageExtDtos(list);
        return beanList;
    }

    @Override
    public List<SqlLineageExtDto> querySqlLineByTargetTable(String tableName, String database, String tableInstance, String dbType) throws Exception {
        List<Map> list = flinkSqlLineageRepository.querySqlLineByTargetTable(tableName, database, tableInstance, dbType);
        List<SqlLineageExtDto> beanList = getSqlLineageExtDtos(list);
        return beanList;
    }

    private List<SqlLineageExtDto> getSqlLineageExtDtos(List<Map> list) throws IllegalAccessException, InvocationTargetException {
        List<SqlLineageExtDto> beanList = new ArrayList<>();
        for (Map<String, Object> map : list) {
            SqlLineageExtDto bean = new SqlLineageExtDto();
            BeanUtils.populate(bean, map);
            beanList.add(bean);
        }
        return beanList;
    }

    @Override
    public List<String> queryUpStreamJobByTargetTable(String tableName, String database, String tableInstance, String dbType) {
        return flinkSqlLineageRepository.queryUpStreamJobByTargetTable(tableName, database, tableInstance, dbType);
    }

    @Override
    public List<String> queryDownStreamJobBySourceTable(String tableName, String database, String tableInstance, String dbType) {
        return flinkSqlLineageRepository.queryDownStreamJobBySourceTable(tableName, database, tableInstance, dbType);
    }


    @Override
    public List<FlinkSqlLineage> queryJobLine(String jobName) {
        List<FlinkSqlLineage> flinkSqlLineageList = this.querySqlLineByJobName(jobName);
        Set<String> upStreamJob=new HashSet<>();
        Set<String> downStreamJob=new HashSet<>();
        for (int i = 0; i < flinkSqlLineageList.size(); i++) {
            FlinkSqlLineage sqlLineage = flinkSqlLineageList.get(i);
            //根据这个作业的source表，查询他作为下游表的作业，就是查询这个作业的上游作业
            List<String> list1 = this.queryUpStreamJobByTargetTable(sqlLineage.getSourceTable(), sqlLineage.getSourceDatabase(), sqlLineage.getSourceInstance(), sqlLineage.getSourceDbType());
            upStreamJob.addAll(list1);
            // 下游作业同理
            List<String> list2 = this.queryDownStreamJobBySourceTable(sqlLineage.getTargetTable(), sqlLineage.getTargetDatabase(), sqlLineage.getTargetInstance(), sqlLineage.getTargetDbType());
            downStreamJob.addAll(list2);
        }
        upStreamJob.remove(jobName);
        downStreamJob.remove(jobName);
        return null;
    }


    @Override
    public List<FlinkSqlLineage> querySqlLineByJobName(String jobName) {
        List<FlinkSqlLineage> list = flinkSqlLineageRepository.querySqlLineByJobName(jobName);
        return list;
    }

    @Override
    public List<FlinkSqlLineage> queryJobLineByTargetJobName(String jobName) {
        List<FlinkSqlLineage> flinkSqlLineageList = this.querySqlLineByJobName(jobName);
        Set<String> upStreamJob=new HashSet<>();
        for (int i = 0; i < flinkSqlLineageList.size(); i++) {
            FlinkSqlLineage sqlLineage = flinkSqlLineageList.get(i);
            List<String> list1 = this.queryUpStreamJobByTargetTable(sqlLineage.getSourceTable(), sqlLineage.getSourceDatabase(), sqlLineage.getSourceInstance(), sqlLineage.getSourceDbType());
            upStreamJob.addAll(list1);
        }
        upStreamJob.remove(jobName);
        return null;
    }

    @Override
    public List<FlinkSqlLineage> queryJobLineBySourceJobName(String jobName) {
        List<FlinkSqlLineage> flinkSqlLineageList = this.querySqlLineByJobName(jobName);
        Set<String> downStreamJob=new HashSet<>();
        for (int i = 0; i < flinkSqlLineageList.size(); i++) {
            FlinkSqlLineage sqlLineage = flinkSqlLineageList.get(i);
            List<String> list2 = this.queryDownStreamJobBySourceTable(sqlLineage.getTargetTable(), sqlLineage.getTargetDatabase(), sqlLineage.getTargetInstance(), sqlLineage.getTargetDbType());
            downStreamJob.addAll(list2);
        }
        downStreamJob.remove(jobName);
        return null;
    }
}