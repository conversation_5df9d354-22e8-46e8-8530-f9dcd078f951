package com.dewu.property.sql.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.*;
import com.aliyuncs.exceptions.ClientException;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

@Slf4j
public class OssUtils {

//    public static String PRE_DIR="/Users/<USER>/logs/rpp/";
    public static String PRE_DIR="/opt/oss/udx/";

    public static void downloadUdxJar(String objectName, String accessKeyId, String secretAccessKey) {
        log.info("secretAccessKey:"+secretAccessKey.substring(0,5));
        String dir = OssUtils.getDir(objectName);
        log.info("dir:{}",log);
        OssUtils.mkdirs(PRE_DIR+dir);

        String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
        String bucketName = "bigdata-libra-bucket";

        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, secretAccessKey);
            // 下载Object到本地文件，并保存到指定的本地路径中。如果指定的本地文件存在会覆盖，不存在则新建。
            // 如果未指定本地路径，则下载后的文件默认保存到示例程序所属项目对应本地路径中。
            ossClient.getObject(new GetObjectRequest(bucketName, objectName), new File(PRE_DIR+ objectName));
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (Exception ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }

    }


    public static void mkdirs(String dir) {
        File file = new File(dir);
        // 判断文件夹是否存在
        if (!file.exists()) {
            // 创建文件夹
            boolean result = file.mkdirs();
            if (result) {
                log.info("文件夹创建成功！");
            } else {
                log.info("文件夹创建失败！");
            }
        } else {
            log.info("文件夹已存在！");
        }
    }

    public static String getDir(String dir) {
        int lastSlashIndex = dir.lastIndexOf("/");
        String parentFolder = null;
        if (lastSlashIndex > 0) {
            parentFolder = dir.substring(0, lastSlashIndex);
        }
        return parentFolder;
    }





    public static void main(String[] args) throws ClientException {
        // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。关于其他Region对应的Endpoint信息，请参见访问域名和数据中心。
        String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
        // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
//        EnvironmentVariableCredentialsProvider credentialsProvider = CredentialsProviderFactory.newEnvironmentVariableCredentialsProvider();

        // 填写Bucket名称，例如examplebucket。
        String bucketName = "bigdata-libra-bucket";
        // 填写Object完整路径，例如exampledir/exampleobject.txt。Object完整路径中不能包含Bucket名称。
        String objectName = "debug/test.json";


        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, "", "");

        try {
            // 下载Object到本地文件，并保存到指定的本地路径中。如果指定的本地文件存在会覆盖，不存在则新建。
            // 如果未指定本地路径，则下载后的文件默认保存到示例程序所属项目对应本地路径中。
            ossClient.getObject(new GetObjectRequest(bucketName, objectName), new File("/Users/<USER>/logs/rpp/examplefile1.txt"));
//            ossClient.getObject(new GetObjectRequest(bucketName, objectName), new File("/Users/<USER>/logs/rpp/"+objectName));
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (Exception ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

}
