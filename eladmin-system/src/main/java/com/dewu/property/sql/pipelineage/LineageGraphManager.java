package com.dewu.property.sql.pipelineage;

import com.dewu.exception.LineageException;
import com.dewu.property.sql.lineage.service.FlinkSqlLineageService;
import com.dewu.property.sql.lineage.service.dto.FlinkSqlLineageDto;
import com.dewu.property.sql.lineage.service.dto.FlinkSqlLineageQueryCriteria;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.util.Preconditions;
import org.jgrapht.Graph;
import org.jgrapht.GraphPath;
import org.jgrapht.alg.shortestpath.AllDirectedPaths;
import org.jgrapht.graph.DefaultDirectedGraph;
import org.jgrapht.graph.DefaultEdge;
import org.jgrapht.traverse.DepthFirstIterator;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Description: 血缘Graph中心管理服务
 * @author: zhiping.lin
 * @date: 2024/7/8
 */
@Slf4j
@Service
public class LineageGraphManager {

	private final JobInfoCache jobInfoCache;

	private final FlinkSqlLineageService flinkSqlLineageService;

	private final LineageKafkaWriter lineageKafkaWriter;

	Graph<TableNode, TableEdge> inMemGraph;
	AllDirectedPaths<TableNode, TableEdge> directedPathHandler;
	private String currStatusVersion = "";
	private String currLineageVersion = "";
	private String lastAssetsVersion = "";

	private final Object statusLock = new Object();
	private final Object lineageLock = new Object();

	// 公共root节点(虚拟)
	TableNode root;
	// 公共叶子节点(虚拟)
	TableNode leaf;

	public static HashMap<String, Node> inputCache = new HashMap<>();
	public static HashMap<String, Node> outputCache = new HashMap<>();
	static Map<TableNode, TableNode> tables = new HashMap<>();
	static Map<TableEdge, TableEdge> tableEdges = new HashMap<>();
	static Map<JobNode, JobNode> jobs = new HashMap<>();
	List<FlinkSqlLineageDto> flinkSqlLineageUnits = new ArrayList<>();
	ObjectMapper objectMapper = new ObjectMapper();

	public LineageGraphManager(JobInfoCache jobInfoCache, FlinkSqlLineageService flinkSqlLineageService, LineageKafkaWriter lineageKafkaWriter) {
		this.jobInfoCache = jobInfoCache;
		this.flinkSqlLineageService = flinkSqlLineageService;
		this.lineageKafkaWriter = lineageKafkaWriter;
		this.initOrReloadGraph();
		this.reCalcDelayForGraph();
	}

	// trigger : 每小时更新1次血缘依赖信息
	// 核心血缘数据,需pipeline化
	public void initOrReloadGraph() {
		String currentHour = LineageUtils.getCurrentHour();
		log.info("LGM : try to update lineage[{}] from [{}].", currentHour, currLineageVersion);
		if (!currentHour.equals(currLineageVersion)) {
			synchronized (lineageLock) {
				if (!currentHour.equals(currLineageVersion)) {
					log.info("LGM : Begin to update lineage[{}] from [{}].", currentHour, currLineageVersion);
					FlinkSqlLineageQueryCriteria flinkSqlLineageQueryCriteria = new FlinkSqlLineageQueryCriteria();
					flinkSqlLineageQueryCriteria.setIsDelete("0");
					List<String> projects = new ArrayList<>();
					projects.add("tech-data-dw2");
					projects.add("dw-rt-2");
					flinkSqlLineageQueryCriteria.setProjectName(projects);
					flinkSqlLineageUnits = flinkSqlLineageService.queryAll(flinkSqlLineageQueryCriteria);
					inMemGraph = parseLineageGraph(flinkSqlLineageUnits);
					directedPathHandler = new AllDirectedPaths<>(inMemGraph);
					reCalcDelayForGraph();
					currLineageVersion = currentHour;
					log.info("LGM : lineage[{}] updated.", currentHour);
				}
			}
		} else {
			log.info("LGM : lineage[{}] is already updated.", currLineageVersion);
		}

	}

	// trigger : 落地资产delay入口。暴露功能给外部调度，支持历史状态数据重刷。血缘无需支持历史回溯
	// 需要控制并发量，并发过大有OOM风险
	public void reCalcDelayForGraph(String ctime) {
		log.info("LGM : Begin to generate assets[{}].", ctime);
		String ctimeBucket = LineageUtils.getTimeBucket(ctime);
		if (ctimeBucket.equals(lastAssetsVersion)) {
			log.info("LGM : Assets[{}] already generated for bucket[{}].", ctime, lastAssetsVersion);
			return;
		}
		String currTimeBucket = StringUtils.isEmpty(currStatusVersion) ? currStatusVersion : LineageUtils.getTimeBucket(currStatusVersion);
		// 时间周期必须不一致，这里需要额外复制新的graph来计算历史状态
		if (StringUtils.isEmpty(currStatusVersion) || timeBucketCompare(ctimeBucket, currTimeBucket) < 0) {
			long start = System.currentTimeMillis();
			Graph<TableNode, TableEdge> snapshot = cloneGraph(inMemGraph);
			Set<String> jobs = snapshot.edgeSet().stream().filter(e -> e != null && e.getJob() != null && e.getJob().jobName != null).map(e -> e.getJob().jobName).collect(Collectors.toSet());
			jobInfoCache.initCache(jobs, ctimeBucket);
			log.info("LGM : fetch jobDelay[{}] takes about {} ms.", ctimeBucket, System.currentTimeMillis() - start);
			refreshDelayForGraph(snapshot, ctimeBucket);
			genAssetsDelay(snapshot, ctimeBucket);
			jobInfoCache.releaseCache(ctimeBucket);
			log.info("LGM : Assets[{}] generated.", ctimeBucket);
		} else {
			throw new LineageException(String.format("LGM : Assets[%s] generate failed with currTimeBucket[%s].", ctimeBucket, currTimeBucket));
		}
	}

	// trigger : 每分钟定时更新当前jobCache和graph的状态。
	// reload : 重置graph会尝试更新当前jobCache和graph的状态。
	// 核心状态数据,需pipeline化。暂时先关闭，如有性能问题再打开
	public void reCalcDelayForGraph() {
		// 更新1分钟前的数据，防止hbase数据未及时更新
		String currentMinute = LineageUtils.getTimeBucket(System.currentTimeMillis() / 1000 - 60);
		log.info("LGM : Try to re-calc graph[{}] delay.", currentMinute);
		if (!currStatusVersion.equals(currentMinute)) {
			synchronized (statusLock) {
				if (!currStatusVersion.equals(currentMinute)) {
					String currTimeBucket = LineageUtils.getTimeBucket(currentMinute);
					log.info("LGM : Begin to update graphDelay[{}] from [{}].", currentMinute, currStatusVersion);
//					reloadJobInfoMetrics(currTimeBucket);
//					refreshDelayForGraph(inMemGraph, currTimeBucket);
					currStatusVersion = currentMinute;
					log.info("LGM : Graph[{}] updated", currentMinute);
				}
			}
		} else {
			log.info("LGM : Graph[{}] is already up to date.", currStatusVersion);
		}
	}

	private int timeBucketCompare(String time1, String time2) {
		try {
			Date date1 = new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(time1);
			Date date2 = new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(time2);
			if (date1.getTime() > date2.getTime()) {
				return 1;
			} else if (date1.getTime() < date2.getTime()) {
				return -1;
			}
			return 0;
		} catch (Exception e) {
			throw new LineageException(String.format("LGM : time bucket compare failed with [%s] and [%s].", time1, time2), e);
		}
	}

	private Graph<TableNode, TableEdge> parseLineageGraph(List<FlinkSqlLineageDto> flinkSqlLineageDtos) {
		AtomicInteger idGenerator = new AtomicInteger();
		Graph<TableNode, TableEdge> g = new DefaultDirectedGraph<>(TableEdge.class);
		root = TableNode.of("lineage_root_vertex", "root", "root", idGenerator.getAndIncrement());
		leaf = TableNode.of("lineage_leaf_vertex", "leaf", "leaf", idGenerator.getAndIncrement());
		g.addVertex(root);
		g.addVertex(leaf);
		for (FlinkSqlLineageDto flinkSqlLineageDto : flinkSqlLineageDtos) {
			TableNode source = getTableNode(TableNode.of(flinkSqlLineageDto.getSourceTable(), flinkSqlLineageDto.getSourceDatabase(), flinkSqlLineageDto.getSourceInstance(), idGenerator.getAndIncrement()));
			TableNode target = getTableNode(TableNode.of(flinkSqlLineageDto.getTargetTable(), flinkSqlLineageDto.getTargetDatabase(), flinkSqlLineageDto.getTargetInstance(), idGenerator.getAndIncrement()));
			JobNode job = getJobNode(new JobNode(flinkSqlLineageDto.getJobName(), flinkSqlLineageDto.getProjectName()));
			TableEdge tableEdge = getTableEdge(new TableEdge(job, source, target));
			if (!g.containsVertex(source)) {
				g.addVertex(source);
			}
			if (!g.containsVertex(target)) {
				g.addVertex(target);
			}
			if (!source.equals(target)) {
				g.addEdge(source, target, tableEdge);
			}
		}

		g.vertexSet().forEach(v -> {
			try {
				if (g.inDegreeOf(v) == 0 && !v.equals(root) && !v.equals(leaf)) {
					g.addEdge(root, v, new TableEdge(null, root, v));
				}
				if (g.outDegreeOf(v) == 0 && !v.equals(root) && !v.equals(leaf)) {
					g.addEdge(v, leaf, new TableEdge(null, v, leaf));
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		});
		TableNode.setTableNum(g.vertexSet().size());
		Set<TableEdge> removeEdges = new HashSet<>();
		markDepthAndBreakCycleFromRoot(g, root, removeEdges);
		removeEdges.forEach(tableEdge -> System.out.println("rm edge " + tableEdge.getSource().getTableName() + " -> " + tableEdge.getTarget().getTableName()));
		removeEdges.forEach(g::removeEdge);
		return g;
	}


	private void reloadJobInfoMetrics(String ctimeBucket) {
		log.info("LGM : Begin to reload jobInfo[{}]", ctimeBucket);
		Set<String> jobs = flinkSqlLineageUnits.stream().map(FlinkSqlLineageDto::getJobName).collect(Collectors.toSet());
		jobInfoCache.refreshAll(jobs, ctimeBucket);
		log.info("LGM : Finish to reload jobInfo[{}]", ctimeBucket);
	}

	public String fetchGraph(TableNode node) {
		return fetchGraph(node, null);
	}

	public String fetchGraph(TableNode node, String ctime) {
		return fetchSubLineageGraph(LineageUtils.getTimeBucket(ctime), node, root, leaf);
	}

	private String fetchSubLineageGraph(String ctimeBucket, TableNode node, TableNode root, TableNode leaf) {
		// 记录每层节点有多少个
		Map<Integer, Set<TableNode>> depthNodes = new HashMap<>();
		// 记录所有的边，用于整合job信息
		Set<TableEdge> edges = new HashSet<>();
		Set<String> jobs = inMemGraph.edgeSet().stream().filter(e -> e != null && e.getJob() != null && e.getJob().jobName != null).map(e -> e.getJob().jobName).collect(Collectors.toSet());
		Graph<TableNode, TableEdge> tmp = fetchSubLineage(node, root, leaf, depthNodes, edges);
		collectVertexAndEdge(tmp.edgeSet(), depthNodes);
		if (StringUtils.isNotEmpty(ctimeBucket)) {
			jobInfoCache.initCache(jobs, ctimeBucket);
			refreshDelayForGraph(tmp, ctimeBucket);
			jobInfoCache.releaseCache(ctimeBucket);
		}

		// 用于定位节点位置作图(++)
		Map<Integer, AtomicInteger> depthCount = new HashMap<>();
		// 用于记录节点位置
		Map<TableNode, Tuple2<Integer, Integer>> graphNodes = new HashMap<>();

		int graphWidth = depthNodes.size() * 900;
		int graphHeight = depthNodes.values().stream().max(Comparator.comparingInt(Set::size)).get().size() * 300;
		if (graphHeight * 3 > graphWidth) {
			graphWidth = graphHeight * 3;
		} else {
			graphHeight = graphWidth / 3;
		}
		int heightGap = graphHeight / depthNodes.size();

		DepthFirstIterator<TableNode, TableEdge> iterator = new DepthFirstIterator<>(tmp);
		while (iterator.hasNext()) {
			TableNode next = iterator.next();
			if (!root.equals(next) && !leaf.equals(next)) {
				int index = depthCount.computeIfAbsent(next.getDepth(), k -> new AtomicInteger()).incrementAndGet();
				graphNodes.put(next, new Tuple2<>((next.getDepth() - 1) * graphWidth / depthNodes.size(), (index) * graphHeight / (depthNodes.get(next.getDepth()).size() + 1)));
			}
		}
		SimpleUIGraph simpleUIGraph = new SimpleUIGraph();
		simpleUIGraph.setSearchNode(tmp.outgoingEdgesOf(node).stream().map(e -> e.source).findAny().orElse(node).getSimpleString());
		List<GraphNode> nodes = new ArrayList<>();
		List<GraphEdge> links = new ArrayList<>();
		graphNodes.forEach((k, v) -> {
			if (!root.equals(k) && !leaf.equals(k)) {
				GraphNode graphNode = new GraphNode();
				graphNode.setX(v.f0);
				graphNode.setY(v.f1);
				graphNode.setName(k.getSimpleString());
				graphNode.getValue().put("delay", String.valueOf(k.getDelay()));
				graphNode.getValue().put("table", k.getUniqString());
				nodes.add(graphNode);
			}
		});
		edges.forEach(edge -> {
			if (!root.equals(edge.getSource()) && !leaf.equals(edge.getTarget())) {
				GraphEdge graphEdge = new GraphEdge();
				graphEdge.setSource(edge.getSource().getSimpleString());
				graphEdge.setTarget(edge.getTarget().getSimpleString());
				graphEdge.getEdgeValue().put("job", edge.getJob().getUniqString());
				graphEdge.getEdgeValue().put("delay", edge.getDelay().toString());
				links.add(graphEdge);
			}
		});
		simpleUIGraph.setLinks(links);
		simpleUIGraph.setNodes(nodes);
		try {
			return objectMapper.writeValueAsString(simpleUIGraph);
		} catch (JsonProcessingException e) {
			throw new LineageException("simpleUIGraph json build failed.", e);
		}
	}

	private Graph<TableNode, TableEdge> fetchSubLineage(TableNode node, TableNode root, TableNode leaf, Map<Integer, Set<TableNode>> depthNodes, Set<TableEdge> edges) {
		List<GraphPath<TableNode, TableEdge>> leaves = directedPathHandler.getAllPaths(node, leaf, true, 100);
		List<GraphPath<TableNode, TableEdge>> roots = directedPathHandler.getAllPaths(root, node, true, 100);
//		collectVertexAndEdge(leaves, depthNodes, edges);
//		collectVertexAndEdge(roots, depthNodes, edges);
		return buildTmpGraph(leaves, roots, leaf, root, edges);
	}

	private Graph<TableNode, TableEdge> buildTmpGraph(List<GraphPath<TableNode, TableEdge>> leaves, List<GraphPath<TableNode, TableEdge>> roots, TableNode leaf, TableNode root, Set<TableEdge> edges) {
		Graph<TableNode, TableEdge> tmp = new DefaultDirectedGraph<>(TableEdge.class);
		Map<TableNode, TableNode> tmpNodes = new HashMap<>();
		Map<TableEdge, TableEdge> tmpEdges = new HashMap<>();
		List<GraphPath<TableNode, TableEdge>> allEdges = new ArrayList<>();
		allEdges.addAll(roots);
		allEdges.addAll(leaves);
		allEdges.forEach(path -> path.getEdgeList().forEach(edge -> {
			TableNode source = tmpNodes.computeIfAbsent(edge.getSource(), k -> edge.getSource().copyTmp());
			TableNode target = tmpNodes.computeIfAbsent(edge.getTarget(), k -> edge.getTarget().copyTmp());
			TableEdge tmpEdge = new TableEdge(edge.getJob(), source, target);
			TableEdge tableEdge = tmpEdges.computeIfAbsent(tmpEdge, k -> tmpEdge);
			tmp.addVertex(source);
			tmp.addVertex(target);
			tmp.addEdge(source, target, tableEdge);
		}));
		markDepthAndBreakCycleFromRoot(tmp, root, new HashSet<>());
		edges.addAll(tmpEdges.values());
		return tmp;
	}

	private static void collectVertexAndEdge(List<GraphPath<TableNode, TableEdge>> roots, Map<Integer, Set<TableNode>> depthNodes, Set<TableEdge> edges) {
		roots.forEach(path -> path.getEdgeList().forEach(edge -> {
			depthNodes.computeIfAbsent(edge.getTarget().getDepth(), k -> new HashSet<>()).add(edge.getTarget());
			depthNodes.computeIfAbsent(edge.getSource().getDepth(), k -> new HashSet<>()).add(edge.getSource());
		}));
	}

	private static void collectVertexAndEdge(Set<TableEdge> edges, Map<Integer, Set<TableNode>> depthNodes) {
		edges.forEach(edge -> {
			depthNodes.computeIfAbsent(edge.getTarget().getDepth(), k -> new HashSet<>()).add(edge.getTarget());
			depthNodes.computeIfAbsent(edge.getSource().getDepth(), k -> new HashSet<>()).add(edge.getSource());
		});
	}

	private Graph<TableNode, TableEdge> cloneGraph(Graph<TableNode, TableEdge> old) {
		Graph<TableNode, TableEdge> tmp = new DefaultDirectedGraph<>(TableEdge.class);
		Map<TableNode, TableNode> tmpNodes = new HashMap<>();
		old.vertexSet().forEach(v -> tmp.addVertex(v.copyTmp()));
		old.edgeSet().forEach(edge -> {
			TableNode source = tmpNodes.computeIfAbsent(edge.getSource(), k -> edge.getSource().copyTmp());
			TableNode target = tmpNodes.computeIfAbsent(edge.getTarget(), k -> edge.getTarget().copyTmp());
			TableEdge tmpEdge = new TableEdge(edge.getJob(), source, target);
			tmp.addVertex(source);
			tmp.addVertex(target);
			tmp.addEdge(source, target, tmpEdge);
		});
		markDepthAndBreakCycleFromRoot(tmp, root, new HashSet<>());
		return tmp;
	}

	public void genAssetsDelay(Graph<TableNode, TableEdge> snapshot, String formattedTime) {
		Set<AssetDelayMetrics> sinks = snapshot.edgeSet().stream().map(TableEdge::getTarget).filter(v -> !v.equals(root) && !v.equals(leaf)).map(vert -> new AssetDelayMetrics(vert.getTableName(), vert.getDatabase(), vert.getInstance(), formattedTime, vert.getDelay(), System.currentTimeMillis())).collect(Collectors.toSet());
		Set<AssetDelayMetrics> sources = snapshot.edgeSet().stream().map(TableEdge::getSource).filter(v -> !v.equals(root) && !v.equals(leaf)).map(vert -> new AssetDelayMetrics(vert.getTableName(), vert.getDatabase(), vert.getInstance(), formattedTime, vert.getDelay(), System.currentTimeMillis())).collect(Collectors.toSet());
		Set<AssetDelayMetrics> tables = new HashSet<>();
		tables.addAll(sources);
		tables.addAll(sinks);
//		for (AssetDelayMetrics assetDelayMetrics : tables) {
//			log.info("LGM : asset gen : " + assetDelayMetrics);
//		}
		lineageKafkaWriter.sendMessMonitorStatus(tables);
		log.info("successfully send assets metrics to kafka.");
	}

	private void refreshDelayForGraph(Graph<TableNode, TableEdge> g, String ctimeBucket) {
		refreshDelayForNode(g, root, ctimeBucket);
	}

	private void refreshDelayForNode(Graph<TableNode, TableEdge> g, TableNode node, String ctimeBucket) {
//		log.info("LGM : Begin to update " + node.getTableName() + " delay.");
		// leaf 节点也有延迟。可以用来参考整个数仓链路最大延迟
		if (!node.equals(root)) {
			Preconditions.checkArgument(g.inDegreeOf(node) > 0, "LGM : 非根节点应该有输入");
			Double maxDelay = g.incomingEdgesOf(node).stream().filter(e -> !e.source.equals(root) && e.job != null).map(e -> {
				double jobDelay = jobInfoCache.getDelay(e.job.getJobName(), ctimeBucket);
				e.setDelay(jobDelay);
//				log.info("LGM : " + e.job.getJobName() + " delay " + jobDelay);
				return e.source.getDelay() + e.getDelay();
			}).reduce(Math::max).orElse(0.0);
			if (maxDelay > node.getDelay() || node.getDelay() == 0.0) {
				//非root节点只有在delay更新或者第一次初始化的时候才重新计算下游延迟
				node.setDelay(maxDelay);
//				log.info("LGM : update set " + node.getTableName() + " delay " + node.getDelay());
				g.outgoingEdgesOf(node).forEach(e -> refreshDelayForNode(g, e.getTarget(), ctimeBucket));
			}
		} else {
			// root节点直接计算下游延迟
			g.outgoingEdgesOf(node).forEach(e -> refreshDelayForNode(g, e.getTarget(), ctimeBucket));
		}
	}

	private void markDepthAndBreakCycleFromRoot(Graph<TableNode, TableEdge> g, TableNode node, Set<TableEdge> removeEdges) {
		if (!node.isInit()) {
			node.initMemSet();
		}
		if (g.outDegreeOf(node) == 0) {
			return;
		}
		g.outgoingEdgesOf(node).forEach(e -> {
			TableNode target = e.getTarget();
			if (!target.isInit()) {
				target.initMemSet();
			}
			if (node.isInMemSet(target.id)) {
				removeEdges.add(e);
			} else {
				target.setMemSet(node.getMemSet());
				target.setMemSet(node.getId());
				if (target.getDepth() <= node.getDepth()) {
					target.setDepth(node.getDepth() + 1);
				}
				markDepthAndBreakCycleFromRoot(g, target, removeEdges);
			}
		});
	}

	public static TableNode getTableNode(TableNode node) {
		if (tables.containsKey(node)) {
			return tables.get(node);
		}
		tables.put(node, node);
		return node;
	}

	public static TableEdge getTableEdge(TableEdge edge) {
		if (tableEdges.containsKey(edge)) {
			return tableEdges.get(edge);
		}
		tableEdges.put(edge, edge);
		return edge;
	}


	public static JobNode getJobNode(JobNode node) {
		if (jobs.containsKey(node)) {
			return jobs.get(node);
		}
		jobs.put(node, node);
		return node;
	}


	/**
	 * 寻找输出节点，并计算权重
	 *
	 * @param g    图
	 * @param node 定位节点
	 * @return node 定位节点
	 */
	public static Node findOutputLineage(Graph<String, DefaultEdge> g, Node node) {
		if (outputCache.containsKey(node.getName())) {
			return node;
		}
		node.getOutputs().addAll(g.outgoingEdgesOf(node.getName()).stream().map(g::getEdgeTarget).map(e -> findOutputLineage(g, new Node(e))).collect(Collectors.toList()));
		outputCache.put(node.getName(), node);
		node.calcWeight();
		return node;
	}

	/**
	 * 寻找输入节点，并计算权重
	 *
	 * @param g    图
	 * @param node 定位节点
	 * @return node 定位节点
	 */
	public static Node findInputLineage(Graph<String, DefaultEdge> g, Node node) {
		if (inputCache.containsKey(node.getName())) {
			return node;
		}
		node.getInputs().addAll(g.incomingEdgesOf(node.getName()).stream().map(g::getEdgeSource).map(e -> findInputLineage(g, new Node(e))).collect(Collectors.toList()));
		inputCache.put(node.getName(), node);
		node.calcWeight();
		return node;
	}

	@Data
	public static class Node {
		String name; // 节点名
		int weight = 1; // 节点权重
		float score; // 稳定分
		long delay; // 延迟
		Set<Node> inputs = new HashSet<>(); // 上游节点
		Set<Node> outputs = new HashSet<>(); // 下游节点

		public Node(String name) {
			this.name = name;
		}

		@Override
		public boolean equals(Object o) {
			if (this == o) return true;
			if (o == null || getClass() != o.getClass()) return false;
			Node node = (Node) o;
			return Objects.equals(name, node.name);
		}

		@Override
		public int hashCode() {
			return Objects.hash(name);
		}

		public void calcWeight() {
			int inputWeight = inputs.stream().map(Node::getWeight).reduce(Integer::sum).orElse(1);
			int outputWeight = outputs.stream().map(Node::getWeight).reduce(Integer::sum).orElse(1);
			weight = Math.max(inputWeight, outputWeight);
		}
	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class TableNodeReq {
		String tableName;
		String database;
		String instance;
		String ctime;
	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class TableNode {
		String tableName;
		String database;
		String instance;
		Double delay = 0.0;
		int depth;
		int id;
		BitSet memSet;

		static int tableNum = 0;

		public static TableNode of(String tableName, String database, String instance, int id) {
			TableNode node = new TableNode();
			node.tableName = null == tableName ? "" : tableName;
			node.database = null == database ? "" : database;
			node.instance = null == instance ? "" : instance;
			node.id = id;
			return node;
		}

		public static void setTableNum(int num) {
			tableNum = num;
		}

		public void initMemSet() {
			memSet = new BitSet(tableNum);
		}

		public boolean isInMemSet(int id) {
			return memSet.get(id);
		}

		public void setMemSet(int id) {
			memSet.set(id);
		}

		public void setMemSet(BitSet bitSet) {
			memSet.or(bitSet);
		}

		public boolean isInit() {
			return memSet != null;
		}

		@Override
		public boolean equals(Object o) {
			if (this == o) return true;
			if (o == null || getClass() != o.getClass()) return false;
			TableNode tableNode = (TableNode) o;
			return Objects.equals(tableName, tableNode.tableName) && Objects.equals(database, tableNode.database) && Objects.equals(instance, tableNode.instance);
		}

		@Override
		public int hashCode() {
			return Objects.hash(tableName, database, instance);
		}

		public TableNode copyTmp() {
			return TableNode.of(tableName, database, instance, id);
		}

		public String getUniqString() {
			return tableName + "(" + instance + "/" + database + ")";
		}

		public String getSimpleString() {
			return id + ":" + tableName;
		}
	}


	@Data
	@EqualsAndHashCode
	@AllArgsConstructor
	public static class JobNode {
		String jobName;
		String projectName;

		public String getUniqString() {
			return projectName + "/" + jobName;
		}
	}

	@Data
	public static class TableEdge {
		JobNode job;
		TableNode source;
		TableNode target;
		Double delay = 0.0;

		public TableEdge(JobNode job, TableNode source, TableNode target) {
			this.job = job;
			this.source = source;
			this.target = target;
		}

		@Override
		public boolean equals(Object o) {
			if (this == o) return true;
			if (o == null || getClass() != o.getClass()) return false;
			TableEdge tableEdge = (TableEdge) o;
			return Objects.equals(job, tableEdge.job) && Objects.equals(source, tableEdge.source) && Objects.equals(target, tableEdge.target);
		}

		@Override
		public int hashCode() {
			return Objects.hash(job, source, target);
		}
	}


	@Data
	@EqualsAndHashCode
	@AllArgsConstructor
	@NoArgsConstructor
	public static class GraphNode {
		String name;
		Map<String, String> value = new HashMap<>();
		int x;
		int y;
	}

	@Data
	@EqualsAndHashCode
	@AllArgsConstructor
	@NoArgsConstructor
	public static class GraphEdge {
		String source;
		String target;
		Map<String, String> EdgeValue = new HashMap<>();
	}

	@Data
	@EqualsAndHashCode
	@AllArgsConstructor
	@NoArgsConstructor
	public static class SimpleUIGraph {
		String searchNode;
		List<GraphNode> nodes;
		List<GraphEdge> links;
	}
}
