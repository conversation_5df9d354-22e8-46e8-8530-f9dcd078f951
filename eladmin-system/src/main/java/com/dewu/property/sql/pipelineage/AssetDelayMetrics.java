package com.dewu.property.sql.pipelineage;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2024/7/8
 */
@Data
@AllArgsConstructor
@ToString
public class AssetDelayMetrics {
	private String tableName;
	private String database;
	private String instance;
	private String timeUnit;
	private Double delay;
	private long updateMillis;
}
