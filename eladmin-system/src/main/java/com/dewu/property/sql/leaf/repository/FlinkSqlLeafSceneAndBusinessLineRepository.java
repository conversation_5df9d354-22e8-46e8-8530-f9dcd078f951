/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.leaf.repository;

import com.dewu.property.sql.leaf.domain.FlinkSqlLeafSceneAndBusinessLine;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-03-12
**/
public interface FlinkSqlLeafSceneAndBusinessLineRepository extends JpaRepository<FlinkSqlLeafSceneAndBusinessLine, Long>, JpaSpecificationExecutor<FlinkSqlLeafSceneAndBusinessLine> {

	@Modifying
	@Transactional
	@Query(value = "update flink_sql_leaf_scene_and_business_line set business_line=?3,scene=?4 where project_name=?1 and job_name=?2", nativeQuery = true)
	void updateBusinessLineAndSceneByProjectNameAndJobName(@Param("project_name") String projectName, @Param("job_name") String jobName,  @Param("business_line")String businessLine, @Param("scene")String scene);
}