/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.sql.leaf.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-03-12
**/
@Data
public class FlinkSqlLeafSceneAndBusinessLineDto implements Serializable {

    /** 自增id */
    private Long id;

    /** 作业名称 */
    private String jobName;

    /** 业务线 */
    private String businessLine;

    /** 场景 */
    private String scene;

    /** 创建时间 */
    private Timestamp createTime;

    /** 修改时间 */
    private Timestamp modifyTime;

    /** 作业归属人 */
    private String creator;

    /** 空间名称 */
    private String projectName;

    /** 是否删除 */
    private Integer isDelete;

    /** 是否合规，0无效，1不合规，2 合规 */
    private Integer isGrc;
}