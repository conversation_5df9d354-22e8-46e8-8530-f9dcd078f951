/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.sql.lineage.service.impl;

import com.dewu.property.sql.lineage.domain.SqlInstanceMap;
import com.dewu.property.sql.lineage.repository.SqlInstanceMapRepository;
import com.dewu.property.sql.lineage.service.SqlInstanceService;
import com.dewu.property.sql.lineage.service.dto.*;
import com.dewu.property.sql.lineage.service.mapstruct.SqlInstanceMapMapper;
import com.dewu.utils.PageUtil;
import com.dewu.utils.QueryHelp;
import com.dewu.utils.ValidationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2023-07-14
 **/
@Service
@RequiredArgsConstructor
public class SqlInstanceMapServiceImpl implements SqlInstanceService {

    private final SqlInstanceMapRepository sqlInstanceMapRepository;
    private final SqlInstanceMapMapper sqlInstanceMapMapper;

    @Override
    public Map<String, Object> queryAll(SqlInstanceMapQueryCriteria criteria, Pageable pageable) {
        Page<SqlInstanceMap> page = sqlInstanceMapRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(sqlInstanceMapMapper::toDto));
    }

    @Override
    public List<SqlInstanceMapDto> queryAll(SqlInstanceMapQueryCriteria criteria) {
        return sqlInstanceMapMapper.toDto(sqlInstanceMapRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public SqlInstanceMapDto findById(Long id) {
        SqlInstanceMap sqlInstanceMap = sqlInstanceMapRepository.findById(id).orElseGet(SqlInstanceMap::new);
        ValidationUtil.isNull(sqlInstanceMap.getId(), "sqlInstanceMap", "id", id);
        return sqlInstanceMapMapper.toDto(sqlInstanceMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SqlInstanceMapDto create(SqlInstanceMap resources) {
        return sqlInstanceMapMapper.toDto(sqlInstanceMapRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SqlInstanceMap resources) {
        SqlInstanceMap sqlInstanceMap = sqlInstanceMapRepository.findById(resources.getId()).orElseGet(SqlInstanceMap::new);
        ValidationUtil.isNull(sqlInstanceMap.getId(), "sqlInstanceMap", "id", resources.getId());
        sqlInstanceMap.copy(resources);
        sqlInstanceMapRepository.save(sqlInstanceMap);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            sqlInstanceMapRepository.deleteById(id);
        }
    }



}