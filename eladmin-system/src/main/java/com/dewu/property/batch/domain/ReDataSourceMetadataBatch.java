/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.batch.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * @website https://el-admin.vip
 * @description /
 * <AUTHOR>
 * @date 2022-06-09
 **/
@Data
public class ReDataSourceMetadataBatch implements Serializable {

    @ApiModelProperty(value = "消息类型")
    private String messType;

    @ApiModelProperty(value = "自增id")
    private Long tableId;

    @NotBlank
    @ApiModelProperty(value = "字段名")
    private String binlogText;

    public void copy(ReDataSourceMetadataBatch source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}

