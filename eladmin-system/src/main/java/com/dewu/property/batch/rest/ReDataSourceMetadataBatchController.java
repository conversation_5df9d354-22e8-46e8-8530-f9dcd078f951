/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.batch.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dewu.flink.rule.base.check.SqlParserUtils;
import com.dewu.flink.template.meta.tablefield.domain.ReDataSourceTableField;
import com.dewu.flink.template.meta.tablefield.service.ReDataSourceTableFieldService;
import com.dewu.flink.template.meta.tablefield.service.dto.ReDataSourceTableFieldDto;
import com.dewu.flink.template.meta.tableinfo.domain.ReDataSourceMetadata;
import com.dewu.property.batch.domain.ReDataSourceMetadataBatch;
import com.dewu.annotation.Log;
import com.dewu.property.metadata.service.ReDataSourceMetadataService;
import com.dewu.utils.BinlogParse;
import com.dewu.utils.Constant;
import lombok.RequiredArgsConstructor;
import net.sf.jsqlparser.statement.create.table.ColumnDefinition;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;

import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-06-09
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "一键导入管理")
@RequestMapping("/api/reDataSourceMetadataBatch")
public class ReDataSourceMetadataBatchController {

    private final ReDataSourceMetadataService reDataSourceMetadataService;

    private final ReDataSourceTableFieldService reDataSourceTableFieldService;


    @GetMapping
    @ApiOperation("查询一键导入")
    @PreAuthorize("@el.check('reDataSourceMetadataBatch:list')")
    public ResponseEntity<Object> queryReDataSourceMetadataBatch() {
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping
    @Log("新增一键导入")
    @ApiOperation("新增一键导入")
    @PreAuthorize("@el.check('reDataSourceMetadataBatch:add')")
    public ResponseEntity<Object> createReDataSourceMetadataBatch(@Validated @RequestBody ReDataSourceMetadataBatch resources) throws Exception {
        String mess = resources.getBinlogText();
        Long tableId = resources.getTableId();
        String dataType = resources.getMessType();

        ReDataSourceMetadata tableInfo = reDataSourceMetadataService.findByIdNoDto(tableId);

        HashMap<String, String> fieldNameAndTypeMap = new HashMap<>();
        if (dataType.equalsIgnoreCase(Constant.MessType.BINLOG)) {
            JSONObject jsonObject = JSON.parseObject(mess);
            String tableName = jsonObject.getString(BINLOG_SOURCE_TABLE);
            String tableN = tableInfo.getTableName();
//            if (!tableName.equals(tableN)) {
//                throw  new Exception("表ID和数据中表名不一致");
//            }
            fieldNameAndTypeMap = BinlogParse.parseFieldName(mess);
        } else if (dataType.equalsIgnoreCase(Constant.MessType.DDL)) {
            List<ColumnDefinition> ddlColumn = SqlParserUtils.getDDLColumn(mess);
            for (int i = 0; i < ddlColumn.size(); i++) {
                ColumnDefinition columnDefinition = ddlColumn.get(i);
                String columnName = columnDefinition.getColumnName();
                fieldNameAndTypeMap.put(columnName.replace("`", "").trim(), columnDefinition.getColDataType().getDataType().toUpperCase());
            }
        } else {
            HashMap<String, String> tmpMap = JSON.parseObject(mess, HashMap.class);
            Set<String> set = tmpMap.keySet();
            Iterator<String> iterator = set.iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                fieldNameAndTypeMap.put(key, "VARCHAR");
            }
        }

        //查询现在已经存储的表字段
        List<ReDataSourceTableFieldDto> tableFieldDtos = reDataSourceTableFieldService.findByTableId(Long.valueOf(tableId + ""));
        Set<String> dbTableField = new HashSet<>();
        for (int j = 0; j < tableFieldDtos.size(); j++) {
            ReDataSourceTableFieldDto reDataSourceTableFieldDto = tableFieldDtos.get(j);
            dbTableField.add(reDataSourceTableFieldDto.getFieldName());
        }

        //将新增的表字段信息存储下来
        for (Map.Entry<String, String> entry : fieldNameAndTypeMap.entrySet()) {
            String fieldName = entry.getKey();
            ReDataSourceTableField tableField = new ReDataSourceTableField();
            if (dbTableField.contains(fieldName)) {
                continue;
            }
            tableField.setFieldName(fieldName);
            tableField.setDataType(entry.getValue());
            tableField.setReDataSourceMetadataId(tableId);
            reDataSourceTableFieldService.create(tableField);
        }
        dbTableField.addAll(fieldNameAndTypeMap.keySet());


        if (StringUtils.isNotBlank(tableInfo.getUniqueKey())) {// && !dbTableField.contains(tableInfo.getUniqueKey())) {
            String trim = tableInfo.getUniqueKey().trim();
            String[] split = trim.split(",");
            for (int i = 0; i < split.length; i++) {
                if (!dbTableField.contains(split[i])) {
                    tableInfo.setUniqueKey("");
                    reDataSourceMetadataService.update(tableInfo);
                    return new ResponseEntity<>(HttpStatus.valueOf("表一键导入的字段不存在指定的UniqueKey字段，已将UniqueKey字段设置为空，请核实后经快纠正UniqueKey字段"));
                }
            }
            return new ResponseEntity<>(HttpStatus.CREATED);
        } else {
            return new ResponseEntity<>(HttpStatus.CREATED);
        }
    }


    private static final String BINLOG_SOURCE_TABLE = "table";


}
