/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.attribute.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-10-12
**/
@Entity
@Data
@DynamicInsert
@DynamicUpdate
@Table(name="te_public_attribute")
public class TePublicAttribute implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    @ApiModelProperty(value = "自增id")
    private Long id;

    @Column(name = "`name`",unique = true,nullable = false)
    @NotBlank
    @ApiModelProperty(value = "属性名称")
    private String name;

    @Column(name = "`description`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "属性描述")
    private String description;

    @Column(name = "`creator`")
    @ApiModelProperty(value = "属性创建人")
    private String creator;

    @Column(name = "`operator`")
    @ApiModelProperty(value = "属性修改人")
    private String operator;

    @Column(name = "`create_time`")
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @Column(name = "`update_time`")
    @ApiModelProperty(value = "修改时间")
    private Timestamp updateTime;

    public void copy(TePublicAttribute source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
