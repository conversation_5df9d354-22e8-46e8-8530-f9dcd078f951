/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.attribute.rest;

import com.dewu.annotation.AnonymousAccess;
import com.dewu.annotation.Log;
import com.dewu.annotation.rest.AnonymousGetMapping;
import com.dewu.flink.template.meta.tablefield.service.ReDataSourceTableFieldService;
import com.dewu.property.attribute.domain.TePublicAttribute;
import com.dewu.property.attribute.domain.TePublicAttributeTableField;
import com.dewu.property.attribute.service.dto.TePublicAttributeDto;
import com.dewu.property.attribute.service.dto.TePublicAttributeQueryCriteria;
import com.dewu.property.attribute.service.TePublicAttributeService;
import com.dewu.utils.Constant;
import com.dewu.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-10-12
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "指标属性管理管理")
@RequestMapping("/api/tePublicAttribute")
public class TePublicAttributeController {

    private final TePublicAttributeService tePublicAttributeService;

    private final ReDataSourceTableFieldService reDataSourceTableFieldService;


    @AnonymousGetMapping
    @ApiOperation("查询指标属性管理")
//    @PreAuthorize("@el.check('tePublicAttribute:list')")
    public ResponseEntity<Object> queryTePublicAttribute(TePublicAttributeQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(tePublicAttributeService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增指标属性管理")
    @ApiOperation("新增指标属性管理")
    @PreAuthorize("@el.check('tePublicAttribute:add')")
    public ResponseEntity<Object> createTePublicAttribute(@Validated @RequestBody TePublicAttribute resources){
        String currentUsername = SecurityUtils.getCurrentUsername();
        resources.setCreator(currentUsername);
        resources.setOperator(currentUsername);
        return new ResponseEntity<>(tePublicAttributeService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改指标属性管理")
    @ApiOperation("修改指标属性管理")
    @PreAuthorize("@el.check('tePublicAttribute:edit')")
    public ResponseEntity<Object> updateTePublicAttribute(@Validated @RequestBody TePublicAttribute resources){
        String currentUsername = SecurityUtils.getCurrentUsername();
        resources.setOperator(currentUsername);
        tePublicAttributeService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }


    /**
     *功能描述
     * <AUTHOR>
     * @date 2022/10/12
     * @param
     * @return
     */
    @GetMapping(value = "/relation/table")
    @AnonymousAccess
    @ApiOperation("查询基础属性关联的表字段,isRelationAttribute 为0表示未关联，为1表示关联")
    public ResponseEntity<Object> queryTableFieldInfoWithTableNameCondition(Long attributeId, Integer isRelationAttribute) {
        TePublicAttributeDto tePublicAttributeDto = tePublicAttributeService.findById(attributeId);
        if (isRelationAttribute == Constant.ATTRIBUTE.NO_RELATION) {
            List<Map<String, Object>> list = reDataSourceTableFieldService.queryTableFieldInfoWithTableNameConditionNoRelationAttribute(tePublicAttributeDto.getName(),tePublicAttributeDto.getDescription());
            return new ResponseEntity<>(list, HttpStatus.OK);
        } else if (isRelationAttribute == Constant.ATTRIBUTE.RELATION) {
            List<Map<String, Object>> list = reDataSourceTableFieldService.queryTableFieldInfoWithTableNameConditionRelationAttribute(tePublicAttributeDto.getName(),tePublicAttributeDto.getDescription());
            return new ResponseEntity<>(list, HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/relation/batch/add")
    @AnonymousAccess
    @ApiOperation("基础属性批量关联表")
    public ResponseEntity<Object> post(@Validated @RequestBody TePublicAttributeTableField param) {
        TePublicAttributeDto tePublicAttributeDto = tePublicAttributeService.findById(param.getAttributeId());
        String description = tePublicAttributeDto.getDescription();
        int i = reDataSourceTableFieldService.updateBatchTableFieldInfoConditionRelationAttribute(description, Arrays.asList(param.getTableFields()));
        return new ResponseEntity<>(i,HttpStatus.OK);
    }


    @DeleteMapping
    @Log("删除指标属性管理")
    @ApiOperation("删除指标属性管理")
    @PreAuthorize("@el.check('tePublicAttribute:del')")
    public ResponseEntity<Object> deleteTePublicAttribute(@RequestBody Long[] ids) {
        tePublicAttributeService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }



}
