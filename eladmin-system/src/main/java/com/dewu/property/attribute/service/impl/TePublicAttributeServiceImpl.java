/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.attribute.service.impl;

import com.dewu.flink.template.meta.tablefield.service.ReDataSourceTableFieldService;
import com.dewu.property.attribute.domain.TePublicAttribute;
import com.dewu.exception.EntityExistException;
import com.dewu.rule.domain.RuleJob;
import com.dewu.rule.domain.RuleOneTable;
import com.dewu.source.compare.domain.RuleCompare;
import com.dewu.source.source.domain.RuleSource;
import com.dewu.utils.*;
import lombok.RequiredArgsConstructor;
import com.dewu.property.attribute.repository.TePublicAttributeRepository;
import com.dewu.property.attribute.service.TePublicAttributeService;
import com.dewu.property.attribute.service.dto.TePublicAttributeDto;
import com.dewu.property.attribute.service.dto.TePublicAttributeQueryCriteria;
import com.dewu.property.attribute.service.mapstruct.TePublicAttributeMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2022-10-12
 **/
@Service
@RequiredArgsConstructor
public class TePublicAttributeServiceImpl implements TePublicAttributeService {

    private final TePublicAttributeRepository tePublicAttributeRepository;
    private final TePublicAttributeMapper tePublicAttributeMapper;
    private final ReDataSourceTableFieldService reDataSourceTableFieldService;

    @Override
    public Map<String, Object> queryAll(TePublicAttributeQueryCriteria criteria, Pageable pageable) {
        Page<TePublicAttribute> page = tePublicAttributeRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        List<TePublicAttribute> content = page.getContent();

        List<TePublicAttributeDto> list = new ArrayList<>();
        for (int i = 0; i < content.size(); i++) {
            TePublicAttributeDto tePublicAttributeDto=new TePublicAttributeDto();
            TePublicAttribute tePublicAttribute = content.get(i);
            String name = tePublicAttribute.getName();
            String description = tePublicAttribute.getDescription();
            long disConnectNum = reDataSourceTableFieldService.countTableFieldInfoWithTableNameConditionNoRelationAttribute(name, description);
            long connectNum = reDataSourceTableFieldService.countTableFieldInfoWithTableNameConditionRelationAttribute(name, description);
            BeanUtils.copyProperties(tePublicAttribute,tePublicAttributeDto);
            tePublicAttributeDto.setConnectNum(connectNum);
            tePublicAttributeDto.setDisConnectNum(disConnectNum);
            list.add(tePublicAttributeDto);
        }

        return PageUtil.toPageV1(page.map(tePublicAttributeMapper::toDto), list);
    }

    @Override
    public List<TePublicAttributeDto> queryAll(TePublicAttributeQueryCriteria criteria) {
        return tePublicAttributeMapper.toDto(tePublicAttributeRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public TePublicAttributeDto findById(Long id) {
        TePublicAttribute tePublicAttribute = tePublicAttributeRepository.findById(id).orElseGet(TePublicAttribute::new);
        ValidationUtil.isNull(tePublicAttribute.getId(), "TePublicAttribute", "id", id);
        return tePublicAttributeMapper.toDto(tePublicAttribute);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TePublicAttributeDto create(TePublicAttribute resources) {
        if (tePublicAttributeRepository.findByName(resources.getName()) != null) {
            throw new EntityExistException(TePublicAttribute.class, "name", resources.getName());
        }
        return tePublicAttributeMapper.toDto(tePublicAttributeRepository.save(resources));
    }

    /**
     * 功能描述 基础属性不允许修改属性名
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/10/12
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TePublicAttribute resources) {
        resources.setName(null);
        TePublicAttribute tePublicAttribute = tePublicAttributeRepository.findById(resources.getId()).orElseGet(TePublicAttribute::new);
        ValidationUtil.isNull(tePublicAttribute.getId(), "TePublicAttribute", "id", resources.getId());
        TePublicAttribute tePublicAttribute1 = null;
        tePublicAttribute1 = tePublicAttributeRepository.findByName(resources.getName());
        if (tePublicAttribute1 != null && !tePublicAttribute1.getId().equals(tePublicAttribute.getId())) {
            throw new EntityExistException(TePublicAttribute.class, "name", resources.getName());
        }
        tePublicAttribute.copy(resources);
        tePublicAttributeRepository.save(tePublicAttribute);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            tePublicAttributeRepository.deleteById(id);
//            reDataSourceTableFieldService.updateBatchResetTableFieldRelationAttribute(id);
        }
    }


}