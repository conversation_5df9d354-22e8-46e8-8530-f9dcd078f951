/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.property.attribute.rest;

import com.dewu.annotation.Log;
import com.dewu.annotation.rest.AnonymousDeleteMapping;
import com.dewu.annotation.rest.AnonymousGetMapping;
import com.dewu.annotation.rest.AnonymousPostMapping;
import com.dewu.flink.template.meta.tablefield.service.ReDataSourceTableFieldService;
import com.dewu.property.attribute.domain.TePublicAttribute;
import com.dewu.property.attribute.domain.TePublicAttributeTableField;
import com.dewu.property.attribute.service.TePublicAttributeService;
import com.dewu.property.attribute.service.dto.TePublicAttributeDto;
import com.dewu.property.attribute.service.dto.TePublicAttributeQueryCriteria;
import com.dewu.utils.Constant;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: WangLin
 * Date: 2022/10/12 下午15:40
 * Description: 公共属性管理接口
 */
@RestController
@RequestMapping("/api/publicAttribute")
public class PublicAttributeController {

	Logger logger = LoggerFactory.getLogger(PublicAttributeController.class);

	@Autowired
	TePublicAttributeService tePublicAttributeService;

	@Autowired
	ReDataSourceTableFieldService reDataSourceTableFieldService;


	/**
	 * 分页查询公共属性
	 *
	 * @param criteria 模糊查询的条件集合
	 * @param pageable 分页条件
	 * @return Map<String, Object> 返回给前端的信息
	 */
	@AnonymousGetMapping("/queryAttributePageable")
	public Object queryPublicAttributePageable(TePublicAttributeQueryCriteria criteria, Pageable pageable) {
		Map<String, Object> returnMap = new HashMap<>();

		try {
			Map<String, Object> stringObjectMap = tePublicAttributeService.queryAll(criteria, pageable);
			returnMap.put("code", "200");
			returnMap.put("data", stringObjectMap);
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("公共属性查询接口出错", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}

		return returnMap;
	}

	/**
	 * 创建公共属性
	 *
	 * @param resources 传参
	 * @return Map<String, Object> 返回给前端的信息
	 */
	@Log("创建公共属性")
	@AnonymousPostMapping("/create")
	public Object createPublicAttribute(@RequestBody TePublicAttribute resources) {
		Map<String, Object> returnMap = new HashMap<>();

		try {
//			String currentUsername = SecurityUtils.getCurrentUsername();
			resources.setCreator("wanglin");
			resources.setOperator("wanglin");
			tePublicAttributeService.create(resources);

			returnMap.put("code", "200");
			returnMap.put("data", "");
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("创建公共属性接口出错", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}

		return returnMap;
	}

	/**
	 * 修改公共属性
	 *
	 * @param resources 传参
	 * @return Map<String, Object> 返回给前端的信息
	 */
	@Log("更新公共属性")
	@AnonymousPostMapping("/update")
	public Object updatePublicAttribute(@RequestBody TePublicAttribute resources) {
		Map<String, Object> returnMap = new HashMap<>();

		try {
//			String currentUsername = SecurityUtils.getCurrentUsername();
			resources.setOperator("wanglin");
			tePublicAttributeService.update(resources);

			returnMap.put("code", "200");
			returnMap.put("data", "");
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("修改公共属性接口出错", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}

		return returnMap;
	}

	/**
	 * 删除公告属性
	 *
	 * @param ids 公共属性id数组
	 * @return Map<String, Object> 返回给前端的信息
	 */
	@Log("删除公共属性")
	@AnonymousDeleteMapping("/delete")
	public Object deletePublicAttribute(@RequestBody Long[] ids) {
		Map<String, Object> returnMap = new HashMap<>();

		try {
			tePublicAttributeService.deleteAll(ids);
			returnMap.put("code", "200");
			returnMap.put("data", "");
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("删除公共属性接口出错", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}
		return returnMap;
	}

	/**
	 * 查询基础属性关联的表字段,isRelationAttribute 为0表示未关联，为1表示关联
	 *
	 * @param attributeId         公共属性id
	 * @param isRelationAttribute 是否关联标识
	 * @return Map<String, Object> 返回给前端的信息
	 */
	@AnonymousGetMapping(value = "/relation/table")
	public Object queryTableFieldInfoWithTableNameCondition(Long attributeId, Integer isRelationAttribute) {
		Map<String, Object> returnMap = new HashMap<>();
		List<Map<String, Object>> list = null;

		try {
			TePublicAttributeDto tePublicAttributeDto = tePublicAttributeService.findById(attributeId);
			if (isRelationAttribute == Constant.ATTRIBUTE.NO_RELATION) {
				list = reDataSourceTableFieldService.queryTableFieldInfoWithTableNameConditionNoRelationAttribute(tePublicAttributeDto.getName(), tePublicAttributeDto.getDescription());
			} else if (isRelationAttribute == Constant.ATTRIBUTE.RELATION) {
				list = reDataSourceTableFieldService.queryTableFieldInfoWithTableNameConditionRelationAttribute(tePublicAttributeDto.getName(), tePublicAttributeDto.getDescription());
			}

			returnMap.put("code", "200");
			returnMap.put("data", list);
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("查询公共属性关联table接口出错", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}
		return returnMap;
	}

	/**
	 * 公共属性关联topic批量更新
	 *
	 * @param param 批量更新参数
	 * @return Map<String, Object> 返回给前端的信息
	 */
	@AnonymousPostMapping(value = "/relation/batchUpdateTable")
	@ApiOperation("公共属性关联topic批量更新")
	public Object post(@Validated @RequestBody TePublicAttributeTableField param) {
		Map<String, Object> returnMap = new HashMap<>();

		try {
			TePublicAttributeDto tePublicAttributeDto = tePublicAttributeService.findById(param.getAttributeId());
			String description = tePublicAttributeDto.getDescription();
			reDataSourceTableFieldService.updateBatchTableFieldInfoConditionRelationAttribute(description, Arrays.asList(param.getTableFields()));

			returnMap.put("code", "200");
			returnMap.put("data", "");
			returnMap.put("mes", "OK");
		} catch (Exception e) {
			logger.info("公共属性关联topic批量更新接口出错", e);
			returnMap.put("code", "500");
			returnMap.put("mes", e.getMessage());
		}
		return returnMap;
	}
}
