/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.dqc.service.impl;

import com.dewu.property.dqc.domain.TeJobDqcMessage;
import com.dewu.utils.ValidationUtil;
import com.dewu.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.dewu.property.dqc.repository.TeJobDqcMessageRepository;
import com.dewu.property.dqc.service.TeJobDqcMessageService;
import com.dewu.property.dqc.service.dto.TeJobDqcMessageDto;
import com.dewu.property.dqc.service.dto.TeJobDqcMessageQueryCriteria;
import com.dewu.property.dqc.service.mapstruct.TeJobDqcMessageMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.dewu.utils.PageUtil;
import com.dewu.utils.QueryHelp;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-05-06
**/
@Service
@RequiredArgsConstructor
public class TeJobDqcMessageServiceImpl implements TeJobDqcMessageService {

    private final TeJobDqcMessageRepository teJobDqcMessageRepository;
    private final TeJobDqcMessageMapper teJobDqcMessageMapper;

    @Override
    public Map<String,Object> queryAll(TeJobDqcMessageQueryCriteria criteria, Pageable pageable){
        Page<TeJobDqcMessage> page = teJobDqcMessageRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(teJobDqcMessageMapper::toDto));
    }

    @Override
    public List<TeJobDqcMessageDto> queryAll(TeJobDqcMessageQueryCriteria criteria){
        return teJobDqcMessageMapper.toDto(teJobDqcMessageRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public TeJobDqcMessageDto findById(Long id) {
        TeJobDqcMessage teJobDqcMessage = teJobDqcMessageRepository.findById(id).orElseGet(TeJobDqcMessage::new);
        ValidationUtil.isNull(teJobDqcMessage.getId(),"TeJobDqcMessage","id",id);
        return teJobDqcMessageMapper.toDto(teJobDqcMessage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TeJobDqcMessageDto create(TeJobDqcMessage resources) {
        return teJobDqcMessageMapper.toDto(teJobDqcMessageRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TeJobDqcMessage resources) {
        TeJobDqcMessage teJobDqcMessage = teJobDqcMessageRepository.findById(resources.getId()).orElseGet(TeJobDqcMessage::new);
        ValidationUtil.isNull( teJobDqcMessage.getId(),"TeJobDqcMessage","id",resources.getId());
        teJobDqcMessage.copy(resources);
        teJobDqcMessageRepository.save(teJobDqcMessage);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            teJobDqcMessageRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<TeJobDqcMessageDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TeJobDqcMessageDto teJobDqcMessage : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("作业名", teJobDqcMessage.getJobName());
            map.put("作业务负责人", teJobDqcMessage.getJobOwner());
            map.put("ts 事件时间", teJobDqcMessage.getEventTime());
            map.put("告警标签", teJobDqcMessage.getTag());
            map.put("异常数据源", teJobDqcMessage.getTableName());
            map.put("异常数据", teJobDqcMessage.getMsg());
            map.put("创建时间", teJobDqcMessage.getCreateTime());
            map.put("修改时间", teJobDqcMessage.getModifyTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}