/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.dqc.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-05-06
**/
@Data
public class TeJobDqcMessageDto implements Serializable {

    /** 自增id */
    private Long id;

    /** 作业名 */
    private String jobName;

    /** 作业务负责人 */
    private String jobOwner;

    /** ts 事件时间 */
    private String eventTime;

    /** 告警标签 */
    private String tag;

    /** 异常数据源 */
    private String tableName;

    /** 异常数据 */
    private String msg;

    /** 创建时间 */
    private Timestamp createTime;

    /** 修改时间 */
    private Timestamp modifyTime;
}