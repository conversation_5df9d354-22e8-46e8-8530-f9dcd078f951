/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.dqc.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
import javax.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-05-06
**/
@Entity
@Data
@Table(name="te_job_dqc_message")
public class TeJobDqcMessage implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    @ApiModelProperty(value = "自增id")
    private Long id;

    @Column(name = "`job_name`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "作业名")
    private String jobName;

    @Column(name = "`job_owner`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "作业务负责人")
    private String jobOwner;

    @Column(name = "`event_time`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "ts 事件时间")
    private String eventTime;

    @Column(name = "`tag`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "告警标签")
    private String tag;

    @Column(name = "`table_name`",nullable = false)
    @NotBlank
    @ApiModelProperty(value = "异常数据源")
    private String tableName;

    @Column(name = "`msg`")
    @ApiModelProperty(value = "异常数据")
    private String msg;

    @Column(name = "`create_time`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @Column(name = "`modify_time`",nullable = false)
    @NotNull
    @ApiModelProperty(value = "修改时间")
    private Timestamp modifyTime;

    public void copy(TeJobDqcMessage source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
