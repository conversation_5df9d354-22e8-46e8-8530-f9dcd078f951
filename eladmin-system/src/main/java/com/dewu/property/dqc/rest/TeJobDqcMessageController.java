/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.dewu.property.dqc.rest;

import com.dewu.annotation.Log;
import com.dewu.property.dqc.domain.TeJobDqcMessage;
import com.dewu.property.dqc.service.TeJobDqcMessageService;
import com.dewu.property.dqc.service.dto.TeJobDqcMessageQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-05-06
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "dqc异常消息管理")
@RequestMapping("/api/teJobDqcMessage")
public class TeJobDqcMessageController {

    private final TeJobDqcMessageService teJobDqcMessageService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('teJobDqcMessage:list')")
    public void exportTeJobDqcMessage(HttpServletResponse response, TeJobDqcMessageQueryCriteria criteria) throws IOException {
        teJobDqcMessageService.download(teJobDqcMessageService.queryAll(criteria), response);
    }

    @GetMapping
    @ApiOperation("查询dqc异常消息")
    @PreAuthorize("@el.check('teJobDqcMessage:list')")
    public ResponseEntity<Object> queryTeJobDqcMessage(TeJobDqcMessageQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(teJobDqcMessageService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增dqc异常消息")
    @ApiOperation("新增dqc异常消息")
    @PreAuthorize("@el.check('teJobDqcMessage:add')")
    public ResponseEntity<Object> createTeJobDqcMessage(@Validated @RequestBody TeJobDqcMessage resources){
        return new ResponseEntity<>(teJobDqcMessageService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改dqc异常消息")
    @ApiOperation("修改dqc异常消息")
    @PreAuthorize("@el.check('teJobDqcMessage:edit')")
    public ResponseEntity<Object> updateTeJobDqcMessage(@Validated @RequestBody TeJobDqcMessage resources){
        teJobDqcMessageService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除dqc异常消息")
    @ApiOperation("删除dqc异常消息")
    @PreAuthorize("@el.check('teJobDqcMessage:del')")
    public ResponseEntity<Object> deleteTeJobDqcMessage(@RequestBody Long[] ids) {
        teJobDqcMessageService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
