package com.dewu.property.cost;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.Data;

@Data
public class LinkedJob {

	private JSONArray parent;

	private Job job;

	private int childrenSize;

	private float selfCost;

	private double cpuLimit;

	private double cpuUsage;

	@Override
	public String toString() {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("job",job);
		jsonObject.put("childrenSize",childrenSize);
		jsonObject.put("parents", JSONObject.toJSON(parent));
		jsonObject.put("selfCost",selfCost);
		jsonObject.put("cpuLimit",cpuLimit);
		jsonObject.put("cpuUsage",cpuUsage);
		return  JSON.toJSONString(jsonObject, SerializerFeature.DisableCircularReferenceDetect);
	}
}
