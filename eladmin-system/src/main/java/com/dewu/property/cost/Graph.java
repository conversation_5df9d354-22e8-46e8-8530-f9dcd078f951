package com.dewu.property.cost;

import com.alibaba.fastjson.JSONArray;
import com.dewu.property.sql.leaf.domain.FlinkSqlLeafSceneAndBusinessLine;
import com.dewu.property.sql.lineage.service.dto.FlinkSqlLineageDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.flink.api.java.tuple.Tuple2;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


public class Graph {

	public final Map<Table, Set<Table>> tableMapping;
	public final Map<Table, Set<Job>> tableAsSinkOfJobMapping;
	public final Map<Table, Set<Job>> tableAsSourceOfJobMapping;
	public final Map<Job, Set<Table>> jobWithAllSinkTableMapping;
	public final Map<Job, Set<Table>> jobWithAllSourceTableMapping;
	public final Map<Job, Set<Job>> jobMapping;
	public final Map<Job, Set<Job>> reverseJobMapping;
	private final Map<Tuple2<String, String>, Float> jobCostInfo;
	private final Map<Tuple2<String, String>, Double> jobCpuLimit;
	private final Map<Tuple2<String, String>, Double> jobCpuUsage;


	public static final Set<String> projectSet = Sets.newHashSet("tech-data-dw2",
			"dw-rt-2", "tech-data-dw4","tech-data-template");


	public static final Map<String,String> kafkaInstance = Maps.newHashMap();


	static {
		kafkaInstance.put("dwd-du-sensors-log-prod-kafka-v2.shizhuang-inc.com:10067","dwd-du-sensors-log-prod-kafka-v2");
		kafkaInstance.put("dwd-du-sensors-log-prod-kafka.shizhuang-inc.com:10035","dwd-du-sensors-log-prod-kafka");
		kafkaInstance.put("bigdata-algo-ods.shizhuang-inc.com:10053","bigdata-algo-ods");
		kafkaInstance.put("binlog-kafka-bigdata","binlog-kafka-bigdata");
		kafkaInstance.put("rt-kafka-bigdata-ads","rt-kafka-bigdata-ads");
		kafkaInstance.put("rt-kafka-bigdata-dwd-v2","rt-kafka-bigdata-dwd-v2");
		kafkaInstance.put("ods-sensors-log-kafka-v2.shizhuang-inc.com:10066","ods-sensors-log-kafka-v2");
		kafkaInstance.put("ods-sensors-log-kafka.shizhuang-inc.com:10015","ods-sensors-log-kafka");
		kafkaInstance.put("rt-kafka-bigdata-ods","rt-kafka-bigdata-ods");
		kafkaInstance.put("dpp-log-ods-kafka.shizhuang-inc.com:10032","dpp-log-ods-kafka");
		kafkaInstance.put("rt-kafka-bigdata-ads-v2","rt-kafka-bigdata-ads-v2");
	}

	public Graph(Map<Tuple2<String, String>, Float> jobCostInfo, Map<Tuple2<String, String>, Double> jobCpuUsage, Map<Tuple2<String, String>, Double> jobCpuLimit) {
		this.tableMapping = new HashMap<>();
		this.jobMapping = new HashMap<>();
		this.reverseJobMapping = new HashMap<>();
		this.tableAsSinkOfJobMapping = new HashMap<>();
		this.tableAsSourceOfJobMapping = new HashMap<>();

		this.jobWithAllSourceTableMapping = new HashMap<>();
		this.jobWithAllSinkTableMapping = new HashMap<>();
		this.jobCostInfo = jobCostInfo;
		this.jobCpuLimit = jobCpuLimit;
		this.jobCpuUsage = jobCpuUsage;
	}

	public Graph(Map<Tuple2<String, String>, Double> jobCpuUsage, Map<Tuple2<String, String>, Double> jobCpuLimit) {
		 this(null,jobCpuUsage,jobCpuLimit);
	}

	public Graph(Map<Tuple2<String, String>, Float> jobCostInfo) {
		 this(jobCostInfo,null,null);
	}

	public void addEdge(Table source, Table destination, Job currentJob) {
		tableMapping.putIfAbsent(source, new HashSet<>());
		if (!source.equals(destination)) {
			tableMapping.get(source).add(destination);
		}
		tableMapping.putIfAbsent(destination, new HashSet<>());
//        adjList.get(destination).add(source); // 如果是无向图，需要在两个顶点之间双向添加边

		tableAsSourceOfJobMapping.putIfAbsent(source, new HashSet<>());
		tableAsSourceOfJobMapping.get(source).add(currentJob);

		tableAsSinkOfJobMapping.putIfAbsent(destination, new HashSet<>());
		tableAsSinkOfJobMapping.get(destination).add(currentJob);


		jobWithAllSourceTableMapping.putIfAbsent(currentJob, new HashSet<>());
		jobWithAllSourceTableMapping.get(currentJob).add(source);

		jobWithAllSinkTableMapping.putIfAbsent(currentJob, new HashSet<>());
		jobWithAllSinkTableMapping.get(currentJob).add(destination);

	}

	public void buildTableGraph(List<FlinkSqlLineageDto> flinkSqlLineageDtos) throws Exception {
		for (FlinkSqlLineageDto record : flinkSqlLineageDtos) {

			String source_table = record.getSourceTable();
			String source_database = record.getSourceDatabase();
			String source_db_type = record.getSourceDbType();
			String source_instance = record.getSourceInstance();
			String target_table = record.getTargetTable();
			String target_database = record.getTargetDatabase();
			String target_db_type = record.getTargetDbType();
			String target_instance = record.getTargetInstance();
			String job_name = record.getJobName();
			String project_name = record.getProjectName();
			String creator = record.getCreator();

			Float jobFloat;
			Double cpuUsage;
			if (jobCostInfo!=null){
				 jobFloat = jobCostInfo.get(Tuple2.of(project_name, job_name));
				 if (jobFloat==null){
					//当前作业不在该日期成本
					continue;
				}
			}else {
				 cpuUsage = jobCpuUsage.get(Tuple2.of(project_name, job_name));
				 if (cpuUsage==null){
					//当前作业不在该日期成本
					continue;
				}
			}

			Job job = new Job();
			job.setTaskName(job_name);
			job.setProjectName(project_name);
			job.setCreator(creator);

			Table sourceTable = new Table();
			sourceTable.setTableName(source_table);
			sourceTable.setDatabase(source_database);
			sourceTable.setInstanceName(source_instance);
			sourceTable.setDbType(source_db_type);

			Table targetTable = new Table();
			targetTable.setTableName(target_table);
			targetTable.setDatabase(target_database);
			targetTable.setInstanceName(target_instance);
			targetTable.setDbType(target_db_type);

			if ("ads_dewu_increase_metrics_for_rule_engine".equals(sourceTable.getTableName()) && "ads_dewu_increase_metrics_bak".equals(targetTable.getTableName())) {
				continue;
			}

			this.addEdge(sourceTable, targetTable, job);
		}

		buildJobGraph();

		buildReverseJobGraph();

	}

	private void buildJobGraph() {
		for (Map.Entry<Table, Set<Job>> entry : tableAsSinkOfJobMapping.entrySet()) {
			Table sinkTable = entry.getKey();
			entry.getValue().forEach(j -> {
				Set<Job> tableAsSourceJobs = tableAsSourceOfJobMapping.get(sinkTable);
				if (tableAsSourceJobs != null) {
					tableAsSourceJobs.forEach(
							sj -> {
								jobMapping.putIfAbsent(j, new HashSet<>());
								if (!j.equals(sj)) {
									jobMapping.get(j).add(sj);
								}
							}
					);
				} else {
					jobMapping.putIfAbsent(j, new HashSet<>());
				}
			});
		}
	}


	/**
	 * 获取叶子节点 不带链路
	 * @return
	 */
	public Set<Job> getTailJob() {
		Set<Job> tailJobs = new HashSet<>();
		for (Map.Entry<Job, Set<Job>> entry : jobMapping.entrySet()) {
			Set<Job> children = entry.getValue();
			Job parent = entry.getKey();
			if (children == null || children.isEmpty()) {
				tailJobs.add(parent);
			}else {
				for (Job child : children) {
					if (children.stream().noneMatch(c-> projectSet.contains(c.getProjectName()))){
						tailJobs.add(parent);
					}
					Set<Job> grandson = jobMapping.get(child);
					if(grandson != null && !grandson.isEmpty()){
						if(grandson.contains(parent)){
							//去除循环
							tailJobs.add(child);
						}
					}
				}
			}
		}
		return tailJobs;
	}

	/**
	 * 计算叶子节点链路
	 * @return
	 */
	public Set<LinkedJob> calcTailJobLink(){
		Set<Job> tailJob = this.getTailJob();
		Set<LinkedJob> dataPlatformLinkedJob = tailJob.stream().filter(j->projectSet.contains(j.getProjectName())).map(s -> {
			LinkedJob linkedJob = new LinkedJob();
			linkedJob.setJob(s);
			return linkedJob;
		}).collect(Collectors.toSet());
		this.calcJobLink(dataPlatformLinkedJob);

		Set<LinkedJob> flinkCodeJob = getFlinkCodeJob();

		dataPlatformLinkedJob.addAll(flinkCodeJob);
		return dataPlatformLinkedJob;
	}

	/**
	 * 计算叶子节点和其父节点元数据 用来分摊到叶子节点成本
	 * @param tailJob
	 */
	private void calcJobLink(Set<LinkedJob> tailJob ) {
		Queue<LinkedJob> queue = new ArrayDeque<>(tailJob);

		while (!queue.isEmpty()){
			LinkedJob tail = queue.poll();
			Job tailSelf = tail.getJob();
			Set<Job> childJobs = jobMapping.get(tailSelf);
			tail.setChildrenSize(childJobs==null?0:childJobs.size());
			tail.setSelfCost(jobCostInfo.getOrDefault(Tuple2.of(tailSelf.getProjectName(),tailSelf.getTaskName()),0f));
			tail.setCpuUsage(jobCpuUsage.getOrDefault(Tuple2.of(tailSelf.getProjectName(),tailSelf.getTaskName()),0d));
			tail.setCpuLimit(jobCpuLimit.getOrDefault(Tuple2.of(tailSelf.getProjectName(),tailSelf.getTaskName()),0d));


			Set<Job> tailParents = this.reverseJobMapping.get(tailSelf);
			if (tailParents!=null&&!tailParents.isEmpty()){
				Set<LinkedJob> childrenParents = new HashSet<>();
				for (Job parent : tailParents) {
					LinkedJob parentSelf = new LinkedJob();
					parentSelf.setJob(parent);
					queue.add(parentSelf);
					childrenParents.add(parentSelf);
					Set<Job> tailGrandpa = this.reverseJobMapping.get(parent);
					if (tailGrandpa!=null&&!tailGrandpa.isEmpty()){
						tailGrandpa.remove(tailSelf);
					}
				}
				JSONArray childrenParentsArray = new JSONArray(Lists.newArrayList(childrenParents));
				tail.setParent(childrenParentsArray);
			}
		}

	}


	/**
	 * 获取叶子节点成本
	 * @return
	 * @throws Exception
	 */
	public Map<LinkedJob, JobShareMetrics> getTailJobShareMetrics(){
		Map<LinkedJob, JobShareMetrics> jobShareMetricsMap = new HashMap<>();
		Set<LinkedJob> linkedJobs = this.calcTailJobLink();

		linkedJobs.forEach(s->{
			JobShareMetrics jobShareMetrics = this.calcLinkedJobMetrics(s);
			jobShareMetricsMap.put(s,jobShareMetrics);

		});

		return jobShareMetricsMap;
	}

	public Set<LinkedJob> getFlinkCodeJob(){
		Set<LinkedJob> codeJobs = new HashSet<>();
		Set<Tuple2<String, String>> sqlJobs = this.jobMapping.keySet().stream().
				map(jobs -> Tuple2.of(jobs.getProjectName(), jobs.getTaskName())).collect(Collectors.toSet());

		for (Map.Entry<Tuple2<String, String>, Float> entry : jobCostInfo.entrySet()) {
			if (projectSet.contains(entry.getKey().f0)&&!sqlJobs.contains(entry.getKey())){
				LinkedJob linkedJob = new LinkedJob();
				Job job = new Job();
				job.setProjectName(entry.getKey().f0);
				job.setTaskName(entry.getKey().f1);
				job.setCreator("<EMAIL>");
				linkedJob.setJob(job);
				linkedJob.setSelfCost(entry.getValue());
				linkedJob.setCpuUsage(jobCpuUsage.getOrDefault(entry.getKey(),0d));
				linkedJob.setCpuLimit(jobCpuLimit.getOrDefault(entry.getKey(),0d));
				codeJobs.add(linkedJob);
			}
		}

		return codeJobs;
	}

	private float calcLinkedJobCost(LinkedJob linkedJob) {
		JSONArray parent = linkedJob.getParent();
		if (parent !=null&&!parent.isEmpty()){
			float parentFloat = 0f;
			for (Object o : parent) {
				 parentFloat+=calcLinkedJobCost((LinkedJob) o);
			}
			float currentCost = parentFloat + linkedJob.getSelfCost();
			return currentCost/(linkedJob.getChildrenSize() == 0 ? 1 : linkedJob.getChildrenSize());
		}else {
			return  linkedJob.getSelfCost() / (linkedJob.getChildrenSize() == 0 ? 1 : linkedJob.getChildrenSize());
		}
	}

	private JobShareMetrics calcLinkedJobMetrics(LinkedJob linkedJob) {
		JSONArray parent = linkedJob.getParent();
		if (parent !=null&&!parent.isEmpty()){
			JobShareMetrics parentMetrics = JobShareMetrics.empty();
			for (Object o : parent) {
				 parentMetrics.sum(calcLinkedJobMetrics((LinkedJob) o));
			}

			float currentCost = parentMetrics.getSelfCost() + linkedJob.getSelfCost();
			double currentCpuUsage = parentMetrics.getCpuUsage() + linkedJob.getCpuUsage();
			double currentCpuLimit = parentMetrics.getCpuLimit() + linkedJob.getCpuLimit();
			//parent+current
			parentMetrics.setSelfCost(currentCost/(linkedJob.getChildrenSize() == 0 ? 1 : linkedJob.getChildrenSize()));
			parentMetrics.setCpuUsage(currentCpuUsage/(linkedJob.getChildrenSize() == 0 ? 1 : linkedJob.getChildrenSize()));
			parentMetrics.setCpuLimit(currentCpuLimit/(linkedJob.getChildrenSize() == 0 ? 1 : linkedJob.getChildrenSize()));

			return parentMetrics;
		}else {
			float currentJobCost = linkedJob.getSelfCost() / (linkedJob.getChildrenSize() == 0 ? 1 : linkedJob.getChildrenSize());
			double currentJobCpuUsage = linkedJob.getCpuUsage() / (linkedJob.getChildrenSize() == 0 ? 1 : linkedJob.getChildrenSize());
			double currentJobCpuLimit = linkedJob.getCpuLimit() / (linkedJob.getChildrenSize() == 0 ? 1 : linkedJob.getChildrenSize());
			JobShareMetrics jobShareMetrics = new JobShareMetrics();
			jobShareMetrics.setSelfCost(currentJobCost);
			jobShareMetrics.setCpuUsage(currentJobCpuUsage);
			jobShareMetrics.setCpuLimit(currentJobCpuLimit);
			return jobShareMetrics;
		}
	}



	/**
	 * 获取叶子节点
	 * @param jobScene
	 * @return
	 * @throws Exception
	 */
	public Set<FlinkSqlLeafSceneAndBusinessLine> getNewLeafNode(Map<Tuple2<String, String>, Tuple2<List<String>, String>> jobScene) {

		Set<Job> tailJob = this.getTailJob();

		Set<LinkedJob> flinkCodeJob = getFlinkCodeJob();
		Set<Job> codeJobs = flinkCodeJob.stream().map(LinkedJob::getJob).collect(Collectors.toSet());
		tailJob.addAll(codeJobs);

		return tailJob.stream().filter(s -> !jobScene.containsKey(Tuple2.of(s.getProjectName(), s.getTaskName()))).map(s -> {
			String businessLine = "";
			FlinkSqlLeafSceneAndBusinessLine flinkSqlLeafSceneAndBusinessLineDto = new FlinkSqlLeafSceneAndBusinessLine();
			flinkSqlLeafSceneAndBusinessLineDto.setProjectName(s.getProjectName());
			flinkSqlLeafSceneAndBusinessLineDto.setJobName(s.getTaskName());
			switch (s.getProjectName()){
				case "dw-rt-2":
					businessLine = "算法";
					break;
				case "tech-data-template":
					businessLine = "数据平台";
					break;
				default:
					businessLine = "";
			}
			flinkSqlLeafSceneAndBusinessLineDto.setBusinessLine(businessLine);
			flinkSqlLeafSceneAndBusinessLineDto.setScene("");
			flinkSqlLeafSceneAndBusinessLineDto.setCreator(s.getCreator());
			Timestamp timestamp = new Timestamp(new Date().getTime());
			flinkSqlLeafSceneAndBusinessLineDto.setCreateTime(timestamp);
			flinkSqlLeafSceneAndBusinessLineDto.setModifyTime(timestamp);
			flinkSqlLeafSceneAndBusinessLineDto.setIsDelete(0);
			flinkSqlLeafSceneAndBusinessLineDto.setIsGrc(0);
			return flinkSqlLeafSceneAndBusinessLineDto;
		}).collect(Collectors.toSet());
	}

	public void buildReverseJobGraph() {
		for (Map.Entry<Table, Set<Job>> entry : tableAsSinkOfJobMapping.entrySet()) {
			Table sinkTable = entry.getKey();
			entry.getValue().forEach(j -> {
				Set<Job> tableAsSourceJobs = tableAsSourceOfJobMapping.get(sinkTable);
				if (tableAsSourceJobs != null) {
					tableAsSourceJobs.forEach(
							sj -> {
								reverseJobMapping.putIfAbsent(sj, new HashSet<>());
								if (!j.equals(sj)) {
									reverseJobMapping.get(sj).add(j);
								}
							}
					);
				} else {
					reverseJobMapping.putIfAbsent(j, new HashSet<>());
				}
			});
		}
	}


	/**
	 * 计算kafka表所用到的叶子节点
	 * @param sourceLinkedJob
	 * @param currentJob
	 * @param tableContainLeafJobs
	 */
	public void calcLinkedKafka(LinkedJob sourceLinkedJob,LinkedJob currentJob,Map<Table, Set<Job>> tableContainLeafJobs ){
		JSONArray parents = currentJob.getParent();
		Job sourceJob = sourceLinkedJob.getJob();
		if (parents !=null&&!parents.isEmpty()){
			for (Object parent : parents) {
				calcLinkedKafka(sourceLinkedJob,(LinkedJob)parent,tableContainLeafJobs);
			}
			calcSelfKafka(sourceJob,currentJob,tableContainLeafJobs);
		}else {
			calcSelfKafka(sourceJob,currentJob,tableContainLeafJobs);
		}
	}

	private void calcSelfKafka(Job sourceJob, LinkedJob currentJob, Map<Table, Set<Job>> tableContainLeafJobs) {
			Set<Table> sourceTables = this.jobWithAllSourceTableMapping.getOrDefault(currentJob.getJob(), new HashSet<>());
			Set<Table> sinkTables = this.jobWithAllSinkTableMapping.getOrDefault(currentJob.getJob(), new HashSet<>());
			Set<Table> sourceKafkaTables = sourceTables.stream().filter(t -> "kafka".equals(t.getDbType())).collect(Collectors.toSet());
			Set<Table> sinkKafkaTables = sinkTables.stream().filter(t -> "kafka".equals(t.getDbType())).collect(Collectors.toSet());
			sourceKafkaTables.addAll(sinkKafkaTables);
			sourceKafkaTables.forEach(s->{
				tableContainLeafJobs.putIfAbsent(s,new HashSet<>());
				tableContainLeafJobs.get(s).add(sourceJob);
			});
	}


//		public Set<Job> getHeadJob(Set<String> projectSet) {
//		Set<Job> headJobs = new HashSet<>();
//		for (Map.Entry<Table, Set<Job>> entry : tableAsSourceOfJobMapping.entrySet()) {
//			Table sourceTable = entry.getKey();
//			Set<Job> tableAsSinkJobs = tableAsSinkOfJobMapping.get(sourceTable);
//			if (tableAsSinkJobs == null || tableAsSinkJobs.isEmpty()) {
//				Set<Job> tableAsSourceJobs = entry.getValue();
//				// jobs all source is head
//				for (Job job : tableAsSourceJobs) {
//					Set<Table> allSources = jobWithAllSourceTableMapping.get(job);
//					if (allSources.stream().noneMatch(tableAsSinkOfJobMapping::containsKey)) {
//						if (!projectSet.isEmpty()){
//							if (projectSet.contains(job.getProjectName())){
//								headJobs.add(job);
//							}
//						}else {
//							headJobs.add(job);
//						}
//					}
//				}
//			}
//		}
//		return headJobs;
//	}


	//	public Sets.SetView<Table> getHeadTable() {
//		Set<Table> set = new HashSet<>();
//		for (Map.Entry<Table, Set<Table>> tableSetEntry : tableMapping.entrySet()) {
//			Set<Table> children = tableSetEntry.getValue();
//			set.addAll(children);
//		}
//		return Sets.difference(tableMapping.keySet(), set);
//	}



//	public void calcJobCost(Set<Job> headJobs, Map<Tuple2<String, String>, Float> jobCostInfo,Map<Job, Float> jobRealCost, Map<Job, Boolean> jobHasVisit, Set<Job> leafJobs) {
//		Iterator<Job> iterator = headJobs.iterator();
//		LinkedHashSet<Job> childrenJob = new LinkedHashSet<>();
//		//从头节点分摊任务成本到下游
//		while (iterator.hasNext()) {
//			Job head = iterator.next();
//			Boolean headHasVisit = jobHasVisit.get(head);
//			if (headHasVisit == null) {
//				headHasVisit = false;
//				jobHasVisit.put(head, true);
//			}
//
//			Float headCost = jobCostInfo.getOrDefault(Tuple2.of(head.getProjectName(), head.getTaskName()),0f);
//
//			jobRealCost.putIfAbsent(head, headCost);
//
//			Set<Job> children = this.jobMapping.get(head);
//			if (children != null && !children.isEmpty()) {
//				for (Job child : children) {
//					Boolean childHasVisit = jobHasVisit.get(child);
//					if (childHasVisit == null) {
//						childHasVisit = false;
//						jobHasVisit.put(child, true);
//					}
//					Set<Job> childChildren = this.jobMapping.get(child);
//					if (headHasVisit && childHasVisit && childChildren != null && childChildren.contains(head)) {
//						//把环的节点关系移除 before:A—>B-A  after:A->B
//						childChildren.remove(head);
//					}
//					childrenJob.add(child);
//					Float childCost = jobCostInfo.getOrDefault(Tuple2.of(child.getProjectName(), child.getTaskName()),0f);
//					Float cost = jobRealCost.get(child);
//					float currentJobCost = headCost / children.size();
//					if (cost == null) {
//						cost = childCost + currentJobCost;
//					} else {
//						cost = cost + currentJobCost;
//					}
//					jobRealCost.put(child, cost);
//				}
//			} else {
//				leafJobs.add(head);
//			}
//		}
//
//		if (!childrenJob.isEmpty()) {
//			calcJobCost(childrenJob,jobCostInfo, jobRealCost, jobHasVisit, leafJobs);
//		}
//	}

//	public void calcTableCost(Set<Table> headTables, Map<Tuple2<String,String>, Float> tableCost, Map<Table, Float> tableRealCost, Map<Table, Boolean> tableHasVisit, Set<Table> leafTables) {
//		Iterator<Table> tableIterator = headTables.iterator();
//		LinkedHashSet<Table> childrenTable = new LinkedHashSet<>();
//		//从头节点分摊任务成本到下游
//		while (tableIterator.hasNext()) {
//			Table head = tableIterator.next();
//			Boolean headHasVisit = tableHasVisit.get(head);
//			if (headHasVisit == null) {
//				headHasVisit = false;
//				tableHasVisit.put(head, true);
//			}
//
//			Float headCost = tableCost.getOrDefault(Tuple2.of(head.getInstanceName(),head.getTableName()),0.0f);
//			tableRealCost.putIfAbsent(head, headCost);
//
//			Set<Table> children = this.tableMapping.get(head);
//			if (children != null && !children.isEmpty()) {
//				for (Table child : children) {
//					Boolean childHasVisit = tableHasVisit.get(child);
//					if (childHasVisit == null) {
//						childHasVisit = false;
//						tableHasVisit.put(child, true);
//					}
//					Set<Table> childChildren = this.tableMapping.get(child);
//					if (headHasVisit && childHasVisit && childChildren != null && childChildren.contains(head)) {
//						//把环的节点关系移除 before:A—>B-A  after:A->B
//						childChildren.remove(head);
//					}
//					childrenTable.add(child);
//					Float childRealCost = tableRealCost.getOrDefault(child,0f);
//					float currentJobCost = headCost / children.size();
//					if (childRealCost == null) {
//						if ("kafka".equals(child.getDbType())) {
//							childRealCost = tableCost.getOrDefault(Tuple2.of(child.getInstanceName(),child.getTableName()),0.0f) + currentJobCost;
//						} else {
//							childRealCost = currentJobCost;
//						}
//					} else {
//						childRealCost = childRealCost + currentJobCost;
//					}
//					tableRealCost.put(child, childRealCost);
//				}
//			} else {
//				leafTables.add(head);
//			}
//		}
//
//		if (!childrenTable.isEmpty()) {
//			calcTableCost(childrenTable, tableCost, tableRealCost, tableHasVisit, leafTables);
//		}
//
//	}


}
