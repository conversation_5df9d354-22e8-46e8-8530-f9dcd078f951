package com.dewu.property.cost;

public class JobMetrics {
	private double cpuUsage;
	private double cpuRequest;
	private double memoryRequest;

	public double getCpuUsage() {
		return cpuUsage;
	}

	public void setCpuUsage(double cpuUsage) {
		this.cpuUsage = cpuUsage;
	}

	public double getCpuRequest() {
		return cpuRequest;
	}

	public void setCpuRequest(double cpuRequest) {
		this.cpuRequest = cpuRequest;
	}

	public double getMemoryRequest() {
		return memoryRequest;
	}

	public void setMemoryRequest(double memoryRequest) {
		this.memoryRequest = memoryRequest;
	}

	public JobMetrics sum(JobMetrics o){
		this.cpuUsage+=o.getCpuUsage();
		this.cpuRequest+=o.getCpuRequest();
		this.memoryRequest+=o.getMemoryRequest();
		return this;
	}

}
