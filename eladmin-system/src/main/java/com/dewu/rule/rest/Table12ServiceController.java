/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.rule.rest;

import com.dewu.annotation.AnonymousAccess;
import com.dewu.property.sql.lineage.service.FlinkSqlLineageService;
import com.dewu.property.sql.lineage.service.dto.SqlLineageExtDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;


/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-06-09
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "对外服务接口")
@RequestMapping("/libra/web/task/lineage")
public class Table12ServiceController {

    private final FlinkSqlLineageService flinkSqlLineageService;

    @GetMapping(value = "/relationship")
    @AnonymousAccess
    @ApiOperation("查询表")
    public ResponseEntity<Object> queryFlinkSqlLineage(String physicsTable, String tableDatabase, String tableDbType,String tableInstance, String levelRelationship) {
        try {
            Map<String, Object> map = new HashMap();
            map.put("code", "200");
            List<SqlLineageExtDto> responses;
            if ("upstream".equalsIgnoreCase(levelRelationship)) {
                responses = flinkSqlLineageService.querySqlLineByTargetTable(physicsTable, tableDatabase, tableInstance,tableDbType);
            } else if ("downstream".equalsIgnoreCase(levelRelationship)) {
                responses = flinkSqlLineageService.querySqlLineBySourceTable(physicsTable, tableDatabase, tableInstance,tableDbType);
            } else if (StringUtils.isBlank(levelRelationship)) {
                responses = flinkSqlLineageService.querySqlLine(physicsTable, tableDatabase,tableInstance, tableDbType);
            } else {
                map.put("data", "levelRelationship参数非法");
                return new ResponseEntity<>(map, HttpStatus.OK);
            }
            map.put("data", responses);
            return new ResponseEntity<>(map, HttpStatus.OK);
        } catch (Exception e) {
            log.error("", e);
            Map<String, Object> map = new HashMap();
            map.put("code", "500");
            map.put("data", "拼命加载中");
            return new ResponseEntity<>(map, HttpStatus.OK);
        }
    }


}
