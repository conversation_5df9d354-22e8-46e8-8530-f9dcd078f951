/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.rule.rest;

import com.dewu.annotation.Log;
import com.dewu.rule.domain.RuleCdc;
import com.dewu.rule.service.RuleJobService;
import com.dewu.rule.service.dto.RuleJobQueryCriteria;
import com.dewu.utils.Constant;
import com.dewu.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-06-09
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "cdc模版管理")
@RequestMapping("/api/cdcJob")
public class RuleCdcController {

    private final RuleJobService ruleJobService;

    @GetMapping
    @ApiOperation("查询cdc模版")
    @PreAuthorize("@el.check('cdcJob:list')")
    public ResponseEntity<Object> queryRuleJob(RuleJobQueryCriteria criteria, Pageable pageable) {
        criteria.setRuleType(Constant.RuleType.CDC_TABLE);
        criteria.setIsDel("0");
        return new ResponseEntity<>(ruleJobService.queryCdcAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增cdc模版")
    @ApiOperation("新增cdc模版")
    @PreAuthorize("@el.check('cdcJob:add')")
    public ResponseEntity<Object> createRuleJob(@Validated @RequestBody RuleCdc param) throws Exception {
        String currentUsername = SecurityUtils.getCurrentUsername();
        param.setUserName(currentUsername);
        param.setRuleType(Constant.RuleType.CDC_TABLE);
        return ruleJobService.createCdc(param);
    }



    @PutMapping
    @Log("修改cdc模版")
    @ApiOperation("修改cdc模版")
    @PreAuthorize("@el.check('cdcJob:edit')")
    public ResponseEntity<Object> updateRuleJob(@Validated @RequestBody RuleCdc param) throws Exception {
        String currentUsername = SecurityUtils.getCurrentUsername();
        if("admin".equals(currentUsername)) {
            //admin用户允许修改作业的负责人
        }else {
            //否则取当前操作用户
            param.setUserName(currentUsername);
        }
        param.setRuleType(Constant.RuleType.CDC_TABLE);
        return ruleJobService.updateCdc(param,currentUsername);
    }

    @DeleteMapping
    @Log("删除cdc模版")
    @ApiOperation("删除cdc模版")
    @PreAuthorize("@el.check('cdcJob:del')")
    public ResponseEntity<Object> deleteRuleJob(@RequestBody Integer[] ids) throws Exception {
        return ruleJobService.updateRuleStatus(ids[0],Constant.RuleType.CDC_TABLE);
    }

}
