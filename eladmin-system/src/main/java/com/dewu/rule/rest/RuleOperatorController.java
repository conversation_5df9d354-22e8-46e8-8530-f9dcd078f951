/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.rule.rest;

import com.alibaba.fastjson.JSON;
import com.dewu.flink.template.deploy.*;
import com.dewu.flink.template.deploy.param.FlinkParams;
import com.dewu.flink.template.deploy.param.Items;
import com.dewu.flink.template.deploy.param.MainParams;
import com.dewu.flink.template.job.domain.TeJobDeployInfo;
import com.dewu.flink.template.job.enums.FlinkTaskHandlerType;
import com.dewu.flink.template.job.handler.flinktask.AbstractFlinkTaskHandler;
import com.dewu.flink.template.job.handler.flinktask.FlinkTaskHandlerFactory;
import com.dewu.flink.template.meta.tableinfo.service.dto.ReDataSourceMetadataDto;
import com.dewu.modules.security.service.dto.JwtUserDto;
import com.dewu.modules.system.service.dto.RoleSmallDto;
import com.dewu.property.metadata.service.ReDataSourceMetadataService;
import com.dewu.rule.service.RuleJobService;
import com.dewu.rule.service.dto.RuleJob1Dto;
import com.dewu.source.source.domain.RuleSource;
import com.dewu.source.source.repository.RuleSourceRepository;
import com.dewu.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-06-09
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "作业交互")
@RequestMapping("/api/operatorJob")
public class RuleOperatorController {

    @Value("${flink.projectName}")
    private String projectName;

    @Value("${flink.engineName}")
    private String engineName;

    @Value("${flink.resourceName}")
    private String resourceName;

    @Value("${flink.jarId}")
    private String jarId;

    @Value("${flink.parentFolderId}")
    private String parentFolderId;

    private final RuleJobService ruleJobService;

    private final ReDataSourceMetadataService reDataSourceMetadataService;

    private final RuleSourceRepository ruleSourceRepository;

    private final RealtimePlatformDeployManager deployManager;

    @PutMapping(value = "/create/{ruleId}")
    @ApiOperation("创建作业")
    @PreAuthorize("@el.check('operatorJob:create')")
    public ResponseEntity<Object> createRuleJob(@PathVariable int ruleId) throws Exception {
        RuleJob1Dto ruleJob = ruleJobService.findById(ruleId);
        String currentUsername = SecurityUtils.getCurrentUsername();
        UserDetails currentUser = SecurityUtils.getCurrentUser();
        JwtUserDto jwtUserDto = (JwtUserDto) currentUser;
        Set<RoleSmallDto> roles = jwtUserDto.getUser().getRoles();
        String nickName = jwtUserDto.getUser().getNickName();
        log.info("currentUsername-----------------------------currentUsername------------currentUsername:{}", currentUsername);
        log.info("roles-----------------------------roles------------roles:{}", JSON.toJSON(roles));


        ResponseEntity<Object> operatorCheck = operatorCheck(ruleJob, currentUsername);
        if (operatorCheck != null) return operatorCheck;

//        String flinkTaskId = ruleJob.getFlinkTaskId();
        //如果没有flinkTaskId，说明没有在自建集群创建作业，则创建作业
//        if(StringUtils.isBlank(flinkTaskId)) {
        CreateTaskResponse taskResponse = createFlinkJob(ruleId, ruleJob);
//        }
        return new ResponseEntity<>(taskResponse.getMsg(), HttpStatus.OK);
    }

    @PutMapping(value = "/deploy/{ruleId}")
    @ApiOperation("创建并启动作业")
    @PreAuthorize("@el.check('operatorJob:deploy')")
    public ResponseEntity<Object> deployRuleJob(@PathVariable int ruleId) throws Exception {
        RuleJob1Dto ruleJob = ruleJobService.findById(ruleId);
        String currentUsername = SecurityUtils.getCurrentUsername();

        ResponseEntity<Object> operatorCheck = operatorCheck(ruleJob, currentUsername);
        if (operatorCheck != null) return operatorCheck;

        String flinkTaskId = ruleJob.getFlinkTaskId();
        //如果没有flinkTaskId，说明没有在自建集群创建作业，则创建作业
        if (StringUtils.isBlank(flinkTaskId)) {
            CreateTaskResponse taskResponse = createFlinkJob(ruleId, ruleJob);
            flinkTaskId = taskResponse.getTaskId();
        }

        //启动作业
        TeJobDeployInfo tmpDeployInfo = new TeJobDeployInfo();
        tmpDeployInfo.setTaskId(flinkTaskId);
        AbstractFlinkTaskHandler strategy = FlinkTaskHandlerFactory.getInvokeStrategy(FlinkTaskHandlerType.SUBMIT);
        SubmitTaskRequest request = (SubmitTaskRequest) strategy.buildRequest(tmpDeployInfo);
        //开始提交作业
        SubmitTaskResponse submitTaskResponse = deployManager.submitTask(request);
        return new ResponseEntity<>(submitTaskResponse.getMsg(), HttpStatus.OK);
    }


    @PutMapping(value = "/stop/{ruleId}")
    @ApiOperation("下线作业")
    @PreAuthorize("@el.check('operatorJob:stop')")
    public ResponseEntity<Object> stopRuleJob(@PathVariable int ruleId) throws Exception {
        RuleJob1Dto ruleJob = ruleJobService.findById(ruleId);
        String currentUsername = SecurityUtils.getCurrentUsername();

        ResponseEntity<Object> operatorCheck = operatorCheck(ruleJob, currentUsername);
        if (operatorCheck != null) return operatorCheck;


        TeJobDeployInfo tmpDeployInfo = new TeJobDeployInfo();
        tmpDeployInfo.setTaskId(ruleJob.getFlinkTaskId());

        //执行停止作业的策略
        AbstractFlinkTaskHandler strategy = FlinkTaskHandlerFactory.getInvokeStrategy(FlinkTaskHandlerType.KILL);
        KillTaskRequest request = (KillTaskRequest) strategy.buildRequest(tmpDeployInfo);
        KillTaskResponse killTaskResponse = deployManager.killTask(request);
        return new ResponseEntity<>(killTaskResponse.getMsg(), HttpStatus.OK);
    }


    private ResponseEntity<Object> operatorCheck(RuleJob1Dto job, String currentUsername) {
        String jobUserName = job.getUserName();
        if ("admin".equals(currentUsername)||"admin".equals(currentUsername)||"admin".equals(currentUsername)
                ||"admin".equals(currentUsername)||"admin".equals(currentUsername)||"admin".equals(currentUsername)) {
            //可以操作所有任务
        } else {
//            String deptName = job.getDeptName();
//            if (!jobUserName.equals(currentUsername)) {
//                if ("dcheck".equals(deptName) && "yuwenhui".equals(currentUsername)) {
//                } else {
            return new ResponseEntity<>(HttpStatus.valueOf("只能编辑自己的任务"));
//                }
//            }
        }
        return null;
    }


    private CreateTaskResponse createFlinkJob(int ruleId, RuleJob1Dto ruleJob) throws Exception {
        String ruleName = ruleJob.getRuleName();
        List<RuleSource> ruleSources = ruleSourceRepository.findByRuleName(ruleName);
        //多个sourcetable,以最大分区数作为flink作业的最大并行度
        int parallelNum = 1;
        for (int i = 0; i < ruleSources.size(); i++) {
            RuleSource ruleSource = ruleSources.get(i);
            ReDataSourceMetadataDto topicInfo = reDataSourceMetadataService.findById(ruleSource.getSourceReDataSourceTableId());
            int num = topicInfo.getPartitionNum();
            if (num > parallelNum) {
                parallelNum = num;
            }
        }
        //创建作业
        CreateTaskResponse taskResponse = createFlinkJob(ruleId, ruleJob, parallelNum);
        //更新flinkTaskId到rulejob
        if (StringUtils.isNotBlank(taskResponse.getTaskId())) {
            ruleJobService.updateRuleFlinkTaskId(ruleId, taskResponse.getTaskId());
        }
        return taskResponse;
    }


    private CreateTaskResponse createFlinkJob(int id, RuleJob1Dto ruleJob, int parallelNum) throws Exception {
        //获取个性作业配置
        TeJobDeployInfo tmpDeployInfo = new TeJobDeployInfo();
        //执行创建策略
        AbstractFlinkTaskHandler strategy = FlinkTaskHandlerFactory.getInvokeStrategy(FlinkTaskHandlerType.CREATE);
        CreateTaskRequest request = (CreateTaskRequest) strategy.buildRequest(tmpDeployInfo, null, (long) id);

        request.setEngineName(engineName);
        request.setProjectName(projectName);
        request.setResourceName(resourceName);
        request.setTaskType(1);
        request.setTaskName(ruleJob.getRuleName());
        request.setTaskDesc(ruleJob.getRuleDesc());
        request.setMainClass("com.dewu.flink.client.rule.formwork.SQLClient");
        request.setJarId(Integer.valueOf(jarId));
        request.setParentFolderId(Integer.valueOf(parentFolderId));

        MainParams mainParams = new MainParams();

        Items items = new Items();
        items.setKey("ruleName");
        items.setValue(ruleJob.getRuleName());
        List<Items> itemsList = new ArrayList<>();
        itemsList.add(items);
        mainParams.setItems(itemsList);
        request.setMainParams(mainParams);

        FlinkParams flinkParams = new FlinkParams();
        flinkParams.setParallel(parallelNum);
        flinkParams.setSlotPerTm(2);
        request.setFlinkParams(flinkParams);

        //开始提交作业
        CreateTaskResponse taskResponse = deployManager.createTask(request);
        log.info("获取创建作业的结果:{}", JSON.toJSON(taskResponse));
        return taskResponse;
    }


}
