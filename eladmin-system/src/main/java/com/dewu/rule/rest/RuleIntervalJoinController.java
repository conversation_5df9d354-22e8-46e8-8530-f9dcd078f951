/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.rule.rest;

import com.dewu.annotation.Log;
import com.dewu.rule.domain.RuleTwoJoin;
import com.dewu.rule.service.RuleJobService;
import com.dewu.rule.service.dto.RuleJobQueryCriteria;
import com.dewu.utils.Constant;
import com.dewu.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@RestController
@RequiredArgsConstructor
@Api(tags = "超时join规则管理")
@RequestMapping("/api/intervalJoin")
public class RuleIntervalJoinController {

    private final RuleJobService ruleJobService;

    @GetMapping
    @ApiOperation("超时join查询")
    @PreAuthorize("@el.check('intervalJoin:list')")
    public ResponseEntity<Object> queryJob(RuleJobQueryCriteria criteria, Pageable pageable) {
        criteria.setRuleType(Constant.RuleType.INTERVAL_JOIN);
        criteria.setIsDel("0");
        return new ResponseEntity<>(ruleJobService.queryAllTwoJoin(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增超时join")
    @ApiOperation("新增超时join")
    @PreAuthorize("@el.check('intervalJoin:add')")
    public ResponseEntity<Object> createRule(@Validated @RequestBody RuleTwoJoin param) throws Exception {
        String currentUsername = SecurityUtils.getCurrentUsername();
        param.setUserName(currentUsername);
        param.setRuleType(Constant.RuleType.INTERVAL_JOIN);
        return new ResponseEntity<>(ruleJobService.createTwoJoin(param), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改超时join")
    @ApiOperation("修改超时join")
    @PreAuthorize("@el.check('intervalJoin:edit')")
    public ResponseEntity<Object> updateRule(@Validated @RequestBody RuleTwoJoin param) throws Exception {
        String currentUsername = SecurityUtils.getCurrentUsername();
        if("admin".equals(currentUsername)) {
            //admin用户允许修改作业的负责人
        }else {
            //否则取当前操作用户
            param.setUserName(currentUsername);
        }
        param.setRuleType(Constant.RuleType.INTERVAL_JOIN);
        return ruleJobService.updateTwoJoin(param,currentUsername);
    }


    @DeleteMapping
    @Log("删除超时join")
    @ApiOperation("删除超时join")
    @PreAuthorize("@el.check('intervalJoin:del')")
    public ResponseEntity<Object> deleteRuleJob(@RequestBody Integer[] ids) throws Exception {
        return ruleJobService.updateRuleStatus(ids[0],Constant.RuleType.INTERVAL_JOIN);
    }
}
