/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.rule.rest;

import com.alibaba.fastjson2.JSON;
import com.dewu.annotation.AnonymousAccess;
import com.dewu.annotation.Log;
import com.dewu.flink.template.deploy.KillTaskResponse;
import com.dewu.flink.template.job.enums.JobStatusType;
import com.dewu.flink.template.job.service.TeJobInfoService;
import com.dewu.flink.template.job.service.dto.TeJobInfoDto;
import com.dewu.flink.template.job.service.dto.TeJobInfoQueryCriteria;
import com.dewu.flink.template.meta.datasource.service.ReDataSourceService;
import com.dewu.flink.template.meta.datasource.service.dto.ReDataSourceDto;
import com.dewu.flink.template.meta.metric.domain.TeMetricMetadataInfo;
import com.dewu.flink.template.meta.metric.service.TeMetricGroupMetadataInfoService;
import com.dewu.flink.template.meta.metric.service.TeMetricMetadataInfoService;
import com.dewu.flink.template.meta.metric.service.dto.TeMetricGroupMetadataInfoDto;
import com.dewu.flink.template.meta.metric.service.dto.TeMetricMetadataInfoDto;
import com.dewu.flink.template.meta.tableinfo.service.dto.ReDataSourceMetadataDto;
import com.dewu.flink.template.translator.CodeResponse;
import com.dewu.modules.quartz.task.KafkaTask;
import com.dewu.property.metadata.service.ReDataSourceMetadataService;
import com.dewu.rule.domain.*;
import com.dewu.flink.template.meta.metric.service.AdsMetricsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.util.CollectionUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2022-06-09
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "对外服务接口")
@RequestMapping("/api/teJob")
@Slf4j
public class AdsMetricsServiceController {

    private final AdsMetricsService adsMetricsService;

    private final TeJobInfoService templateEngineJobInfoService;

    private final TeMetricMetadataInfoService teMetricMetadataInfoService;

    private final TeMetricGroupMetadataInfoService teMetricGroupMetadataInfoService;

    private final TeJobInfoService teJobInfoService;

    private final ReDataSourceMetadataService reDataSourceMetadataService;
    private final ReDataSourceService reDataSourceService;

    private final KafkaTask kafkaTask;

    private final String JOB_NAME_PRE_SUFFIX = "metric_platform_";

    private final String MODULE_TYPE = "RealtimeIndexModule";

    public static final String CREATE = "create";

    public static final String START = "start";

    public static final String STOP = "stop";

    public static final String FAILURE = "failure";

    public static final String SUCCESS = "success";


    @PostMapping(value = "/remoteSyncMetrics")
    @AnonymousAccess
    public ResponseEntity<Object> remoteSyncMetrics(@RequestBody TeMetricMetadataInfo teMetricMetadataInfo) throws Exception {
        adsMetricsService.remoteSyncMetrics(teMetricMetadataInfo);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @PostMapping(value = "/validator")
    @AnonymousAccess
    @Log("校验指标平台任务")
    @ApiOperation("校验指标平台任务")
    public ResponseEntity<Object> validatorMetricsTask(@RequestBody MetricsTaskBean param) {
        Map<String, String> map = new HashMap();
        //校验任务是否可运行
        try {
            log.info("校验指标平台任务校验,请求参数{}", JSON.toJSONString(param));
            TeJobInfoDto resources = new TeJobInfoDto();
            if (paramCheck(param, map, resources)) {
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            CodeResponse codeResponse = teJobInfoService.validatorAndCodeGenerator(resources);
            if (Objects.isNull(codeResponse)) {
                map.put("code", "500");
                map.put("msg", "任务校验错误,未知错误");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e) {
            log.error("校验指标平台任务校验失败",e);
            map.put("code", "500");
            map.put("msg", "任务校验错误:" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
        }
        map.put("code", "200");
        map.put("msg", "任务校验通过");
        return new ResponseEntity<>(map, HttpStatus.OK);
    }


    @PostMapping(value = "/add")
    @AnonymousAccess
    @Log("新增指标平台任务")
    @ApiOperation("新增指标平台任务")
    public ResponseEntity<Object> createMetricsTask(@RequestBody MetricsTaskBean param) {
        log.info("新增指标平台任务,请求参数{}", JSON.toJSONString(param));
        Map<String, String> map = new HashMap();
        try {
            TeJobInfoDto resources = new TeJobInfoDto();

            if(StringUtils.isBlank(param.getDataSetId())) {
                map.put("code", "500");
                map.put("msg", "dataSetId不能为空");
                log.info("新增指标平台任务返回参数：{}",map);
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }
            if(StringUtils.isBlank(param.getDataSetName())) {
                map.put("code", "500");
                map.put("msg", "dataSetName不能为空");
                log.info("新增指标平台任务返回参数：{}",map);
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            if (paramCheck(param, map, resources)) {
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            //存储数据到rds,并创建flink作业。
            TeJobInfoDto teJobInfoDto = adsMetricsService.createMetricsTask(resources);
            try {
                //发送创建中
                log.info("向kafka中发送创建任务操作状态");
                MetricsTaskOperatorBean operatorMap = new MetricsTaskOperatorBean();
                operatorMap.setOperator(AdsMetricsServiceController.CREATE);
                operatorMap.setJobId(teJobInfoDto.getId() + "");
                operatorMap.setStartTimestamp(System.currentTimeMillis());
                kafkaTask.sendMessMonitorStatus(operatorMap);

                //启动flink作业
                log.info("新增指标平台任务返回参数，启动flink作业");
                templateEngineJobInfoService.executeTask(teJobInfoDto.getId());
            } catch (Exception e) {
                log.error("指标平台任务创建成功，启动失败，不回滚",e);
                log.info("指标平台任务创建成功，启动失败，不回滚");
                log.error("指标平台任务创建成功，启动失败，不回滚");
            }
            map.put("code", "200");
            map.put("msg", "任务创建成功");
            map.put("jobId", teJobInfoDto.getId() + "");
            map.put("DagJson", resources.getDagJson());
            log.info("新增指标平台任务返回参数：{}",map);
            return new ResponseEntity<>(map, HttpStatus.OK);
        } catch (Exception e) {
            log.error("指标平台任务创建失败", e);
            map.put("code", "500");
            map.put("msg", "任务创建失败:" + e.getMessage());
            log.info("新增指标平台任务返回参数：{}",map);
            return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(value = "/update")
    @AnonymousAccess
    @Log("修改指标平台任务")
    @ApiOperation("修改指标平台任务")
    public ResponseEntity<Object> updateMetricsTask(@RequestBody MetricsTaskBean param) {
        log.info("修改指标平台任务,请求参数{}", JSON.toJSONString(param));
        Map<String, String> map = new HashMap();
        try {
            if(StringUtils.isBlank(param.getJobId())) {
                map.put("code", "500");
                map.put("msg", "jobid不能为空");
                log.info("修改指标平台任务返回参数：{}",map);
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }
            if(StringUtils.isBlank(param.getDataSetId())) {
                map.put("code", "500");
                map.put("msg", "dataSetId不能为空");
                log.info("修改指标平台任务返回参数：{}",map);
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }
            if(StringUtils.isBlank(param.getDataSetName())) {
                map.put("code", "500");
                map.put("msg", "dataSetName不能为空");
                log.info("修改指标平台任务返回参数：{}",map);
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            TeJobInfoQueryCriteria teJobInfoQueryCriteria = new TeJobInfoQueryCriteria();
            List<Long> list = new ArrayList<>();
            list.add(Long.valueOf(param.getJobId()));
            teJobInfoQueryCriteria.setId(list);
            List<TeJobInfoDto> teJobInfoDtos = templateEngineJobInfoService.queryAll(teJobInfoQueryCriteria);
            if (teJobInfoDtos.size() == 0) {
                map.put("code", "500");
                map.put("msg", "任务id不存在:" + param.getJobId());
                log.info("修改指标平台任务返回参数：{}",map);
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            } else {
                TeJobInfoDto teJobInfoDto = teJobInfoDtos.get(0);
                int status = teJobInfoDto.getStatus();
                if (status == JobStatusType.DEPLOYING.getCode()
                        || status == JobStatusType.RUNNING.getCode()
                        || status == JobStatusType.KILLING.getCode()) {
                    map.put("code", "500");
                    map.put("msg", "任务发布中/已运行/下线中，请下线任务后再进行修改");
                    log.info("修改指标平台任务返回参数：{}",map);
                    return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
                }
            }

            TeJobInfoDto resources = new TeJobInfoDto();
            if (paramCheck(param, map, resources)) {
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }
            resources.setId(Long.valueOf(param.getJobId()));
            //存储数据到rds,并创建flink作业。
            templateEngineJobInfoService.update(resources);
            map.put("code", "200");
            map.put("msg", "任务修改成功");
            map.put("jobId", resources.getId() + "");
            map.put("DagJson", resources.getDagJson());
            log.info("修改指标平台任务返回参数：{}",map);
            return new ResponseEntity<>(map, HttpStatus.OK);
        } catch (Exception e) {
            log.error("", e);
            map.put("code", "500");
            map.put("msg", "任务修改失败:" + e.getMessage());
            log.info("修改指标平台任务返回参数：{}",map);
            return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
        }
    }


    @PutMapping(value = "/start/{jobId}")
    @AnonymousAccess
    @Log("启动指标平台任务")
    @ApiOperation("启动指标平台任务")
//    @BotAuthorizationAnnotation(isApproval = true)
    public ResponseEntity<Object> startMetricsTask(@PathVariable String jobId) {
        Map<String, String> map = new HashMap();
        try {
            TeJobInfoDto teJobInfoDto = templateEngineJobInfoService.findById(Long.valueOf(jobId));
            String type = teJobInfoDto.getType();
            if (!MODULE_TYPE.equals(type)) {
                map.put("code", "500");
                map.put("msg", "非指标平台作业无法操作");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }
            MetricsTaskOperatorBean operatorMap = new MetricsTaskOperatorBean();
            operatorMap.setOperator(START);
            operatorMap.setJobId(jobId);
            operatorMap.setStartTimestamp(System.currentTimeMillis());
            kafkaTask.sendMessMonitorStatus(operatorMap);
            templateEngineJobInfoService.deploy(Long.valueOf(jobId));
        } catch (Exception e) {
            map.put("code", "500");
            map.put("msg", "flink作业启动失败" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
        }
        map.put("code", "200");
        map.put("msg", "消息接受成功");
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    @PutMapping(value = "/stop/{jobId}")
    @AnonymousAccess
    @Log("停止指标平台任务")
    @ApiOperation("停止指标平台任务")
    public ResponseEntity<Object> stopMetricsTask(@PathVariable String jobId) {
        Map<String, String> map = new HashMap();
        try {
            TeJobInfoDto teJobInfoDto = templateEngineJobInfoService.findById(Long.valueOf(jobId));
            String type = teJobInfoDto.getType();
            if (!MODULE_TYPE.equals(type)) {
                map.put("code", "500");
                map.put("msg", "非指标平台作业无法操作");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            MetricsTaskOperatorBean operatorMap = new MetricsTaskOperatorBean();
            operatorMap.setOperator(STOP);
            operatorMap.setJobId(jobId);
            operatorMap.setStartTimestamp(System.currentTimeMillis());
            kafkaTask.sendMessMonitorStatus(operatorMap);
            KillTaskResponse task = templateEngineJobInfoService.stopTask(Long.valueOf(jobId));
            if (task.isFlag()) {
                map.put("code", "200");
                map.put("msg", "消息接受成功");
                return new ResponseEntity<>(map, HttpStatus.OK);
            } else {
                map.put("code", "500");
                map.put("msg", "flink作业停止失败" + task.getMsg());
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e) {
            map.put("code", "500");
            map.put("msg", "flink作业停止失败" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
        }

    }


    private boolean paramCheck(@RequestBody MetricsTaskBean param, Map<String, String> map, TeJobInfoDto resources) {
        Set<Set<String>> checkData = new HashSet<>();
        String dimIds = "";
        MetricsIndicatorsCodeMap[] data = param.getData();
        if(StringUtils.isBlank(param.getTableId())) {
            map.put("code", "500");
            map.put("msg", "tableid不能为空");
            return true;
        }
        if(StringUtils.isBlank(param.getStartTime())) {
            map.put("code", "500");
            map.put("msg", "startTime不能为空");
            return true;
        }

        String metricMetadataInfoIds = "";
        for (int i = 0; i < data.length; i++) {
            MetricsIndicatorsCodeMap datum = data[i];
            String teMetricMetadataInfoId = datum.getTeMetricMetadataInfoId();
            if(StringUtils.isBlank(teMetricMetadataInfoId)) {
                map.put("code", "500");
                map.put("msg", "teMetricMetadataInfoId不能为空");
                return true;
            }
            if(StringUtils.isBlank(datum.getIndicatorsCode())) {
                map.put("code", "500");
                map.put("msg", "indicatorsCode不能为空");
                return true;
            }
            if (i < data.length - 1) {
                metricMetadataInfoIds = StringUtils.join(metricMetadataInfoIds, teMetricMetadataInfoId, ",");
            } else {
                metricMetadataInfoIds = StringUtils.join(metricMetadataInfoIds, teMetricMetadataInfoId);
            }
            TeMetricMetadataInfoDto metadataInfoDto = teMetricMetadataInfoService.findById(Long.valueOf(teMetricMetadataInfoId));
            if (metadataInfoDto == null) {
                map.put("code", "500");
                map.put("msg", "指标不存在：" + teMetricMetadataInfoId);
                return true;
            }
            TeMetricGroupMetadataInfoDto metricGroupMetadataInfoDto = teMetricGroupMetadataInfoService.findById(metadataInfoDto.getTeMetricGroupMetadataInfoId());
            String teCommonAttributesIds = metricGroupMetadataInfoDto.getTeCommonAttributesIds2();
            Set<String> dims = Arrays.stream(teCommonAttributesIds.split(",")).collect(Collectors.toSet());
            Long id = metricGroupMetadataInfoDto.getId();
            checkData.add(dims);
            dimIds = teCommonAttributesIds;
        }

        if (checkData.size() > 1) {
            map.put("code", "500");
            map.put("msg", "指标不属于同一个维度范围：" + checkData);
            return true;
        }

        ReDataSourceMetadataDto metadataDto = reDataSourceMetadataService.findById(Long.valueOf(param.getTableId()));
        ReDataSourceDto dataSourceDto = reDataSourceService.findById(metadataDto.getReDataSourceId());
        if (dataSourceDto.getUsePlace() != 3) {
            map.put("code", "500");
            map.put("msg", "输出表非指标资产下表，请核对后再输出");
            return true;
        }

        String jobName = JOB_NAME_PRE_SUFFIX + param.getDataSetId();
        resources.setJobName(jobName);
        resources.setType(MODULE_TYPE);
        resources.setJobDesc("模版平台任务,来自指标平台任务:" + param.getDataSetName());
        Map<String, Object> dagMap = new HashMap<>();
        dagMap.put("jobName", jobName);
        dagMap.put("moduleName", MODULE_TYPE);
        dagMap.put("dimIDs", dimIds);
        dagMap.put("metricIDs", metricMetadataInfoIds);
        dagMap.put("sinkTableId", param.getTableId());
        dagMap.put("dataSetId", param.getDataSetId());
        dagMap.put("dataSetName", param.getDataSetName());
        dagMap.put("extSql", param.getExtSql());
        dagMap.put("relativeStartTs", param.getStartTime());
        resources.setDagJson(JSON.toJSONString(dagMap));
        return false;
    }

    public static void main(String[] args) {
        Set<Set<String>> eles = new HashSet<>();
        Set<String> e1 = new HashSet<>();
        Set<String> e2 = new HashSet<>();
        e1.add("1");
        e1.add("2");
        e1.add("3");
        e2.add("3");
        e2.add("2");
        e2.add("1");

        eles.add(e1);
        eles.add(e2);

        System.out.println(eles.size());
    }

}
