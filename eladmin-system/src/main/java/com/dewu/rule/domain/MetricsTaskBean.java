package com.dewu.rule.domain;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class MetricsTaskBean {

    @ApiModelProperty(value = "修改作业id")
    private String jobId;

    @ApiModelProperty(value = "输出表id")
    private String tableId;

    @ApiModelProperty(value = "启动时间")
    private String startTime;

    @ApiModelProperty(value = "dataSetId名称")
    private String dataSetId;

    @ApiModelProperty(value = "dataSetName名称")
    private String dataSetName;

    @ApiModelProperty(value = "extSql名称")
    private String extSql;

    private MetricsIndicatorsCodeMap[] data;


    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getDataSetId() {
        return dataSetId;
    }

    public void setDataSetId(String dataSetId) {
        this.dataSetId = dataSetId;
    }

    public String getDataSetName() {
        return dataSetName;
    }

    public void setDataSetName(String dataSetName) {
        this.dataSetName = dataSetName;
    }

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public MetricsIndicatorsCodeMap[] getData() {
        return data;
    }

    public void setData(MetricsIndicatorsCodeMap[] data) {
        this.data = data;
    }

    public String getExtSql() {
        return extSql;
    }

    public void setExtSql(String extSql) {
        this.extSql = extSql;
    }

    public static void main(String[] args) {
        MetricsIndicatorsCodeMap metricsIndicatorsCodeMap=new MetricsIndicatorsCodeMap();
        metricsIndicatorsCodeMap.setIndicatorsCode("ZB_631627548113");
        metricsIndicatorsCodeMap.setTeMetricMetadataInfoId("1000");

        MetricsIndicatorsCodeMap metricsIndicatorsCodeMap1=new MetricsIndicatorsCodeMap();
        metricsIndicatorsCodeMap1.setIndicatorsCode("ZB_631627548113");
        metricsIndicatorsCodeMap1.setTeMetricMetadataInfoId("1000");

        MetricsIndicatorsCodeMap[] data=new MetricsIndicatorsCodeMap[]{metricsIndicatorsCodeMap,metricsIndicatorsCodeMap1};

        MetricsTaskBean metricsTaskBean=new MetricsTaskBean();
        metricsTaskBean.setTableId("1234456");
        metricsTaskBean.setStartTime("day-0");
        metricsTaskBean.setData(data);

        System.out.println(JSONObject.toJSON(metricsTaskBean));

    }

}
