package com.dewu.rule.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class RuleCdc extends RuleOneTable {

    @NotNull
    @ApiModelProperty(value = "全量表id")
    private String fullTableId;

    @NotBlank
    @ApiModelProperty(value = "全量表的pt分区值")
    private String fullTablePt;

    @NotNull
    @ApiModelProperty(value = "全量表每次读取数量条数")
    private Long fullTableBatchSize;

    @ApiModelProperty(value = "读取时间")
    private String startupOffsetsTime;


}
