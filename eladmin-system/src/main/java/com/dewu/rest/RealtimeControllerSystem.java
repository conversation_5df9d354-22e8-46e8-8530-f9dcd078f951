package com.dewu.rest;

import com.dewu.annotation.rest.AnonymousGetMapping;
import com.dewu.annotation.rest.AnonymousPostMapping;
import com.dewu.property.sql.lineage.service.FlinkSqlLineageService;
import com.dewu.property.sql.lineage.service.dto.TableLineageDto;
import com.dewu.property.sql.pipelineage.LineageGraphManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: WangLin
 * Date: 2024/7/17 13:54
 * Description: 实时模版接口-Stephen专用，eladmin-system模块
 */
@RestController
@RequestMapping("/api")
public class RealtimeControllerSystem {
    private static final Logger LOG = LoggerFactory.getLogger(RealtimeControllerSystem.class);

    @Autowired
    private FlinkSqlLineageService flinkSqlLineageService;

    @Autowired
    private LineageGraphManager lineageGraphManager;


    /**
     * 后端返回前端的数据结构
     *
     * @param code    状态码
     * @param message 提示消息
     * @param data    数据
     * @return 整个结构
     */
    private Map<String, Object> createResult(String code, String message, Object data) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("mes", message);
        result.put("data", data);
        return result;
    }


    //    血缘查询接口---------------------------------------------------------------------

    /**
     * 根据搜索表名称模糊匹配列表
     *
     * @param tableName 搜索表名
     * @return List<TableLineageDto>
     */
    @AnonymousGetMapping("/table/tableLineage/{tableName}")
    public Object searchTableLineageInfo(@PathVariable String tableName) {
        try {
            List<TableLineageDto> responses = flinkSqlLineageService.likeDistinctByTableNameForAssertLink("%" + tableName + "%");
            return createResult("200", "ok", responses);
        } catch (Exception e) {
            LOG.error("", e);
            return createResult("500", e.getMessage(), null);
        }
    }

    // 查询表的完整上下游信息
    @AnonymousPostMapping("/table/tablePipline")
    public Object getPipline(@RequestBody Map<String, String> map) {
        try {
            String tableName = map.get("tableName");
            String db = map.get("db");
            String instance = map.get("instance");
            String ctime = map.get("ctime");
            LineageGraphManager.TableNode tableNode = LineageGraphManager.TableNode.of(tableName, db, instance, 0);
            String dagJson = lineageGraphManager.fetchGraph(tableNode, ctime);
            return createResult("200", "ok", dagJson);
        } catch (Exception e) {
            LOG.error("Failed assets status persist. ", e);
            return createResult("500", e.getMessage(), null);
        }
    }
}
