package com.dewu.modules.quartz.libra;

public enum ResultEnum {
    PARAM_NULL(-100000, "参数为空"),
    PARAM_ERROR(-100001, "参数错误"),
    PARAM_MISS(-100002, "参数缺失"),
    PARAM_JSON_FORMAT_ERROR(-100003, "json参数格式错误"),
    THIRD_PARTY_INTERFACE_ERROR(-100004, "调用第三方接口失败"),

    RESOURCE_NOT_EXIST(-110000, "资源不存在"),
    RESOURCE_EXIST(-110001, "资源已存在"),
    RESOURCE_TYPE_NOT_EXIST(-110002, "资源类型不存在"),
    RESOURCE_NAME_ERROR(-110003, "资源名称错误"),
    RESOURCE_TYPE_NOT_SUPPORT(-110004, "资源类型不支持"),

    ENGINE_NOT_EXIST(-110100, "引擎不存在"),
    ENGINE_EXIST(-110101, "引擎已存在"),
    ENGINE_NAME_ERROR(-110102, "引擎名称错误"),
    ENGINE_VERSION_ERROR(-110103, "引擎版本错误"),

    ENGINE_RESOURCE_RELATION_NOT_EXIST(-110110, "引擎资源关联不存在"),
    ENGINE_RESOURCE_RELATION_EXIST(-110111, "引擎资源关联已存在"),
    RESOURCES_NOT_ALLOWED(-110112,"P0或P1优先级任务不允许使用混部"),
    PRIORITY_NOT_NULL(-110113, "任务优先级不允许为空"),

    PRIORITY_NOT_RIGHT(-110114, "任务优先级参数值有误，只能传递0或1或2或3"),

    PHONE_CALL_CLOSED_NOT_ALLOWED(-110115, "P0优先级任务不允许关闭电话告警"),

    PROJECT_RESOURCE_RELATION_EXIST(-110203, "项目资源关联已存在"),
    PROJECT_RESOURCE_RELATION_NOT_EXIST(-110204, "项目资源关联不存在"),
    PROJECT_RESOURCE_CORE_OVER(-110205, "核数超过资源剩余核数"),
    PROJECT_RESOURCE_MEMORY_OVER(-110206, "内存超过资源剩余内存"),
    PROJECT_RESOURCE_REGION_NOT_SAME(-110207, "同一项目空间下只能添加相同地区的资源"),


    ADMIN_EXIST(-120000, "管理员已存在"),
    ADMIN_NOT_EXIST(-120001, "管理员不存在"),


    AUTH_BU_NAME_ERROR(-130000, "bu名称错误"),
    AUTH_BU_EXIST(-130001, "bu已经存在"),
    AUTH_BU_NOT_EXIST(-130002, "bu不存在"),
    AUTH_BU_CORE_OVER(-130003, "申请的核数超过BU配额"),
    AUTH_BU_MEMORY_OVER(-130004, "申请的内存超过BU配额"),
    AUTH_BU_OWNER_NOT_EXIST(-130005, "申请的内存超过BU配额"),

    AUTH_PROJECT_NAME_ERROR(-140000, "项目名称错误"),
    AUTH_PROJECT_EXIST(-140001, "项目已经存在"),
    AUTH_PROJECT_NOT_EXIST(-140002, "项目不存在"),
    AUTH_MEMBER_EXIST(-140003, "项目成员存在"),
    AUTH_MEMBER_NOT_EXIST(-140004, "项目成员不存在"),
    AUTH_OWNER_NOT_EXIST(-140005, "项目owner不存在"),

    TASK_NOT_EXIST(-150000, "任务不存在"),
    TASK_EXIST(-150001, "任务已存在"),
    TASK_TYPE_ERROR(-150002, "任务类型错误"),
    TASK_OWNERS_NOT_EXIST(-150003, "任务负责人不存在"),
    TASK_NAME_NOT_EXIST(-150004, "任务名称不存在"),
    TASK_RESOURCE_NOT_EXIST(-150005, "任务资源不存在"),
    TASK_USER_NOT_EXIST(-150006, "任务执行用户不存在"),
    TASK_NAME_ERROR(-150007, "任务名称只能包含数字字母下划线"),
    TASK_BU_NOT_EXIST(-150008, "任务BU不存在"),
    TASK_PROJECT_NOT_EXIST(-150009, "任务项目不存在"),
    TASK_JAR_NOT_EXIST(-150010, "任务JAR路径不存在"),
    TASK_MAIN_CLASS_ERROR(-150011, "任务入口类错误"),
    TASK_FLINK_PARAM_NOT_EXIST(-150012, "任务flink参数不存在"),
    TASK_FLINK_PARALLEL(-150013, "Parallel的范围1~2000"),
    TASK_FLINK_SLOTNUM(-150014, "SlotNum的范围1~16"),
    TASK_FLINK_JMMEMORY(-150015, "JobManagerMemory的范围700~8192"),
    TASK_FLINK_TMMEMORY(-150016, "TaskManagerMemory的范围1024~40960"),
    TASK_STATUS_NOT_OPERABLE(-150017, "状态错误,禁止操作"),
    TASK_SUBMIT_GATEWAY_EXCEPTION(-150018, "调用提交机异常"),
    TASK_SUBMIT_GATEWAY_ERROR(-150019, "提交机返回错误"),
    TASK_CREATOR_NOT_EXIST(-150020, "任务创建人不存在"),
    TASK_STATUS_UPDATE_ERROR(-150021, "任务更新状态失败"),
    TASK_SUBMIT_EXCEPTION(-150022, "发布作业异常"),
    TASK_KILL_GATEWAY_EXCEPTION(-150023, "下线任务: 调用提交机异常"),
    TASK_KILL_GATEWAY_ERROR(-150024, "下线任务: 提交机返回错误"),
    TASK_KILL_EXCEPTION(-150025, "下线任务异常"),
    TASK_STATUS_ERROR(-150026, "任务状态错误"),
    TASK_SQL_CONTENT_NOT_EXIST(-150027, "SQL内容为空"),
    TASK_KILL_FAIL_KEEP_RUN(-150028, "下线失败, 任务仍在运行"),
    TASK_SUBMIT_ERROR(-150029, "发布任务失败"),
    TASK_RUN_EDIT_NO_AUTH(-150030, "无运行时编辑权限"),
    TASK_INSTANCE_EXIST(-150031, "任务实例已存在，禁止发布"),
    TASK_INSTANCE_CHECK_ERROR(-150032, "任务实例检查失败"),
    TASK_STOP_DEPLOY_EXCEPTION(-150033, "任务停止部署异常"),
    TASK_STOP_DEPLOY_ERROR(-150034, "任务停止部署失败"),
    TASK_INSTANCE_CREATE_ERROR(-150035, "任务实例创建失败，禁止发布"),
    TASK_RUN_TYPE_ERROR(-150036, "任务类型错误"),
    TASK_TAG_TYPE_ERROR(-150037, "任务标签不存在"),
    TASK_TAG_EXIST(-150038, "标签已存在"),
    TASK_NAME_SUFFIX_ERROR(-150039, "任务名称错误"),
    TASK_PUBLISH_SQL_UPLOAD_ERROR(-150040, "SQL上传OSS失败"),
    TASK_JAR_VALIDATE_UNSUPPORTED(-150041, "jar包任务不支持校验"),
    TASK_UDF_INVISIBLE(-150042, "udf没有project信息，或者udf和任务属于不同的project，不能调用"),
    TASK_NAME_TOO_LONG(-150043, "任务名称过长"),
    TASK_NAME_NO_SPACE(-150044, "任务名不包含空格"),
    TASK_LOCKED(-150045, "任务被其他用户锁定"),
    TASK_AND_FOLDER_PROJECT_NAME_NOT_MATCH(-150046, "任务与父节点属于不同项目空间"),
    RESOURCE_LOCKED(-150047, "FLINK运行基座K8S故障，平台发板封闭，请联系管理员或在'实时计算平台用户群'咨询"),
    TASK_NAME_NOT_MATCH(-150048, "任务名只能包含字母、数字、下划线"),
    TASK_APPLY_CREATE_ERROR(-150049, "任务审批流创建错误"),


    TASK_CUSTOM_OPTION_ERROR(-156000, "Flink高级参数不支持该OPTION"),
    TASK_CUSTOM_KEY_ERROR(-156001, "Flink高级参数KEY错误"),
    TASK_CUSTOM_NETWORK_FRACTION(-156002, "taskmanager.network.memory.fraction范围:0.1,0.2,0.3,0.4"),
    TASK_CUSTOM_TASKMANAGER_MEMORY_NETWORK_FRACTION(-156003, "taskmanager.memory.network.fraction范围:0.1,0.2,0.3,0.4"),
    TASK_CUSTOM_VALUE_ERROR(-156004, "Flink高级参数VALUE错误"),
    TASK_CUSTOM_MEMORY_MANAGED_ERROR(-156005, "taskmanager.memory.managed.fraction范围:大于0并且小于等于0.5"),

    TASK_INSTANCE_NOT_EXIST(-160000, "状态实例不存在"),
    TASK_INSTANCE_NOT_EXIST_MACHINE(-160003, "状态实例运行机器不存在"),

    TASK_VERSION_NOT_EXIST(-160001, "状态版本不存在"),
    TASK_VERSION_EXIST(-160002, "状态版本已存在"),

    JAR_EXIST(-170000, "jar已存在"),
    JAR_NOT_EXIST(-170001, "jar不存在"),
    TASK_JAR_RELATION_EXIST(-170002, "任务与jar包存在关联，不允许删除该jar包"),

    JAR_VERSION_EXIST(-180000, "jar版本已存在"),
    JAR_VERSION_NOT_EXIST(-180001, "jar版本不存在"),
    TASK_JAR_VERSION_RELATION_EXIST(-180002, "任务与jar包该版本存在关联，不允许删除该jar包版本"),
    JAR_VERSION_AT_LEAST_ONE(-180003, "至少保留1个版本"),

    TASK_FOLDER_EXIST(-190001, "文件夹名称已经存在"),
    TASK_FOLDER_PROJECT_NAME_NOT_EXIST(-190002, "文件夹操作需指定所属项目空间"),
    TASK_FOLDER_CHILDREN_EXIST(-190003, "文件夹包含子文件夹或子文件"),
    TASK_FOLDER_NOT_MATCH(-190004, "任务与父文件夹不属于同一个项目空间，禁止操作"),
    FOLDER_FOLDER_NOT_MATCH(-190004, "文件夹与父文件夹不属于同一个项目空间，禁止操作"),

    MONITOR_TEMPLATE_EXIST(-200001, "模板已存在"),
    MONITOR_TEMPLATE_NOT_EXIST(-200002, "模板不存在"),
    MONITOR_ITEM_EXIST(-200003, "该监控项已存在"),
    MONITOR_ITEM_NOT_EXIST(-200004, "该监控项不存在"),
    MONITOR_ALTER_TYPE_ERROR(-200005, "告警方式错误"),
    MONITOR_FEATURE_QUERY_ERROR(-200006, "特征查询失败"),
    MONITOR_FEATURE_ADD_ERROR(-200007, "特征新增失败"),
    MONITOR_FEATURE_UPDATE_ERROR(-200008, "特征更新失败"),
    MONITOR_FEATURE_DELETE_ERROR(-200009, "特征删除失败"),
    MONITOR_FEATURE_EXIST(-200010, "特征已存在"),
    MONITOR_FEATURE_NOT_EXIST(-200011, "特征不存在"),
    MONITOR_FEATURE_STATUS_UPDATE_ERROR(-200012, "特征状态更新失败"),
    MONITOR_RULE_QUERY_ERROR(-200013, "规则查询失败"),
    MONITOR_RULE_ADD_ERROR(-200014, "规则新增失败"),
    MONITOR_RULE_UPDATE_ERROR(-200015, "规则更新失败"),
    MONITOR_RULE_DELETE_ERROR(-200016, "规则删除失败"),
    MONITOR_RULE_EXIST(-200017, "规则已存在"),
    MONITOR_RULE_NOT_EXIST(-200018, "规则不存在"),
    MONITOR_RULE_STATUS_UPDATE_ERROR(-200019, "规则状态更新失败"),
    MONITOR_ITEM_CREATE_TYPE_ERROR(-200020, "创建类型错误"),
    MONITOR_ITEM_RULE_INFO_ERROR(-200021, "规则信息错误"),
    MONITOR_ITEM_TASK_NAME_NOT_EQUAL(-200022, "任务名称不一致"),
    MONITOR_RULE_DISABLED_ERROR(-200023, "规则禁止失败"),
    MONITOR_RULE_ENABLED_ERROR(-200024, "规则开启失败"),
    MONITOR_ITEM_TEMPLATE_ID_NOT_EXIST(-200025, "缺少模板ID"),
    MONITOR_ITEM_ALERT_TITLE_TASK_NAME(-200026, "报警标题必须包含任务名称"),
    MONITOR_ITEM_ALERT_TITLE_REPEAT(-200027, "报警标题重复"),
    MONITOR_WATERMARK_ERROR_1(-200028, "当告警判断为>或者>=的时候，电话告警水位必须大于等于飞书告警水位"),
    MONITOR_WATERMARK_ERROR_2(-200029, "当告警判断为<或者<=的时候，电话告警水位必须小于等于飞书告警水位"),

    TASK_DEBUG_PARSE_SQL_ERROR(-210000, "sql解析错误"),
    TASK_DEBUG_DATA_JSON_FIELD_ERROR(-210001, "source data中的field在connector中不存在"),

    TASK_PRESSURE_SINK_ANY_NOT_MODIFIED(-220000, "压测时必须修改所有的sink表配置，避免对生产任务造成影响"),
    TASK_PRESSURE_PARSE_SQL_ERROR(-220001, "sql解析错误"),
    TASK_PRESSURE_STATUS_NOT_RIGHT(-220002, "状态错误"),

    TASK_DIAGNOSIS_STATUS_NOT_RIGHT(-230000, "诊断状态错误"),
    TASK_DIAGNOSIS_STATUS_NOT_UN_TERMINAL(-230000, "任务状态错误：需要先将当前任务启动"),

    GATEWAY_SUBMIT_TYPE_NOT_EXIST(-600001, "SubmitType错误"),
    GATEWAY_SUBMIT_NOT_EXIST(-600002, "提交操作不存在"),
    GATEWAY_SUBMIT_RESULT_CONTENT(-600003, "提交结果不存在"),
    GATEWAY_SUBMIT_RESULT_CONTENT_ERROR(-600004, "提交结果错误"),
    GATEWAY_PUBLISH_JAR_DOWNLOAD_ERROR(-600005, "JAR下载失败"),
    GATEWAY_PUBLISH_MAIN_DOWNLOAD_ERROR(-600006, "main参数下载失败"),

    GATEWAY_PUBLISH_SHELL_FILE_ERROR(-600007, "发布脚本不存在"),
    GATEWAY_PUBLISH_EXECUTE_SHELL_ERROR(-600008, "执行发布脚本失败"),
    GATEWAY_PUBLISH_QUEUE_FULL(-600009, "提交机发布队列已满"),
    GATEWAY_PUBLISH_REPEAT(-600010, "禁止重复发布"),

    GATEWAY_KILL_SHELL_FILE_ERROR(-601007, "下线脚本不存在"),
    GATEWAY_KILL_EXECUTE_SHELL_ERROR(-601008, "执行下线脚本失败"),
    GATEWAY_KILL_QUEUE_FULL(-601009, "提交机下线队列已满"),
    GATEWAY_KILL_SAVEPOINT_ERROR(-601010, "保存savepoint失败"),
    GATEWAY_KILL_FAILED(-601011, "任务下线失败"),

    GATEWAY_OPERATE_PARAM_ERROR(-610001, "提交记录: 参数错误"),
    GATEWAY_OPERATE_SAVE_ERROR(-610002, "提交记录: 保存错误"),
    GATEWAY_OPERATE_SAVE_EXCEPTION(-610003, "提交记录: 保存异常"),
    GATEWAY_OPERATE_STATUS_ERROR(-610004, "提交记录: 状态错误"),

    GATEWAY_RESOURCE_TYPE_NOT_EXIST(-620001, "提交机不支持的资源类型"),

    GATEWAY_CLOSE(-630001, "当前K8S集群维护，暂时不允许任务上下线，恢复后会及时通知，请联系管理员或关注'实时计算平台用户群'通知"),

    GATEWAY_PARSE_CONNECTOR_ERROR(-640001, "gateway解析connector失败"),


    APP_INVALID_AUTH_TOKEN(-700001, "auth failed, invalid token"),
    APP_API_FLOW_CONTROL(-700002, "openApi flow control, invoke later"),


    COMMON_EMAIL_ERROR(-900000, "邮箱格式错误"),
    COMMON_EMAIL_NOT_EXIST(-900001, "邮箱不存在"),
    COMMON_AUTH_NOT(-900002, "无权限"),
    COMMON_NOT_OPERATOR(-900003, "不支持的操作"),
    HISTORY_ALTER_TYPE_NOT_EXIST(-900004, "报警类型不存在"),




    INTERNAL_ERROR(-1, "内部错误"),
    SYSTEM_EXCEPTION(-2, "系统异常"),
    SUCCESS(0, "成功");

    private int code;
    private String value;

    ResultEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static boolean isSuccess(int code) {
        if (code >= 0) {
            return true;
        }
        return false;
    }

    public static String value(int code) {
        return parseByCode(code).getValue();
    }

    public static ResultEnum parseByCode(int code) {
        if (code >= 0) {
            return SUCCESS;
        }

        for (ResultEnum statusEnum : ResultEnum.values()) {
            if (statusEnum.getCode() == code) {
                return statusEnum;
            }
        }
        return INTERNAL_ERROR;
    }


    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}

