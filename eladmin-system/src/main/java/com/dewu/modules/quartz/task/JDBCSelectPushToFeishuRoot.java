package com.dewu.modules.quartz.task;

import com.alibaba.fastjson.JSONObject;
import com.dewu.flink.template.job.bot.feishu.BotClient;
import com.dewu.flink.template.job.bot.feishu.BotSendRequest;
import com.dewu.flink.template.job.bot.feishu.enums.BotReceiveIdType;
import com.dewu.flink.template.job.bot.feishu.enums.MsgType;
import com.dewu.flink.template.job.bot.feishu.enums.TemplateType;
import com.dewu.flink.template.job.bot.feishu.mestemplate.CommonTextTemplate;
import com.dewu.flink.template.utils.JsonUtil;
import com.dewu.utils.RedisUtils;
import com.dewu.utils.StringUtils;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;


/**
 * @description: /
 * @Author: lmh
 * @date: 2024/1/11 13:59
 */
@Component
@Slf4j
@RequiredArgsConstructor
//@EnableScheduling
public class JDBCSelectPushToFeishuRoot {

    private final BotClient botClient;
    private final RedisUtils redisUtils;

    public void pushToFeishu(String arg) {
        try {
            log.info("JDBCSelectPushToFeishuRoot start.");
            TaskInstance taskInstance = JsonUtil.parseObject(arg, TaskInstance.class);
            taskInstance.execute(botClient, redisUtils);
        } catch (Exception e) {
            log.error("JDBCSelectPushToFeishuRoot error .arg:{}", arg);
            throw new RuntimeException(e);
        }
    }

    @Data
    public static class TaskInstance {
        public String url;
        public String dbName;
        public String userName;
        public String password;
        public String sqlQuery;
        public String sqlStmtArg;
        private volatile transient Connection connection;
        public String feishuReceiveId;
        public String feishuReceiveIdType;
        public String feishuTemplate_id;
        public String feishuContent;
        public String feishuUUID;
        public int curb = -1;
        public String feishuMsgTitle;

        public void innitConnection() throws SQLException, ClassNotFoundException {
            if (connection == null) {
                synchronized (this) {
                    if (connection == null) {
                        try {
                            Class.forName("com.mysql.jdbc.Driver");
                        } catch (Exception ex) {
                            Class.forName("com.mysql.cj.jdbc.Driver");
                        }
                        connection = DriverManager.getConnection(dbName == null ? url : url + "/" + dbName, userName, password);
                    }
                }
            }
        }

        public boolean execute(BotClient botClient, RedisUtils redisUtils) throws SQLException, ClassNotFoundException, JsonProcessingException {
            List<Map<String, Object>> maps = executeQuery(sqlQuery);

            if (maps == null || maps.isEmpty()) {
                return true;
            }

            BotSendRequest botSendRequest = buildSendRequest(maps);

            if (curb > 0 && StringUtils.isNotEmpty(feishuUUID)) {
                String key = "EJSPTR:" + feishuUUID;
                String type = String.valueOf(redisUtils.get(key));
                if ("1".equals(type)) {
                    return true;
                } else {
                    redisUtils.set("EJSPTR:" + feishuUUID, "1", curb, TimeUnit.MINUTES);
                }
            }

            JSONObject jsonObject = botClient.sendMessagesSync(
                    botSendRequest,
                    feishuReceiveIdType == null ? BotReceiveIdType.EMAIL : BotReceiveIdType.typeOf(feishuReceiveIdType)
            );

            if (jsonObject == null || 0 != Optional.ofNullable(jsonObject.getLong("code")).orElse(9999L)) {
                log.error("JDBCSelectPushToFeishuRoot execute error.body:{}; request:{};", jsonObject == null ? "null" : jsonObject.toJSONString(), botSendRequest);
                return false;
            }

            return true;
        }

        public BotSendRequest buildSendRequest(List<Map<String, Object>> maps) throws JsonProcessingException {


            // TODO: 2024/1/11 table 支持多行
            if (StringUtils.isNotEmpty(feishuTemplate_id)) {

                StrSubstitutor substitutor = new StrSubstitutor(maps.get(0));
                String newContent = substitutor.replace(feishuContent);

                HashMap<String, Object> msgMap = new HashMap<>();
                HashMap<String, Object> msgDataMap = new HashMap<>();
                msgDataMap.put("template_id", feishuTemplate_id);
                JSONObject jsonObject = JSONObject.parseObject(newContent);
                jsonObject.put("fs_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                jsonObject.put("fs_title", feishuMsgTitle == null ? "自定义消息" : feishuMsgTitle);
                msgDataMap.put("template_variable", jsonObject);
                msgMap.put("type", "template");
                msgMap.put("data", msgDataMap);

                return new BotSendRequest(
                        feishuReceiveId,
                        JsonUtil.writeString(msgMap),
                        MsgType.INTERACTIVE
                );

            }

            StringBuffer buffer = new StringBuffer();
            maps.forEach(stringObjectMap -> {
                for (String s : stringObjectMap.keySet()) {
                    buffer.append(s).append(":");
                    buffer.append(stringObjectMap.get(s)).append("\n");
                }
            });

            return new BotSendRequest(
                    feishuReceiveId,
                    CommonTextTemplate.buildSimpleMsg(
                            feishuMsgTitle == null ? "自定义消息" : feishuMsgTitle,
                            buffer.toString(),
                            TemplateType.GREEN
                    ),
                    MsgType.INTERACTIVE
            );

        }

        private List<Map<String, Object>> executeQuery(String sqlQuery) throws ClassNotFoundException, SQLException {
            if (sqlQuery == null) {
                return null;
            }
            innitConnection();
            PreparedStatement stmt = connection.prepareStatement(sqlQuery, ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
            if (StringUtils.isNotEmpty(sqlStmtArg)) {
                String[] split = sqlStmtArg.split(",");
                for (int i = 0; i < split.length; i++) {
                    stmt.setString(i + 1, split[i]);
                }
            }
            ResultSet rs = stmt.executeQuery();
            rs.next();
            ResultSetMetaData meta = rs.getMetaData();
            int columns = meta.getColumnCount();
            List<Map<String, Object>> list = new ArrayList<>();
            int currRowIndex = rs.getRow();
            rs.beforeFirst();
            while (rs.next()) {
                Map<String, Object> row = new HashMap<>(columns);
                for (int i = 1; i <= columns; ++i) {
                    row.put(meta.getColumnName(i), rs.getObject(i));
                }
                list.add(row);
            }
            rs.absolute(currRowIndex);
            rs.close();
            close();
            return list;
        }

        public void close() {
            if (connection == null) {
                return;
            }
            try {
                connection.close();
            } catch (SQLException e) {
                log.error("JDBC connection close failed.", e);
            } finally {
                connection = null;
            }
        }
    }

//    @Scheduled(fixedRate = 10000)
//    public void test() throws SQLException, JsonProcessingException, ClassNotFoundException {
//        String str = "{\"url\":\"******************************\",\"dbName\":\"dw_bigdata_realtime_dev\",\"userName\":\"dw_bigdata_realtime_dev\",\"password\":\"since20240111\",\"sqlQuery\":\"with tmp_media as( SELECT *, CASE when account_id ='84cfa0f06317038487e8' then '实验账户' else '对照账户' end as bucket_group FROM `increase_ad_hard_cube_metric` WHERE `media_id` ='12' AND `account_id` in ('2ebc39449e1075a074ee','84cfa0f06317038487e8') AND `dim_type`='planDateDim' AND `date`=current_date() and `producer`='main') ,tmp_agg as ( SELECT `date`, count( case when bucket_group ='实验账户' then 1 else NULL end) as 实验组计划数, count( case when bucket_group ='对照账户' then 1 else NULL end) as 对照组计划数, sum( case when bucket_group ='对照账户' then view else 0 end) as 对照组展现, sum( case when bucket_group ='实验账户' then view else 0 end) as 实验组展现, sum( case when bucket_group ='对照账户' then click else 0 end) as 对照组点击, sum( case when bucket_group ='实验账户' then click else 0 end) as 实验组点击, sum( case when bucket_group ='对照账户' then cost else 0 end) / 100 as 对照组消耗, sum( case when bucket_group ='实验账户' then cost else 0 end) / 100 as 实验组消耗, sum( case when bucket_group ='对照账户' then activate else 0 end) as 对照组激活, sum( case when bucket_group ='实验账户' then activate else 0 end) as 实验组激活, sum( case when bucket_group ='对照账户' then order_count else 0 end) as 对照组订单, sum( case when bucket_group ='实验账户' then order_count else 0 end) as 实验组订单, sum( case when bucket_group ='对照账户' then `real_cost` else 0 end) / 100 as 对照组实际消耗, sum( case when bucket_group ='实验账户' then `real_cost` else 0 end) / 100 as 实验组实际消耗, max( case when bucket_group ='实验账户' then `current_time` else null end) as 实验组最后更新时间, max( case when bucket_group ='对照账户' then `current_time` else null end) as 对照组最后更新时间 FROM tmp_media GROUP BY `date` ) SELECT *, now() as time, hour(now()) as hour, 'vivo应用市场' as media, '指标' as name1, '实验组'as name2, '对照组' as name3, case when 实验组激活 is null or 实验组激活 = 0 then 0 else ROUND(实验组实际消耗/实验组激活,2) end as 实验组激活cac, case when 对照组激活 is null or 对照组激活 = 0 then 0 else ROUND(对照组实际消耗/对照组激活,2) end as 对照组激活cac, case when 实验组订单 is null or 实验组订单 = 0 then 0 else ROUND(实验组实际消耗/实验组订单,2) end as 实验组订单cac, case when 对照组订单 is null or 对照组订单 = 0 then 0 else ROUND(对照组实际消耗/对照组订单,2) end as 对照组订单cac FROM tmp_agg\",\"feishuReceiveId\":\"<EMAIL>\",\"feishuReceiveIdType\":\"email\",\"feishuContent\":\"{\\\"group_table\\\":[{\\\"metric_name\\\":\\\"计划数\\\",\\\"A_value\\\":\\\"${实验组计划数}\\\",\\\"B_value\\\":\\\"${对照组计划数}\\\"},{\\\"metric_name\\\":\\\"展现\\\",\\\"A_value\\\":\\\"${实验组展现}\\\",\\\"B_value\\\":\\\"${对照组展现}\\\"},{\\\"metric_name\\\":\\\"点击\\\",\\\"A_value\\\":\\\"${实验组点击}\\\",\\\"B_value\\\":\\\"${对照组点击}\\\"},{\\\"metric_name\\\":\\\"消耗\\\",\\\"A_value\\\":\\\"${实验组消耗}\\\",\\\"B_value\\\":\\\"${对照组消耗}\\\"},{\\\"metric_name\\\":\\\"订单\\\",\\\"A_value\\\":\\\"${实验组订单}\\\",\\\"B_value\\\":\\\"${对照组订单}\\\"},{\\\"metric_name\\\":\\\"激活\\\",\\\"A_value\\\":\\\"${实验组激活}\\\",\\\"B_value\\\":\\\"${对照组激活}\\\"},{\\\"metric_name\\\":\\\"激活cac\\\",\\\"A_value\\\":\\\"${实验组激活cac}\\\",\\\"B_value\\\":\\\"${对照组激活cac}\\\"},{\\\"metric_name\\\":\\\"订单cac\\\",\\\"A_value\\\":\\\"${实验组订单cac}\\\",\\\"B_value\\\":\\\"${对照组订单cac}\\\"}],\\\"simple_msg\\\":\\\"**日期：**${date}\\\\n**hour：**${hour}\\\\n**渠道：** ${media}\\\",\\\"time\\\":\\\"${time}\\\",\\\"name1\\\":\\\"${name1}\\\",\\\"name2\\\":\\\"${name2}\\\",\\\"name3\\\":\\\"${name3}\\\"}\",\"feishuTemplate_id\":\"ctp_AAr5mVC8od5s\",\"feishuMsgTitle\":\"vivo应用市场算法实验统计\"}";
//        pushToFeishu(str);
//    }


}
