//package com.dewu.modules.quartz.config;
//
//import com.dewu.flink.template.utils.ARKUtils;
//import lombok.RequiredArgsConstructor;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.boot.ApplicationArguments;
//import org.springframework.boot.ApplicationRunner;
//import org.springframework.stereotype.Component;
//
//@Component
//@RequiredArgsConstructor
//public class ARKRunner implements ApplicationRunner {
//
//    private static final Logger log = LoggerFactory.getLogger(ARKRunner.class);
//
//    private String accessKey;
//
//    private String accessKeySecret;
//
//    /**
//     * 项目启动时重新激活启用的定时任务
//     *
//     * @param applicationArguments /
//     */
//    @Override
//    public void run(ApplicationArguments applicationArguments) {
//        try {
//            ARKUtils.AccessKeyValue accessKeyValue = ARKUtils.getAccessKeyValue();
//            accessKey = accessKeyValue.getKey();
//            accessKeySecret = accessKeyValue.getValue();
//        }catch (Exception e) {
//            log.error("配置中心获取oss信息失败",e);
//        }
//    }
//
//    public String getAccessKey() {
//        return accessKey;
//    }
//
//    public void setAccessKey(String accessKey) {
//        this.accessKey = accessKey;
//    }
//
//    public String getAccessKeySecret() {
//        return accessKeySecret;
//    }
//
//    public void setAccessKeySecret(String accessKeySecret) {
//        this.accessKeySecret = accessKeySecret;
//    }
//}
//
