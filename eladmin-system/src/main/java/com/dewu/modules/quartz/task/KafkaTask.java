package com.dewu.modules.quartz.task;

import com.alibaba.fastjson.JSON;
import com.dewu.rule.domain.MetricsTaskOperatorBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class KafkaTask {

    @Value("${kafka.ip}")
    private String ip;

    @Value("${kafka.job_status_sync_topic}")
    private String jobStatusSyncTopic;

    @Value("${kafka.job_status_monitor_topic}")
    private String jobStatusMonitorTopic;


    public void sendMessMonitorStatus(MetricsTaskOperatorBean metricsTaskOperatorBean) {
        Properties props = new Properties();
        props.put("bootstrap.servers", ip);
        props.put("acks", "all");
        props.put("retries", 3);
        props.put("max.in.flight.requests.per.connection", 1);
        props.put("batch.size", 16384);
        props.put("linger.ms", 1);
        props.put("buffer.memory", 33554432);
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        Producer<String, String> producer = new KafkaProducer<>(props);
        producer.send(new ProducerRecord<String, String>(jobStatusMonitorTopic, metricsTaskOperatorBean.getJobId(), JSON.toJSONString(metricsTaskOperatorBean)), new Callback() {
            @Override
            public void onCompletion(RecordMetadata metadata, Exception exception) {
                if (exception == null) {
                    log.info(" success-> offset:" + metadata.offset() + "  partiton:" + metadata.partition());
                } else {
                    exception.printStackTrace();
                }
            }
        });
        producer.close();
    }



    public KafkaConsumer consumerMessCommitSync() {
        Properties props = new Properties();
        props.put("bootstrap.servers", ip);
        props.put("ack", "all");
        props.put("enable.auto.commit", "false");
        props.put("auto.offset.reset", "earliest");
        props.put("group.id", "realtime-property-platform-dispatch");
        props.setProperty("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.setProperty("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");

        KafkaConsumer kafkaConsumer = new KafkaConsumer<>(props);
        kafkaConsumer.subscribe(Arrays.asList(jobStatusMonitorTopic));
        return kafkaConsumer;
    }

    public void sendMessSyncStatus(MetricsTaskOperatorBean metricsTaskOperatorBean) {
        Properties props = new Properties();
        props.put("bootstrap.servers", ip);
        props.put("acks", "all");
        props.put("retries", 3);
        props.put("max.in.flight.requests.per.connection", 1);
        props.put("batch.size", 16384);
        props.put("linger.ms", 1);
        props.put("buffer.memory", 33554432);
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        Producer<String, String> producer = new KafkaProducer<>(props);
        producer.send(new ProducerRecord<String, String>(jobStatusSyncTopic, metricsTaskOperatorBean.getJobId(), JSON.toJSONString(metricsTaskOperatorBean)), new Callback() {
            @Override
            public void onCompletion(RecordMetadata metadata, Exception exception) {
                if (exception == null) {
                    log.info(" success-> offset:" + metadata.offset() + "  partiton:" + metadata.partition());
                } else {
                    log.info("通知指标平台的消息发送kafka失败");
                    exception.printStackTrace();
                }
            }
        });
        producer.close();
    }

}
