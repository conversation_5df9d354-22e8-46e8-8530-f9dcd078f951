package com.dewu.modules.quartz.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dewu.modules.quartz.libra.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.bind.DatatypeConverter;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class PrometheusOperateComponent {
    private static final Logger logger = LoggerFactory.getLogger(PrometheusOperateComponent.class);

//    public static String PROMETHEUS_DEV = "http://k8s-sh-cube-dev.shizhuang-inc.net/api/v1/query?";
//    public static String PROMETHEUS_PRD = "http://libra-prometheus.shizhuang-inc.com/api/v1/query?";
//    public static String PROMETHEUS_QUERY_RANGE_PRD = "http://libra-prometheus.shizhuang-inc.com/api/v1/query_range?";

    private final String prometheusHost;
    private final String prometheusUsername;
    private final String prometheusPassword;

    public PrometheusOperateComponent(String prometheusHost, String prometheusUsername, String prometheusPassword) {
        this.prometheusHost = prometheusHost;
        this.prometheusUsername = prometheusUsername;
        this.prometheusPassword = prometheusPassword;
    }

    public PrometheusBean query(QueryParam param) {
        String url = "http://" + this.prometheusHost + "/api/v1/query?";
//        logger.info("query param: {}, {}", url, JSON.toJSONString(param));
        try {
            if (StringUtils.isBlank(param.getQuery())) {
                return null;
            }
            url += "query=" + param.getQuery();
            if (param.getTime() > 0) {
                url += "&time=" + param.getTime();
            }
            if (param.getTimeout() > 0) {
                url += "&timeout=" + param.getTimeout();
            }

            String encoding = DatatypeConverter.printBase64Binary((prometheusUsername + ":" + prometheusPassword).getBytes("UTF-8"));
            BasicHeader header = new BasicHeader("Authorization", "Basic " + encoding);

            HttpResult httpResult = LibraHttpUtils.get(url, header);
            if (httpResult.getCode() != 200) {
                return new PrometheusBean("error");
            }
            return JSON.parseObject(httpResult.getContent(), PrometheusBean.class);
        } catch (Exception ex) {
            logger.error("query error: {}, {}, {}", url, JSON.toJSONString(param), ExceptionUtils.getStackTrace(ex));
            return new PrometheusBean("error");
        }
    }

    public PrometheusBean queryRange(QueryRangeParam param) {
        String url = "http://" + this.prometheusHost + "/api/v1/query_range?";
//        logger.info("queryRange param: {}, {}", url, JSON.toJSONString(param));
        try {
            if (StringUtils.isBlank(param.getQuery())) {
                return null;
            }
            url += "query=" + param.getQuery();
            if (param.getStart() > 0) {
                url += "&start=" + param.getStart();
            }
            if (param.getEnd() > 0) {
                url += "&end=" + param.getEnd();
            }
            if (param.getStep() > 0) {
                url += "&step=" + param.getStep();
            }
            if (param.getTimeout() > 0) {
                url += "&timeout=" + param.getTimeout();
            }

            String encoding = DatatypeConverter.printBase64Binary((prometheusUsername + ":" + prometheusPassword).getBytes("UTF-8"));
            BasicHeader header = new BasicHeader("Authorization", "Basic " + encoding);

            HttpResult httpResult = LibraHttpUtils.get(url, header);
            return JSON.parseObject(httpResult.getContent(), PrometheusBean.class);
        } catch (Exception ex) {
            logger.error("query error: {}, {}, {}", url, JSON.toJSONString(param), ExceptionUtils.getStackTrace(ex));
            return new PrometheusBean("error");
        }
    }

    private static class metricsWithInstId {
        public Long instId;
        public Double value;

        public metricsWithInstId(Long instId, Double value) {
            this.instId = instId;
            this.value = value;
        }

        public String toString() {
            return String.format("instid: %d value: %s", instId, value);
        }
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        PrometheusOperateComponent component = new PrometheusOperateComponent(
                "apm-metrics-center-api.shizhuang-inc.com/t1",
                "data-warehouse",
                "ea88d3da0e57494a7006a9439b39aeb3"
        );
        PrometheusBean result = component.query(new QueryParam(LibraURLUtils.encode("sum(increase(flink_jobmanager_job_numberOfFailedCheckpoints{}[20m])) by  (application, task_inst_id)")));
        JSONArray resultJsonArray = Optional.ofNullable(result).map(e -> e.getData()).map(e -> e.getJSONArray("result")).orElse(null);
        Map<String, metricsWithInstId> resultMap = new HashMap<>();
        resultJsonArray.stream().forEach(e -> {
            JSONObject jsonObject = JSON.parseObject(e.toString());
            if(jsonObject.getJSONObject("metric").size() < 1) {
                return;
            }
            String taskId = jsonObject.getJSONObject("metric")
                    .getString("application")
                    .replaceAll("flink-", StringUtils.EMPTY);
            Long taskInstId = Optional.ofNullable(jsonObject.getJSONObject("metric"))
                    .map(a -> a.getString("task_inst_id"))
                    .map(Long::valueOf)
                    .orElse(null);

            Double requestCores = jsonObject.getJSONArray("value").getDouble(1);

            System.err.printf("%d, %s\n", taskInstId, requestCores);
            System.out.println("taskId:"+taskId+" taskInstId"+taskInstId+ "-requestCores----"+ requestCores);

            resultMap.compute(taskId, (key, oldValue) ->(
                    oldValue == null || oldValue.instId == null ||
                            (taskInstId != null && taskInstId > oldValue.instId) ?
                            new metricsWithInstId(taskInstId , requestCores) :
                            oldValue)
            );
        });

        for (Map.Entry<String, metricsWithInstId> a: resultMap.entrySet()) {
            System.err.println(a.getValue());
        }
    }
}

