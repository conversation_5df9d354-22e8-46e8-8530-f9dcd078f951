package com.dewu.modules.quartz.libra;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLEncoder;

public class LibraURLUtils {
    private static final Logger logger = LoggerFactory.getLogger(LibraURLUtils.class);

    public static String encode(String str) {
        if (str == null) {
            return null;
        }
        try {
            return URLEncoder.encode(str, "UTF-8");
        } catch (Exception ex) {
            logger.error("encode error: {}, {}", str, ExceptionUtils.getStackTrace(ex));
            return str;
        }
    }
}

