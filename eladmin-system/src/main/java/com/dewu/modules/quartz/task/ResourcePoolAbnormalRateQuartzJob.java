package com.dewu.modules.quartz.task;


import com.dewu.flink.template.job.domain.InstanceCpuInfo;
import com.dewu.flink.template.job.service.InstanceCpuInfoService;
import com.poizon.metrics.client.MetricsClient;
import com.poizon.metrics.client.MetricsCluster;
import com.poizon.metrics.core.converter.query.QueryResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * Author: WangLin
 * Date: 2024/12/02 10:35
 * Description: 资源池异常率查询-5分钟查询一次
 */
@Component
public class ResourcePoolAbnormalRateQuartzJob {
    private static final Logger LOG = LoggerFactory.getLogger(ResourcePoolAbnormalRateQuartzJob.class);

    @Value("${dw.poizon-metrics.url}")
    private String url;

    @Value("${dw.poizon-metrics.accessId}")
    private String accessId;

    @Value("${dw.poizon-metrics.accessKey}")
    private String accessKey;

    private static final String PROJECT = "default";

    private MetricsClient metricsClient;

    private static final String promql = "(label_replace((1 - min(sum(rate(node_cpu_seconds_total{mode=\"idle\",instance!~\"\",cluster=~\"k8s-prd-flink-hz-1|k8s-prd-flink-hz-2\"}[1m]offset 1m))by(cluster,instance,cpu))by(instance)),\"instance\",\"$1\",\"instance\",\"(.*):9100\")+ on(instance) group_left(pool)apm_metadata_k8s_node_info{cluster=~\"k8s-prd-flink-hz-1|k8s-prd-flink-hz-2\",pool=~\"tech-data-dw2-amd8.*\"}) * on(instance)group_left(node,cluster)label_move(kube_node_info{},\"internal_ip\",\"instance\")";
    private static final String promql_avg = "(label_replace((1 - avg(sum(rate(node_cpu_seconds_total{mode=\"idle\",instance!~\"\",cluster=~\"k8s-prd-flink-hz-1|k8s-prd-flink-hz-2\"}[1m]offset 1m))by(cluster,instance,cpu))by(instance)),\"instance\",\"$1\",\"instance\",\"(.*):9100\")+ on(instance) group_left(pool)apm_metadata_k8s_node_info{cluster=~\"k8s-prd-flink-hz-1|k8s-prd-flink-hz-2\",pool=~\"tech-data-dw2-amd8.*\"}) * on(instance)group_left(node,cluster)label_move(kube_node_info{},\"internal_ip\",\"instance\")";


    @PostConstruct
    public void init() {
        this.metricsClient = MetricsCluster.getMetricsClient(url + PROJECT, accessId, accessKey);
    }

    @Autowired
    private InstanceCpuInfoService instanceCpuInfoService;


    /**
     * 会由页面上的quartz定时任务执行，这里是入口
     */
    protected void runTask() {
        LOG.info("start get cpu usage metrics------------------------------");
        List<InstanceCpuInfo> instanceCpuInfoList = getInstanceCpuInfo();
        instanceCpuInfoService.createBatch(instanceCpuInfoList);
        LOG.info("this batch num of cpu usage metrics is {}", instanceCpuInfoList.size());

        LOG.info("start get cpu usage avg metrics------------------------------");
        List<InstanceCpuInfo> instanceCpuInfoListAvg = getInstanceCpuInfoAvg();
        instanceCpuInfoService.createBatch(instanceCpuInfoListAvg);
        LOG.info("this batch num of cpu usage avg metrics is {}", instanceCpuInfoListAvg.size());

    }

    /**
     * 通过promql 获取cpu的使用率
     */
    public List<InstanceCpuInfo> getInstanceCpuInfo() {
        List<QueryResult> queryResultList = metricsClient.query(promql);
        return queryResultList.stream().map(item -> {
            Map<String, String> metric = item.getMetric();
            List<Object> value = item.getValue();

            InstanceCpuInfo instanceCpuInfo = new InstanceCpuInfo();
            instanceCpuInfo.setCluster(metric.get("cluster"));
            instanceCpuInfo.setPool(metric.get("pool"));
            instanceCpuInfo.setInstanceIpPort(metric.get("instance"));
            instanceCpuInfo.setNode(metric.get("node"));

            instanceCpuInfo.setQueryTime((Integer) value.get(0));
            instanceCpuInfo.setUsageRate((String) value.get(1));
            instanceCpuInfo.setUsageRateAvg("0");

            return instanceCpuInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 通过promql 获取cpu的平均使用率
     */
    public List<InstanceCpuInfo> getInstanceCpuInfoAvg() {
        List<QueryResult> queryResultList = metricsClient.query(promql_avg);
        return queryResultList.stream().map(item -> {
            Map<String, String> metric = item.getMetric();
            List<Object> value = item.getValue();

            InstanceCpuInfo instanceCpuInfo = new InstanceCpuInfo();
            instanceCpuInfo.setCluster(metric.get("cluster"));
            instanceCpuInfo.setPool(metric.get("pool"));
            instanceCpuInfo.setInstanceIpPort(metric.get("instance"));
            instanceCpuInfo.setNode(metric.get("node"));

            instanceCpuInfo.setQueryTime((Integer) value.get(0));
            instanceCpuInfo.setUsageRateAvg((String) value.get(1));
            instanceCpuInfo.setUsageRate("0");

            return instanceCpuInfo;
        }).collect(Collectors.toList());
    }
}
