package com.dewu.modules.quartz.dataline;

import java.util.Objects;

public class LineageResultV2 {

    private String sourceCatalog;

    private String sourceDatabase;

    private String sourceTable;

    private String targetCatalog;

    private String targetDatabase;

    private String targetTable;

    public LineageResultV2(
            String sourceCatalog,
            String sourceDatabase,
            String sourceTable,
            String targetCatalog,
            String targetDatabase,
            String targetTable) {
        this.sourceCatalog = sourceCatalog;
        this.sourceDatabase = sourceDatabase;
        this.sourceTable = sourceTable;
        this.targetCatalog = targetCatalog;
        this.targetDatabase = targetDatabase;
        this.targetTable = targetTable;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LineageResultV2 that = (LineageResultV2) o;
        return Objects.equals(sourceCatalog, that.sourceCatalog) && Objects.equals(
                sourceDatabase,
                that.sourceDatabase) && Objects.equals(sourceTable, that.sourceTable)
                && Objects.equals(targetCatalog, that.targetCatalog) && Objects.equals(
                targetDatabase,
                that.targetDatabase) && Objects.equals(targetTable, that.targetTable);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
                sourceCatalog,
                sourceDatabase,
                sourceTable,
                targetCatalog,
                targetDatabase,
                targetTable);
    }

    @Override
    public String toString() {
        return "LineageResultV2{" +
                "sourceCatalog='" + sourceCatalog + '\'' +
                ", sourceDatabase='" + sourceDatabase + '\'' +
                ", sourceTable='" + sourceTable + '\'' +
                ", targetCatalog='" + targetCatalog + '\'' +
                ", targetDatabase='" + targetDatabase + '\'' +
                ", targetTable='" + targetTable + '\'' +
                '}';
    }
}
