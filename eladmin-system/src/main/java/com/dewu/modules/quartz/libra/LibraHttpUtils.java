package com.dewu.modules.quartz.libra;

import com.alibaba.fastjson.JSON;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.*;
import org.apache.http.client.CookieStore;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLInitializationException;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.net.URL;
import java.nio.charset.Charset;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

public class LibraHttpUtils {
    private static final Logger logger = LoggerFactory.getLogger(LibraHttpUtils.class);

    private static final int CONNECT_TIMEOUT = 30000;
    private static final int SOCKET_TIMEOUT = 90000;

    //如果不设置Content-type即为该方式，提交的数据按照 key1=val1&key2=val2 的方式进行编码
    public static final Header FORM_URLENCODED = new BasicHeader("Content-type", "application/x-www-form-urlencoded;charset=UTF-8");
    //用于上传表单和文件时
    public static final Header FORM_DATA = new BasicHeader("Content-type", "multipart/form-data;charset=UTF-8");
    //用于复杂数据传输，通过JSON优势进行编码解码
    public static final Header FORM_JSON = new BasicHeader("Content-type", "application/json;charset=UTF-8");

    private static final Retryer<HttpResult> RETRY_TOOL = RetryerBuilder.<HttpResult>newBuilder()
            .retryIfException()
            .retryIfResult(res -> Optional.ofNullable(res).map(e -> e.getCode() >= 500).orElse(true))
            .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS, 1, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterAttempt(5))
            .build();


    //    final static Executor executor;
    final static PoolingHttpClientConnectionManager connectionManager;
    final static CloseableHttpClient httpClient;
    final static RequestConfig requestConfig;

    static {
        LayeredConnectionSocketFactory ssl = null;
        try {
            ssl = SSLConnectionSocketFactory.getSystemSocketFactory();
        } catch (final SSLInitializationException ex) {
            final SSLContext sslcontext;
            try {
                sslcontext = SSLContext.getInstance(SSLConnectionSocketFactory.TLS);
                sslcontext.init(null, null, null);
                ssl = new SSLConnectionSocketFactory(sslcontext);
            } catch (final SecurityException ignore) {
                logger.error("SecurityException: {}", ExceptionUtils.getStackTrace(ignore));
            } catch (final KeyManagementException ignore) {
                logger.error("KeyManagementException: {}", ExceptionUtils.getStackTrace(ignore));
            } catch (final NoSuchAlgorithmException ignore) {
                logger.error("NoSuchAlgorithmException: {}", ExceptionUtils.getStackTrace(ignore));
            }
        }

        final Registry<ConnectionSocketFactory> sfr = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", ssl != null ? ssl : SSLConnectionSocketFactory.getSocketFactory())
                .build();

        connectionManager = new PoolingHttpClientConnectionManager(sfr);
        connectionManager.setDefaultMaxPerRoute(200);
        connectionManager.setMaxTotal(500);
        connectionManager.setValidateAfterInactivity(1000);
        httpClient = HttpClientBuilder.create()
                .setConnectionManager(connectionManager)
                .disableAutomaticRetries()
                .build();
        requestConfig = RequestConfig
                .custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
    }

    public static HttpResult post(String url, Map<String, String> param, Header... headers) {
        try {
            return RETRY_TOOL.call(() -> {
                List<NameValuePair> nameValuePairList = Lists.newArrayList();
                if (MapUtils.isNotEmpty(param)) {
                    for (Map.Entry<String, String> entry : param.entrySet()) {
                        nameValuePairList.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                    }
                }
                UrlEncodedFormEntity encodedFormEntity = new UrlEncodedFormEntity(nameValuePairList, Consts.UTF_8);
                HttpPost httpPost = new HttpPost(url);
                httpPost.setHeaders(headers);
                httpPost.setConfig(requestConfig);
                httpPost.setEntity(encodedFormEntity);

                return parseResponse(httpClient.execute(httpPost));
            });
        } catch (Exception ex) {
            logger.error("post error: {}, {}, {}", url, JSON.toJSONString(param), ExceptionUtils.getStackTrace(ex));
            return new HttpResult(503, ex.toString());
        }
    }

    public static HttpResult post(String url, HttpEntity httpEntity, Header... headers) {
        try {
            return RETRY_TOOL.call(() -> {
                HttpPost httpPost = new HttpPost(url);
                httpPost.setHeaders(headers);
                httpPost.setConfig(requestConfig);
                httpPost.setEntity(httpEntity);

                return parseResponse(httpClient.execute(httpPost));
            });
        } catch (Exception ex) {
            logger.error("post error: {}, {}", url, ExceptionUtils.getStackTrace(ex));
            return new HttpResult(503, ex.getMessage());
        }
    }

    public static HttpResult post(String url, String content, Header... headers) {
        try {
            return RETRY_TOOL.call(() -> {
                StringEntity stringEntity = new StringEntity(content, ContentType.APPLICATION_JSON);
                HttpPost httpPost = new HttpPost(url);
                httpPost.setHeaders(headers);
                httpPost.setConfig(requestConfig);
                httpPost.setEntity(stringEntity);

                return parseResponse(httpClient.execute(httpPost));
            });
        } catch (Exception ex) {
            logger.error("post error: {}, {}", url, ExceptionUtils.getStackTrace(ex));
            return new HttpResult(503, ex.getMessage());
        }
    }

    public static HttpResult patch(String url, Header... headers) {
        try {
            return RETRY_TOOL.call(() -> {
                HttpPatch httpPatch = new HttpPatch(url);
                httpPatch.setHeaders(headers);
                return parseResponse(httpClient.execute(httpPatch));
            });
        } catch (Exception ex) {
            logger.error("patch error: {}, {}", url, ExceptionUtils.getStackTrace(ex));
            return new HttpResult(503, ex.getMessage());
        }
    }

    public static HttpResult patch(String url, String content, Header... headers) {
        try {
            return RETRY_TOOL.call(() -> {
                StringEntity stringEntity = new StringEntity(content, ContentType.APPLICATION_JSON);
                HttpPatch httpPatch = new HttpPatch(url);
                httpPatch.setHeaders(headers);
                httpPatch.setEntity(stringEntity);
                return parseResponse(httpClient.execute(httpPatch));
            });
        } catch (Exception ex) {
            logger.error("patch error: {}, {}", url, ExceptionUtils.getStackTrace(ex));
            return new HttpResult(503, ex.getMessage());
        }
    }

    public static HttpResult put(String url, Header... headers) {
        try {
            return RETRY_TOOL.call(() -> {
                HttpPut httpPut = new HttpPut(url);
                httpPut.setHeaders(headers);

                return parseResponse(httpClient.execute(httpPut));
            });
        } catch (Exception ex) {
            logger.error("put error: {}, {}", url, ExceptionUtils.getStackTrace(ex));
            return new HttpResult(503, ex.getMessage());
        }
    }

    public static HttpResult delete(String url, Header... headers) {
        try {
            return RETRY_TOOL.call(() -> {
                HttpDelete httpDelete = new HttpDelete(url);
                httpDelete.setHeaders(headers);
                return parseResponse(httpClient.execute(httpDelete));
            });
        } catch (Exception ex) {
            logger.error("delete error: {}, {}", url, ExceptionUtils.getStackTrace(ex));
            return new HttpResult(503, ex.getMessage());
        }
    }

    public static HttpResult put(String url, String content, Header... headers) {
        try {
            return RETRY_TOOL.call(() -> {
                HttpPut httpPut = new HttpPut(url);
                httpPut.setHeaders(headers);
                StringEntity stringEntity = new StringEntity(content, ContentType.APPLICATION_JSON);
                httpPut.setEntity(stringEntity);
                return parseResponse(httpClient.execute(httpPut));
            });
        } catch (Exception ex) {
            logger.error("put error: {}, {}", url, ExceptionUtils.getStackTrace(ex));
            return new HttpResult(503, ex.getMessage());
        }
    }

    public static HttpResult getWithCookie(String uri, CookieStore cookieStore, Header... headers) {
        try {
            return RETRY_TOOL.call(() -> {
                logger.info("http getWithCookie url: {}", uri);
                HttpClient httpClient = HttpClientBuilder.create().setDefaultCookieStore(cookieStore).build();
                HttpGet httpGet = new HttpGet(new URL(uri).toURI());
                if (headers != null && headers.length > 0) {
                    for (Header header : headers) {
                        httpGet.addHeader(header);
                    }
                }

                return parseResponse(httpClient.execute(httpGet));
            });
        } catch (Exception ex) {
            logger.error("http getWithCookie error: {}, {}", uri, ExceptionUtils.getStackTrace(ex));
            return new HttpResult(503, ResultEnum.SYSTEM_EXCEPTION.getValue());
        }
    }

    public static HttpResult fastGet(String uri, Header... headers) throws Exception {
        HttpGet httpGet = null;
        RequestConfig requestConfig
                = RequestConfig
                .custom()
                .setConnectTimeout(5 * 1000)
                .setConnectionRequestTimeout(1000)
                .setSocketTimeout(10 * 1000)
                .build();

        try {
            HttpClient httpClient = HttpClients.createMinimal();
            httpGet = new HttpGet(new URL(uri).toURI());
            httpGet.setConfig(requestConfig);
            if (headers != null && headers.length > 0) {
                for (Header header : headers) {
                    httpGet.addHeader(header);
                }
            }

            return parseResponse(httpClient.execute(httpGet));
        } catch (Exception ex) {
            logger.error("http get error: {}, {}", uri, ExceptionUtils.getStackTrace(ex));
            if (httpGet != null) {
                httpGet.abort();
            }
            throw ex;
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
        }
    }

    public static HttpResult get(String uri, Header... headers) {
        try {
            return RETRY_TOOL.call(() -> {
                HttpGet httpGet = null;
                RequestConfig requestConfig
                        = RequestConfig
                        .custom()
                        .setConnectTimeout(10 * 1000)
                        .setConnectionRequestTimeout(1000)
                        .setSocketTimeout(20 * 1000)
                        .build();

                try {
                    HttpClient httpClient = HttpClients.createMinimal();
                    httpGet = new HttpGet(new URL(uri).toURI());
                    httpGet.setConfig(requestConfig);
                    if (headers != null && headers.length > 0) {
                        for (Header header : headers) {
                            httpGet.addHeader(header);
                        }
                    }

                    return parseResponse(httpClient.execute(httpGet));
                } catch (Exception ex) {
                    logger.error("http get error: {}, {}", uri, ExceptionUtils.getStackTrace(ex));
                    if (httpGet != null) {
                        httpGet.abort();
                    }
                    throw ex;
                } finally {
                    if (httpGet != null) {
                        httpGet.releaseConnection();
                    }
                }
            });
        } catch (Exception ex) {
            logger.error("http get error: {}, {}", uri, ExceptionUtils.getStackTrace(ex));
            return new HttpResult(503, ResultEnum.SYSTEM_EXCEPTION.getValue());
        }
    }

    public static HttpResult get(String uri, Map<String, String> params) {
        try {
            return RETRY_TOOL.call(() -> {
                String urlModel = "{0}?{1}";
                List<NameValuePair> nameValuePairs = Lists.newArrayList();
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    nameValuePairs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
                String paramsStr = EntityUtils.toString(new UrlEncodedFormEntity(nameValuePairs, Consts.UTF_8));
                String requestUrl = MessageFormat.format(urlModel, uri, paramsStr);
                logger.info("http get url: {}", requestUrl);

                HttpClient httpClient = HttpClients.createMinimal();
                HttpGet httpGet = new HttpGet(new URL(requestUrl).toURI());

                return parseResponse(httpClient.execute(httpGet));
            });
        } catch (Exception ex) {
            logger.error("http get error: {}, {}", uri, ExceptionUtils.getStackTrace(ex));
            return new HttpResult(503, ResultEnum.SYSTEM_EXCEPTION.getValue());
        }
    }

    public static HttpResult get(String uri, Map<String, String> params, Header... headers) {
        try {
            return RETRY_TOOL.call(() -> {
                RequestConfig requestConfig
                        = RequestConfig
                        .custom()
                        .setConnectTimeout(5 * 1000)
                        .setConnectionRequestTimeout(1000)
                        .setSocketTimeout(10 * 1000)
                        .build();
                String urlModel = "{0}?{1}";
                List<NameValuePair> nameValuePairs = Lists.newArrayList();
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    nameValuePairs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
                String paramsStr = EntityUtils.toString(new UrlEncodedFormEntity(nameValuePairs, Consts.UTF_8));
                String requestUrl = MessageFormat.format(urlModel, uri, paramsStr);
                logger.info("http get url: {}", requestUrl);

                HttpClient httpClient = HttpClients.createMinimal();
                HttpGet httpGet = new HttpGet(new URL(requestUrl).toURI());

                httpGet.setConfig(requestConfig);
                if (headers != null && headers.length > 0) {
                    for (Header header : headers) {
                        httpGet.addHeader(header);
                    }
                }

                return parseResponse(httpClient.execute(httpGet));
            });
        } catch (Exception ex) {
            logger.error("http get error: {}, {}", uri, ExceptionUtils.getStackTrace(ex));
            return new HttpResult(503, ResultEnum.SYSTEM_EXCEPTION.getValue());
        }
    }


    private static HttpResult parseResponse(HttpResponse httpResponse) throws Exception {
        StatusLine status = httpResponse.getStatusLine();
        HttpEntity httpEntityResponse = httpResponse.getEntity();
        HttpResult httpResult;
        if (status.getStatusCode() != 200) {
            String content = EntityUtils.toString(httpEntityResponse, Charset.forName("UTF-8"));
            logger.error("parseResponse error code={} msg={} content={}", status.getStatusCode(), status.getReasonPhrase(), content);
            httpResult = new HttpResult(status.getStatusCode(), status.getReasonPhrase(), content);
        } else {
            httpResult = new HttpResult(200, "SUCCESS", EntityUtils.toString(httpEntityResponse, Charset.forName("UTF-8")));
        }
        return httpResult;
    }
}
