/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.modules.quartz16.service.impl;

import com.dewu.exception.BadRequestException;
import com.dewu.modules.quartz.domain.QuartzLog;
import com.dewu.modules.quartz.repository.QuartzLogRepository;
import com.dewu.modules.quartz.service.dto.JobQueryCriteria;

import com.dewu.modules.quartz16.domain.QuartzJob16;
import com.dewu.modules.quartz16.repository.QuartzJob16Repository;
import com.dewu.modules.quartz16.service.QuartzJob16Service;
import com.dewu.utils.*;
import lombok.RequiredArgsConstructor;
import org.quartz.CronExpression;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR> Jie
 * @date 2019-01-07
 */
@RequiredArgsConstructor
@Service(value = "quartzJob16Service")
public class QuartzJob16ServiceImpl implements QuartzJob16Service {

    private final QuartzJob16Repository quartzJob16Repository;
    private final QuartzLogRepository quartzLogRepository;
//    private final QuartzManage quartzManage;
    private final RedisUtils redisUtils;

    @Override
    public Object queryAll(JobQueryCriteria criteria, Pageable pageable){
        return PageUtil.toPage(quartzJob16Repository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable));
    }

    @Override
    public Object queryAllLog(JobQueryCriteria criteria, Pageable pageable){
        return PageUtil.toPage(quartzLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable));
    }

    @Override
    public List<QuartzJob16> queryAll(JobQueryCriteria criteria) {
        return quartzJob16Repository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder));
    }

    @Override
    public List<QuartzLog> queryAllLog(JobQueryCriteria criteria) {
        return quartzLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder));
    }

    @Override
    public QuartzJob16 findById(Long id) {
        QuartzJob16 quartzJob = quartzJob16Repository.findById(id).orElseGet(QuartzJob16::new);
        ValidationUtil.isNull(quartzJob.getId(),"QuartzJob","id",id);
        return quartzJob;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(QuartzJob16 resources) {
        if (!CronExpression.isValidExpression(resources.getCronExpression())){
            throw new BadRequestException("cron表达式格式错误");
        }
        resources = quartzJob16Repository.save(resources);
//        quartzManage.addJob(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(QuartzJob16 resources) {
        if (!CronExpression.isValidExpression(resources.getCronExpression())){
            throw new BadRequestException("cron表达式格式错误");
        }
        if(StringUtils.isNotBlank(resources.getSubTask())){
            List<String> tasks = Arrays.asList(resources.getSubTask().split("[,，]"));
            if (tasks.contains(resources.getId().toString())) {
                throw new BadRequestException("子任务中不能添加当前任务ID");
            }
        }
        //手动暂停任务
        resources.setIsPause(true);
        resources = quartzJob16Repository.save(resources);
//        quartzManage.updateJobCron(resources);
    }

    @Override
    public void updateIsPause(QuartzJob16 quartzJob) {
        if (quartzJob.getIsPause()) {
//            quartzManage.resumeJob(quartzJob);
            quartzJob.setIsPause(false);
        } else {
//            quartzManage.pauseJob(quartzJob);
            quartzJob.setIsPause(true);
        }
        quartzJob16Repository.save(quartzJob);
    }


//    @Override
    public void execution(QuartzJob16 quartzJob) {
//        quartzManage.runJobNow(quartzJob);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Set<Long> ids) {
        for (Long id : ids) {
            QuartzJob16 quartzJob = findById(id);
//            quartzManage.deleteJob(quartzJob);
            quartzJob16Repository.delete(quartzJob);
        }
    }

//    @Async
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void executionSubJob(String[] tasks) throws InterruptedException {
//        for (String id : tasks) {
//            if (StrUtil.isBlank(id)) {
//                // 如果是手动清除子任务id，会出现id为空字符串的问题
//                continue;
//            }
////            QuartzJob quartzJob = findById(Long.parseLong(id));
////            // 执行任务
////            String uuid = IdUtil.simpleUUID();
////            quartzJob.setUuid(uuid);
////            // 执行任务
////            execution(quartzJob);
//            // 获取执行状态，如果执行失败则停止后面的子任务执行
////            Boolean result = (Boolean) redisUtils.get(uuid);
////            while (result == null) {
//                // 休眠5秒，再次获取子任务执行情况
//                Thread.sleep(5000);
////                result = (Boolean) redisUtils.get(uuid);
////            }
////            if(!result){
////                redisUtils.del(uuid);
////                break;
////            }
//        }
//    }


}
