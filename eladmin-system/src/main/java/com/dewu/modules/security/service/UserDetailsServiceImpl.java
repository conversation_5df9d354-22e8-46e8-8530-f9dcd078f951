/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.modules.security.service;

import com.dewu.modules.security.service.dto.JwtUserDto;
import com.dewu.modules.system.service.DataService;
import com.dewu.modules.system.service.RoleService;
import com.dewu.modules.system.service.UserService;
import com.dewu.modules.system.service.dto.UserLoginDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.dewu.exception.BadRequestException;
import com.dewu.exception.EntityNotFoundException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Jie
 * @date 2018-11-22
 */
@Slf4j
@RequiredArgsConstructor
@Service("userDetailsService")
public class UserDetailsServiceImpl implements UserDetailsService {
    private final UserService userService;
    private final RoleService roleService;
    private final DataService dataService;
    private final UserCacheManager userCacheManager;

    @Override
    public JwtUserDto loadUserByUsername(String username) {
        JwtUserDto jwtUserDto = userCacheManager.getUserCache(username);
        log.debug("获取用户信息 info接口 从缓存中获取用户信息jwtUserDto:{}",jwtUserDto);
        if(jwtUserDto == null){
            UserLoginDto user;
            try {
                user = userService.getLoginData(username);
                log.debug("获取用户信息 info接口 从缓存中获取用户信息 没有获取到，则从数据库中获取:{}",user);
            } catch (EntityNotFoundException e) {
                log.debug("获取用户信息 info接口 从缓存中获取用户信息 没有获取到，则从数据库中获取异常:{},exception:{}",username,e);
                // SpringSecurity会自动转换UsernameNotFoundException为BadCredentialsException
                throw new UsernameNotFoundException(username, e);
            }
            if (user == null) {
                log.debug("获取用户信息 info接口 从缓存中获取用户信息 没有获取到，则从数据库中获取为空:{}",username);
                throw new UsernameNotFoundException("");
            } else {
                if (!user.getEnabled()) {
                    log.debug("获取用户信息 info接口 账号未激活:{}",username);
                    throw new BadRequestException("账号未激活！");
                }
                log.debug("获取用户信息 info接口 构建JwtUserDto");
                jwtUserDto = new JwtUserDto(
                        user,
                        dataService.getDeptIds(user),
                        roleService.mapToGrantedAuthorities(user)
                );
                log.debug("获取用户信息 info接口 构建JwtUserDto:{}",jwtUserDto);
                // 添加缓存数据
                userCacheManager.addUserCache(username, jwtUserDto);
            }
        }
        return jwtUserDto;
    }
}
