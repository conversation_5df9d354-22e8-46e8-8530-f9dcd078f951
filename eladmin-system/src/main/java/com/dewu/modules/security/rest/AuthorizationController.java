/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.modules.security.rest;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.dewu.annotation.Log;
import com.dewu.annotation.rest.AnonymousDeleteMapping;
import com.dewu.annotation.rest.AnonymousGetMapping;
import com.dewu.annotation.rest.AnonymousPostMapping;
import com.dewu.modules.security.security.TokenProvider;
import com.dewu.modules.security.service.OnlineUserService;
import com.dewu.modules.security.service.dto.AuthUserDto;
import com.dewu.modules.security.service.dto.JwtUserDto;
import com.dewu.modules.system.domain.Dept;
import com.dewu.modules.system.domain.Job;
import com.dewu.modules.system.domain.Role;
import com.dewu.modules.system.domain.User;
import com.dewu.modules.system.service.UserService;
import com.wf.captcha.base.Captcha;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.dewu.config.RsaProperties;
import com.dewu.exception.BadRequestException;
import com.dewu.modules.security.config.bean.LoginCodeEnum;
import com.dewu.modules.security.config.bean.LoginProperties;
import com.dewu.modules.security.config.bean.SecurityProperties;
import com.dewu.utils.RsaUtils;
import com.dewu.utils.RedisUtils;
import com.dewu.utils.SecurityUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Jie
 * @date 2018-11-23
 * 授权、根据token获取用户详细信息
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Api(tags = "系统：系统授权接口")
public class AuthorizationController {
    private final SecurityProperties properties;
    private final RedisUtils redisUtils;
    private final OnlineUserService onlineUserService;
    private final TokenProvider tokenProvider;
    private final AuthenticationManagerBuilder authenticationManagerBuilder;
    @Resource
    private LoginProperties loginProperties;
    @Value("${dw.feishu-bot.env}")
    private String env;
    private final RestTemplate restTemplate;
    private final UserDetailsService userDetailsService;
    private final PasswordEncoder passwordEncoder;
    private final UserService userService;

    @Log("用户登录")
    @ApiOperation("登录授权")
    @AnonymousPostMapping(value = "/login")
    public ResponseEntity<Object> login(@Validated @RequestBody AuthUserDto authUser, HttpServletRequest request) throws Exception {
        log.info("用户登陆:{}",JSONObject.toJSON(authUser));
        //测试环境策略no-sso
        if ( "t1".equals(env)){
           return noSSOLogin(authUser,request);
        }else if ("csprd".equals(env)){
            return ssoLogin(authUser,request);
        }
        return new ResponseEntity<>("active Mismatch ！！active：" +env,HttpStatus.BAD_REQUEST);

    }

    @ApiOperation("获取用户信息")
    @GetMapping(value = "/info")
    public ResponseEntity<Object> getUserInfo() {
        log.debug("获取用户信息 info接口");
        return ResponseEntity.ok(SecurityUtils.getCurrentUser());
    }

    @ApiOperation("获取验证码")
    @AnonymousGetMapping(value = "/code")
    public ResponseEntity<Object> getCode() {
        // 获取运算的结果
        Captcha captcha = loginProperties.getCaptcha();
        String uuid = properties.getCodeKey() + IdUtil.simpleUUID();
        //当验证码类型为 arithmetic时且长度 >= 2 时，captcha.text()的结果有几率为浮点型
        String captchaValue = captcha.text();
        if (captcha.getCharType() - 1 == LoginCodeEnum.ARITHMETIC.ordinal() && captchaValue.contains(".")) {
            captchaValue = captchaValue.split("\\.")[0];
        }
        // 保存
        redisUtils.set(uuid, captchaValue, loginProperties.getLoginCode().getExpiration(), TimeUnit.MINUTES);
        // 验证码信息
        Map<String, Object> imgResult = new HashMap<String, Object>(2) {{
            put("img", captcha.toBase64());
            put("uuid", uuid);
        }};
        return ResponseEntity.ok(imgResult);
    }

    @ApiOperation("退出登录")
    @AnonymousDeleteMapping(value = "/logout")
    public ResponseEntity<Object> logout(HttpServletRequest request) {
        log.info("退出登录 接口");
        onlineUserService.logout(tokenProvider.getToken(request));
        return new ResponseEntity<>(HttpStatus.OK);
    }

    private ResponseEntity<Object> noSSOLogin(AuthUserDto authUser, HttpServletRequest request) throws Exception {
        // 密码解密
        String password = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, authUser.getPassword());
//        // 查询验证码
//        String code = (String) redisUtils.get(authUser.getUuid());
//        // 清除验证码
//        redisUtils.del(authUser.getUuid());
//        if (StringUtils.isBlank(code)) {
//            throw new BadRequestException("验证码不存在或已过期");
//        }
//        if (StringUtils.isBlank(authUser.getCode()) || !authUser.getCode().equalsIgnoreCase(code)) {
//            throw new BadRequestException("验证码错误");
//        }
        UsernamePasswordAuthenticationToken authenticationToken =
                new UsernamePasswordAuthenticationToken(authUser.getUsername(), password);
        Authentication authentication = authenticationManagerBuilder.getObject().authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        // 生成令牌与第三方系统获取令牌方式
        // UserDetails userDetails = userDetailsService.loadUserByUsername(userInfo.getUsername());
        // Authentication authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
        // SecurityContextHolder.getContext().setAuthentication(authentication);
        String token = tokenProvider.createToken(authentication);
        final JwtUserDto jwtUserDto = (JwtUserDto) authentication.getPrincipal();
        // 保存在线信息
        onlineUserService.save(jwtUserDto, token, request);
        // 返回 token 与 用户信息
        Map<String, Object> authInfo = new HashMap<String, Object>(2) {{
            put("token", properties.getTokenStartWith() + token);
            put("user", jwtUserDto);
        }};
        if (loginProperties.isSingleLogin()) {
            //踢掉之前已经登录的token
            onlineUserService.checkLoginOnUser(authUser.getUsername(), token);
        }
        return ResponseEntity.ok(authInfo);
    }

    private ResponseEntity<Object> ssoLogin( AuthUserDto authUser, HttpServletRequest request){
        String accessToken = authUser.getSsoToken();
        JSONObject userInfo = null;
        try {
            log.info("用户:{}权限验证",authUser.getSsoToken());
            userInfo = getAndValidateLoginUsers(accessToken);
            if ("success".equals(userInfo.getString("msg")) && userInfo.getInteger("code").equals(200)) {
                log.info("用户:{}权限验证成功",authUser.getSsoToken());
                JSONObject userData = userInfo.getJSONObject("data");
                //每次登录都更新一次用户信息
                String username = userData.getString("username");
                if (StringUtils.isBlank(username)) {
                    log.debug("用户:{}获取用户信息异常,请尝试重新登录",authUser.getSsoToken());
                    throw new BadRequestException("获取用户信息异常，请尝试重新登录");
                }
                //根据飞书ID，查询用户信息
                // 生成令牌与第三方系统获取令牌方式,根据飞书id查询登录用户
                UserDetails userDetails = null;
                try {
                    if ("liuyang0609".equals(username)||"linzhiping".equals(username)||"luoziyu".equals(username)) {
                        //当本人登录时，获取超级管理员权限
                        username = "admin";
                    }
                    userDetails = userDetailsService.loadUserByUsername(username);
                } catch (UsernameNotFoundException e) {
                    e.printStackTrace();
                }
                log.debug("用户:{}获取用户信息",authUser.getSsoToken());
                if (userDetails == null) {
                    log.debug("用户:{}首次登陆，创建用户信息",authUser.getSsoToken());
                    //模拟新建用户employUserInfo
                    String email = userData.getString("email");
                    String phone = userData.getString("mobile");
//                    String flyBookId = userService.findUserFlyBookId(phone);
//                    JSONObject employUserInfo = getUserInfo(phone);
                    //实则为飞书id
//                    String user_id = employUserInfo.getString("user_id");
//                    if (user_id == null) {
//                        throw new BadRequestException("无法从主数据中获取用的id信息：" + employUserInfo.toJSONString());
//                    }
                    User user = new User();
                    user.setPassword(passwordEncoder.encode("123456"));
                    user.setEmail(email);
                    user.setPhone(phone);
                    user.setUsername(username);
                    user.setEnabled(true);
//                    user.setSecondId(user_id);
                    Dept dept = new Dept();
                    dept.setId(17L);
                    dept.setSubCount(0);
                    user.setGender("男");
                    HashSet<Job> jobs = new HashSet<>();
                    Job job = new Job();
                    job.setId(13L);
                    jobs.add(job);
                    HashSet<Role> roles = new HashSet<>();
                    Role role = new Role();
                    role.setDataScope("本级");
                    role.setId(2L);
                    role.setLevel(3);
                    roles.add(role);
                    user.setRoles(roles);
                    user.setJobs(jobs);
                    user.setDept(dept);
                    user.setNickName(userData.getString("realname"));
                    userService.create(user);
                    userDetails = userDetailsService.loadUserByUsername(username);
                }
                log.debug("用户:{} token信息组装",userDetails.getUsername());
                Authentication authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                SecurityContextHolder.getContext().setAuthentication(authentication);
                String token = tokenProvider.createToken(authentication, accessToken);
                //String token = accessToken;
                final JwtUserDto jwtUserDto = (JwtUserDto) authentication.getPrincipal();
                // 保存在线信息
                log.debug("用户:{} 保存在线信息",userDetails.getUsername());
                onlineUserService.save(jwtUserDto, token, request);
                // 返回 token 与 用户信息
                Map<String, Object> authInfo = new HashMap<String, Object>(2) {{
                    put("token", properties.getTokenStartWith() + token);
                    put("user", jwtUserDto);
                }};
                if (loginProperties.isSingleLogin()) {
                    //踢掉之前已经登录的token
                    log.debug("用户:{} 踢掉之前已经登录的token",userDetails.getUsername());
                    onlineUserService.checkLoginOnUser(username, token);
                }
                log.debug("用户:{} 登陆成功",authUser.getUsername());
                return ResponseEntity.ok(authInfo);
            }
        } catch (Exception e) {
            log.warn("用户:{}权限验证失败,{}",authUser.getSsoToken(),e);
            e.printStackTrace();
        }
        log.debug("用户:{}获取用户信息失败,{}",authUser.getSsoToken());
        throw new BadRequestException(HttpStatus.BAD_REQUEST, "获取用户信息失败：" + userInfo.getString("msg"));
    }
    public JSONObject getAndValidateLoginUsers(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("accessToken", accessToken);
        headers.add("backstageCode", "stream-team-code");
        HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange("https://tianwang.shizhuang-inc.com/api/v1/h5/luna/user/getLoginUser", HttpMethod.GET, requestEntity, JSONObject.class);
        JSONObject body = responseEntity.getBody();
        log.debug("用户登陆调用天网请求返回参数:{}",body.toJSONString());
        int code = body.getIntValue("code");
        if (200 != code) {
            throw new BadRequestException("权限验证失败");
        }
        return body;
    }
}
