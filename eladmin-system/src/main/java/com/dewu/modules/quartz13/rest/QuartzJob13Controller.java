/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.modules.quartz13.rest;

import com.alibaba.fastjson.JSONObject;
import com.dewu.annotation.Log;
import com.dewu.base.BaseEntity;
import com.dewu.exception.BadRequestException;
import com.dewu.flink.template.config.HttpRestTemplate;
import com.dewu.modules.quartz.service.dto.JobQueryCriteria;
import com.dewu.modules.quartz13.domain.QuartzJob13;
import com.dewu.modules.quartz13.service.QuartzJob13Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.runtime.util.JsonUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

/**
 * <AUTHOR> Jie
 * @date 2019-01-07
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/jobs_1_13")
@Api(tags = "系统:1.13定时任务管理")
public class QuartzJob13Controller {

    private static final String ENTITY_NAME = "quartzJob";

    private final QuartzJob13Service quartzJobService;

    private final HttpRestTemplate httpRestTemplate;

    @Value("${request.add13}")
    private String add13;

    @Value("${request.update13}")
    private String update13;

    @Value("${request.updatestate13}")
    private String updatestate13;

    @Value("${request.exec13}")
    private String exec13;

    @Value("${request.delete13}")
    private String delete13;


    @ApiOperation("查询定时任务")
    @GetMapping
    @PreAuthorize("@el.check('timing:list')")
    public ResponseEntity<Object> queryQuartzJob(JobQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(quartzJobService.queryAll(criteria, pageable), HttpStatus.OK);
    }


    @ApiOperation("查询任务执行日志")
    @GetMapping(value = "/logs")
    @PreAuthorize("@el.check('timing:list')")
    public ResponseEntity<Object> queryQuartzJobLog(JobQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(quartzJobService.queryAllLog(criteria, pageable), HttpStatus.OK);
    }

    @Log("新增定时任务")
    @ApiOperation("新增定时任务")
    @PostMapping
    @PreAuthorize("@el.check('timing:add')")
    public ResponseEntity<Object> createQuartzJob(@Validated @RequestBody QuartzJob13 resources) throws Exception {
        quartzJobService.create(resources);
//        if (resources.getId() != null) {
//            throw new BadRequestException("A new " + ENTITY_NAME + " cannot already have an ID");
//        }
//        MultiValueMap<String, String> headMap = getMultiValueMap();
//
//        String response = httpRestTemplate.sendRequestAddHead(add13, JSONObject.toJSONString(resources), headMap);
//        String code = JsonUtils.getInstance().getJsonObject(response, "$.code");
//        if (!code.equals("200")) {
//            throw new Exception("新增定时任务失败,稍后重试:" + response);
//        }
        return new ResponseEntity<>(HttpStatus.CREATED);
    }


    @Log("修改定时任务")
    @ApiOperation("修改定时任务")
    @PutMapping
    @PreAuthorize("@el.check('timing:edit')")
    public ResponseEntity<Object> updateQuartzJob(@Validated(BaseEntity.Update.class) @RequestBody QuartzJob13 resources) throws Exception {
        MultiValueMap<String, String> headMap = getMultiValueMap();

        String response = httpRestTemplate.sendRequestAddHead(update13, JSONObject.toJSONString(resources), headMap);
        String code = JsonUtils.getInstance().getJsonObject(response, "$.code");
        if (!code.equals("200")) {
            throw new Exception("修改定时任务失败,稍后重试:" + response);
        }
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("更改定时任务状态")
    @ApiOperation("更改定时任务状态")
    @PutMapping(value = "/{id}")
    @PreAuthorize("@el.check('timing:edit')")
    public ResponseEntity<Object> updateQuartzJobStatus(@PathVariable Long id) throws Exception {
        //调接口
        MultiValueMap<String, String> headMap = getMultiValueMap();

        String response = httpRestTemplate.sendRequestAddHead(updatestate13+"/"+id, JSONObject.toJSONString(null), headMap);
        String code = JsonUtils.getInstance().getJsonObject(response, "$.code");
        if (!code.equals("200")) {
            throw new Exception("更改定时任务状态失败,稍后重试:" + response);
        }
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }


    @Log("执行定时任务")
    @ApiOperation("执行定时任务")
    @PutMapping(value = "/exec/{id}")
    @PreAuthorize("@el.check('timing:edit')")
    public ResponseEntity<Object> executionQuartzJob(@PathVariable Long id) throws Exception {
        //调接口
        MultiValueMap<String, String> headMap = getMultiValueMap();

        String response = httpRestTemplate.sendRequestAddHead(exec13+"/"+id, JSONObject.toJSONString(null), headMap);
        String code = JsonUtils.getInstance().getJsonObject(response, "$.code");
        if (!code.equals("200")) {
            throw new Exception("执行定时任务失败,稍后重试:" + response);
        }
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除定时任务")
    @ApiOperation("删除定时任务")
    @DeleteMapping
    @PreAuthorize("@el.check('timing:del')")
    public ResponseEntity<Object> deleteQuartzJob(@RequestBody Set<Long> ids) throws Exception {
        for (Long id : ids) {
            //调接口
            MultiValueMap<String, String> headMap = getMultiValueMap();
            String response = httpRestTemplate.sendRequestAddHead(delete13+"/"+id, JSONObject.toJSONString(null), headMap);
            String code = JsonUtils.getInstance().getJsonObject(response, "$.code");
            if (!code.equals("200")) {
                throw new Exception("执行定时任务失败,稍后重试:" + response);
            }
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }


    private static MultiValueMap<String, String> getMultiValueMap() {
        MultiValueMap<String, String> headMap = new LinkedMultiValueMap<>();
        headMap.add("accessId", "libra");
        headMap.add("accessKey", "xKihXzfisBWl+Af5DCUcGZjI3RpBjcc9pVZhCFojHcF7YlYzC7FZ6zuaBDLxdSFa4HNbQJ54tHP0lUaJ2V/Sfw==");
        return headMap;
    }
}
