/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.chaos.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dewu.annotation.AnonymousAccess;
import com.dewu.annotation.Log;
import com.dewu.flink.template.chaos.ChaosTaskManager;
import com.dewu.flink.template.chaos.plan.domain.ChaosPlanCommon;
import com.dewu.flink.template.chaos.plan.domain.ChaosRulesCommon;
import com.dewu.flink.template.chaos.plan.service.ChaosPlanService;
import com.dewu.flink.template.chaos.plan.service.dto.ChaosPlanDto;
import com.dewu.flink.template.chaos.result.service.ChaosPlanTaskResultService;
import com.dewu.flink.template.chaos.result.service.dto.ChaosPlanTaskResultDto;
import com.dewu.flink.template.chaos.task.domain.ChaosPlanTask;
import com.dewu.flink.template.chaos.task.repository.ChaosPlanTaskRepository;
import com.dewu.flink.template.chaos.task.service.ChaosPlanTaskService;
import com.dewu.flink.template.chaos.task.service.dto.ChaosPlanTaskDto;
import com.dewu.flink.template.dag.parser.domain.TeJobInfoDagParser;
import com.dewu.flink.template.dag.parser.service.TeJobInfoDagParserService;
import com.dewu.flink.template.dag.parser.service.dto.TeJobInfoDagParserDto;
import com.dewu.flink.template.dag.parser.service.dto.TeJobInfoDagParserQueryCriteria;
import com.dewu.flink.template.job.domain.TeJobInfo;
import com.dewu.flink.template.job.service.TeJobInfoService;
import com.dewu.flink.template.job.service.dto.TeJobInfoDto;
import com.dewu.flink.template.job.service.dto.TeJobInfoQueryCriteria;
import com.dewu.flink.template.meta.GlobalMetaBaseDomain;
import com.dewu.flink.template.meta.GlobalMetaManager;
import com.dewu.flink.template.meta.datasource.service.ReDataSourceService;
import com.dewu.flink.template.meta.datasource.service.dto.ReDataSourceDto;
import com.dewu.flink.template.meta.datasource.service.dto.ReDataSourceQueryCriteria;
import com.dewu.flink.template.meta.datasource.service.dto.SimpleReDataSourceDto;
import com.dewu.flink.template.meta.tablefield.service.ReDataSourceTableFieldService;
import com.dewu.flink.template.meta.tablefield.service.dto.ReDataSourceTableFieldDto;
import com.dewu.flink.template.meta.tablefield.service.dto.SimpleReDataSourceTableFieldDto;
import com.dewu.flink.template.meta.tableinfo.service.dto.ReDataSourceMetadataDto;
import com.dewu.flink.template.meta.tableinfo.service.dto.ReDataSourceMetadataQueryCriteria;
import com.dewu.flink.template.meta.tableinfo.service.dto.SimpleReDataSourceMetadataDto;
import com.dewu.flink.template.translator.generator.DataSourceType;
import com.dewu.flink.template.utils.JsonUtil;
import com.dewu.property.metadata.service.ReDataSourceMetadataService;
import com.dewu.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.dewu.flink.template.utils.RstBeanUtil.showCommonResult;
import static com.dewu.flink.template.utils.RstBeanUtil.showErrorResult;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2023-10-25
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "演练计划管理")
@RequestMapping("/api/open/v2")
public class ChaosPlanOpenApiController {

	private final ChaosPlanService chaosPlanService;

	private final ChaosPlanTaskService chaosPlanTaskService;

	private final TeJobInfoService templateEngineJobInfoService;

	private final TeJobInfoDagParserService teJobInfoDagParserService;

	private final ReDataSourceMetadataService reDataSourceMetadataService;

	private final ReDataSourceService reDataSourceService;

	private final ChaosTaskManager chaosTaskManager;

	private final ChaosPlanTaskResultService chaosPlanTaskResultService;

	private final ChaosPlanTaskRepository chaosPlanTaskRepository;

	private final GlobalMetaManager globalMetaManager;

	private final ReDataSourceTableFieldService reDataSourceTableFieldService;

	@GetMapping(value = "/asset/query")
	@AnonymousAccess
	public ResponseEntity<Object> query(String topic) {
		ReDataSourceMetadataQueryCriteria queryCriteria = new ReDataSourceMetadataQueryCriteria();
		queryCriteria.setTableName(topic);
		try {
			List<ReDataSourceMetadataDto> list = reDataSourceMetadataService.queryAll(queryCriteria);
			return showCommonResult(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return showErrorResult("查询失败:" + e.getMessage(), 500);
		}
	}


	@GetMapping(value = "/asset/reljob")
	@AnonymousAccess
	public ResponseEntity<Object> reljob(String topicId) {
		TeJobInfoDagParserQueryCriteria queryCriteria = new TeJobInfoDagParserQueryCriteria();
		queryCriteria.setStatus("3");
		queryCriteria.setTableId(topicId);
		try {
			List<TeJobInfoDagParserDto> list = teJobInfoDagParserService.queryAll(queryCriteria).stream().filter(job -> job.getJobName().startsWith("DCHECK_")).collect(Collectors.toList());
			return showCommonResult(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return showErrorResult("关联主题规则查询失败:" + e.getMessage(), 500);
		}
	}


	@GetMapping(value = "/asset/field/list")
	@AnonymousAccess
	public ResponseEntity<Object> assetDetail(long tableId) {
		try {
			List<ReDataSourceTableFieldDto> fields = reDataSourceTableFieldService.queryAll(tableId);
			List<SimpleReDataSourceTableFieldDto> collect = fields.stream().map(SimpleReDataSourceTableFieldDto::of).collect(Collectors.toList());
			return showCommonResult(collect);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return showErrorResult("查询数据资产字段列表:" + e.getMessage(), 500);
		}
	}


	@GetMapping(value = "/asset/table/list")
	@AnonymousAccess
	public ResponseEntity<Object> tableList(long sourceId, String tableName) {
		try {
			ReDataSourceMetadataQueryCriteria queryCriteria = new ReDataSourceMetadataQueryCriteria();
			queryCriteria.setReDataSourceId(sourceId);
			queryCriteria.setIsDelete("0");
			if (StringUtils.isNotEmpty(tableName)) {
				queryCriteria.setTableName(tableName);
			}
			List<ReDataSourceMetadataDto> reDataSourceMetadataDtos = reDataSourceMetadataService.queryAll(queryCriteria);
			List<SimpleReDataSourceMetadataDto> tables = reDataSourceMetadataDtos.stream().map(SimpleReDataSourceMetadataDto::of).collect(Collectors.toList());
			return showCommonResult(tables);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return showErrorResult("表列表查询失败:" + e.getMessage(), 500);
		}
	}


	@GetMapping(value = "/asset/db/list")
	@AnonymousAccess
	public ResponseEntity<Object> dbList(String sourceType, @Nullable String dbName) {
		try {
			ReDataSourceQueryCriteria queryCriteria = new ReDataSourceQueryCriteria();
			queryCriteria.setDataSourceType((long) DataSourceType.valueOf(sourceType).getValue());
			if (StringUtils.isNotEmpty(dbName)) {
				queryCriteria.setDbName(dbName);
			}
			List<ReDataSourceDto> reDataSourceDtos = reDataSourceService.queryAll(queryCriteria);
			List<SimpleReDataSourceDto> simpleReDataSourceDtos = reDataSourceDtos.stream().map(SimpleReDataSourceDto::of).collect(Collectors.toList());
			return showCommonResult(simpleReDataSourceDtos);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return showErrorResult("数据源列表查询失败:" + e.getMessage(), 500);
		}
	}

	@GetMapping(value = "/asset/source/list")
	@AnonymousAccess
	public ResponseEntity<Object> sourceList(String sourceType, @Nullable String sourceName) {
		try {
			ReDataSourceQueryCriteria queryCriteria = new ReDataSourceQueryCriteria();
			queryCriteria.setDataSourceType((long) DataSourceType.valueOf(sourceType).getValue());
			if (StringUtils.isNotEmpty(sourceName)) {
				queryCriteria.setDbName(sourceName);
			}
			List<ReDataSourceDto> reDataSourceDtos = reDataSourceService.queryAll(queryCriteria);
			List<SimpleReDataSourceDto> simpleReDataSourceDtos = reDataSourceDtos.stream().map(SimpleReDataSourceDto::of).collect(Collectors.toList());
			return showCommonResult(simpleReDataSourceDtos);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return showErrorResult("数据源列表查询失败:" + e.getMessage(), 500);
		}
	}


	@GetMapping(value = "/chaosplan/collect/{tableid}")
	@AnonymousAccess
	public ResponseEntity<Object> collectTableSampleRecord(@PathVariable String tableid) {
		TeJobInfoDagParserQueryCriteria queryCriteria = new TeJobInfoDagParserQueryCriteria();
		queryCriteria.setStatus("3");
		queryCriteria.setTableId(tableid);
		try {
			List<Map<String, String>> list = reDataSourceMetadataService.consumerTemplateJobOutputMess(new Long[]{Long.valueOf(tableid)});
			Map<String, String> recordInfo = list.get(0);
			return showCommonResult(recordInfo);
		} catch (Exception e) {
			log.error("", e);
			return showErrorResult("查询失败:" + e.getMessage(), 500);
		}
	}


	@Log("创建chaos作业")
	@PostMapping(value = "/chaosplan/create")
	@ApiOperation("创建chaos作业")
	@AnonymousAccess
	public ResponseEntity<Object> create(@Validated @RequestBody ChaosPlanCommon resources) throws InvocationTargetException, IllegalAccessException {
		try {
			ChaosPlanDto common = chaosPlanService.createCommon(resources);
			return showCommonResult(common.getId());
		} catch (Exception e) {
			log.error("", e);
			return showErrorResult("创建演练计划失败:" + e.getMessage(), 500);
		}
	}


	@Log("更新chaos作业")
	@PostMapping(value = "/chaosplan/update")
	@ApiOperation("更新chaos作业")
	@AnonymousAccess
	public ResponseEntity<Object> update(@Validated @RequestBody ChaosPlanCommon resources) throws InvocationTargetException, IllegalAccessException {
		try {
			chaosPlanService.update(resources);
			return showCommonResult(resources.getId());
		} catch (Exception e) {
			log.error("", e);
			return showErrorResult("创建演练计划失败:" + e.getMessage(), 500);
		}
	}


	@Log("校验chaos作业")
	@PostMapping(value = "/chaosplan/validate")
	@ApiOperation("校验chaos作业")
	@AnonymousAccess
	public ResponseEntity<Object> validate(@Validated @RequestBody ChaosPlanCommon resources) {
		for (ChaosRulesCommon rule : resources.getRules()) {
			if (rule.getDatagenRuleType().equals("2")) {
				try {
					String data = rule.getDatagenRuleDetail();
					if (StringUtils.isEmpty(data)) {
						log.error("datagen rule detail 为空 :" + resources);
						return showErrorResult("datagen rule detail 为空", 500);
					}
					String[] rawDatas = data.split("\n");
					for (String str : rawDatas) {
						if (StringUtils.isNotEmpty(str)) {
							JsonUtil.parseObject(str, Map.class);
						}
					}
				} catch (Exception e) {
					log.error("rule detail 格式错误，请用换行符分割多条json数据 :" + resources, e);
					return showErrorResult("rule detail 格式错误，请用换行符分割多条json数据。" + e.getMessage(), 500);
				}
			}
		}
		return showCommonResult("OK");
	}

	@Log("启动chaos作业")
	@PutMapping(value = "/chaosplan/start/{planId}")
	@ApiOperation("启动chaos作业")
	@AnonymousAccess
	public ResponseEntity<Object> start(@PathVariable String planId) {
		try {

			ChaosPlanTaskDto chaosPlanTask = startChaosPlan(planId);
			return showCommonResult(chaosPlanTask.getId());
		} catch (Exception e) {
			log.error("", e);
			return showErrorResult("发起演练失败:" + e.getMessage(), 500);
		}
	}


	@GetMapping(value = "/chaosplan/rst/{taskId}")
	@AnonymousAccess
	public ResponseEntity<Object> collectResultForChaosTask(@PathVariable Long taskId) {
		try {
			List<ChaosPlanTaskResultDto> rsts = chaosPlanTaskResultService.findAllByTaskId(taskId);
			return showCommonResult(buildRst(rsts));
		} catch (Exception e) {
			log.error("查询演练结果失败", e);
			return showErrorResult("查询演练结果失败:" + e.getMessage(), 500);
		}
	}

	private String buildRst(List<ChaosPlanTaskResultDto> rsts) {
		StringBuilder stringBuilder = new StringBuilder();
		if (rsts.size() > 50) {
			rsts = rsts.subList(0, 49);
		}
		rsts.forEach(rst -> stringBuilder.append(rst.getSubjectCode()).append(", ").append(rst.getSubjectOwner()).append(", ").append(rst.getAlarmInfo()).append("\n"));
		return stringBuilder.toString();
	}


	private ChaosPlanTaskDto startChaosPlan(String planId) {
		List<ChaosPlanTask> tasks = chaosPlanTaskRepository.findChaosPlanTasksBycAndChaosPlanIdAndTaskStatus(Long.parseLong(planId), 0);
		if (tasks.size() > 0) {
			throw new RuntimeException("计划正在执行中，请勿重复提交");
		}
		// create task.
		ChaosPlanDto chaosPlanDto = chaosPlanService.findById(Long.valueOf(planId));
		ChaosPlanTask chaosPlanTask = new ChaosPlanTask();
		chaosPlanTask.setChaosPlanId(chaosPlanDto.getId());
		chaosPlanTask.setOperator(chaosPlanDto.getOwner());
		Timestamp curr = new Timestamp(System.currentTimeMillis());
		chaosPlanTask.setStartTime(curr);
		chaosPlanTask.setCreateTime(curr);
		chaosPlanTask.setModifyTime(curr);
		chaosPlanTask.setTaskStatus(0);
		chaosPlanTask.setEndTime(new Timestamp(System.currentTimeMillis() + chaosPlanDto.getDuration() * 60 * 1000));
		ChaosPlanTaskDto chaosPlanTaskDto = chaosPlanTaskService.create(chaosPlanTask);

		chaosTaskManager.asyncGenerateNewTask(chaosPlanDto, chaosPlanTaskDto);

		return chaosPlanTaskDto;
	}


	@Log("停止chaos作业")
	@PutMapping(value = "/chaosplan/stop/{taskId}")
	@ApiOperation("停止chaos作业")
	@AnonymousAccess
	public ResponseEntity<Object> stop(@PathVariable String taskId) {
		try {
//			List<ChaosPlanTask> tasks = chaosPlanTaskRepository.findChaosPlanTasksBycAndChaosPlanIdAndTaskStatus(Long.parseLong(planId), 0);
//			for (ChaosPlanTask task : tasks) {
			chaosTaskManager.stopTask(Long.parseLong(taskId));
//			}
			return showCommonResult("OK");
		} catch (Exception e) {
			log.error("", e);
			return showErrorResult("停止演练失败:" + e.getMessage(), 500);
		}
	}

	@Log("导出chaos数据")
	@AnonymousAccess
	@GetMapping(value = "/test")
	@ApiOperation("导出chaos数据")
	public void exportChaosPlan() throws IOException {
		TeJobInfoQueryCriteria criteria = new TeJobInfoQueryCriteria();
		criteria.setLikeJobName("dcheck");
//        List<Integer> list = new ArrayList<>();
//        list.add(3);
//        criteria.setStatus(list);
		List<TeJobInfoDto> teJobInfoDtos = templateEngineJobInfoService.queryAll(criteria);
		for (int i = 0; i < teJobInfoDtos.size(); i++) {
			TeJobInfoDto teJobInfoDto = teJobInfoDtos.get(i);
			int status = teJobInfoDto.getStatus();
			//运行中是3
//            if (status == 3) {
			String dagJson = teJobInfoDto.getDagJson();
			JSONObject jsonObject = JSON.parseObject(dagJson);
			JSONArray o = (JSONArray) jsonObject.get("sourceTableIds");
			TeJobInfoDagParser teJobInfoDagParser = new TeJobInfoDagParser();
			Long id = teJobInfoDto.getId();
			String jobName = teJobInfoDto.getJobName();
			teJobInfoDagParserService.deleteByJobId(id);
			teJobInfoDagParser.setJobName(jobName);
			teJobInfoDagParser.setJobId(id);
			teJobInfoDagParser.setStatus(status);
			teJobInfoDagParser.setOwnerName(teJobInfoDto.getOwnerName());
			teJobInfoDagParser.setCreateTime(new Timestamp(System.currentTimeMillis()));
			teJobInfoDagParser.setModifyTime(new Timestamp(System.currentTimeMillis()));
			teJobInfoDagParser.setIsDelete(0);
			for (int j = 0; j < o.size(); j++) {
				String s = String.valueOf(o.get(j));
				System.out.println(s);
				teJobInfoDagParser.setTableId(s);
				teJobInfoDagParser.setTableFrom("source");
				teJobInfoDagParserService.create(teJobInfoDagParser);
			}
		}
	}

}
