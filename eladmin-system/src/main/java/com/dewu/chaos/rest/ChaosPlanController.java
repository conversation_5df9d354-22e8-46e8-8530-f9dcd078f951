/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.chaos.rest;

import com.dewu.annotation.Log;
import com.dewu.flink.template.chaos.plan.domain.ChaosPlan;
import com.dewu.flink.template.chaos.plan.domain.ChaosPlanCommon;
import com.dewu.flink.template.chaos.plan.service.ChaosPlanService;
import com.dewu.flink.template.chaos.plan.service.dto.ChaosPlanQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2023-10-25
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "演练计划管理")
@RequestMapping("/api/chaosPlan")
public class ChaosPlanController {

	private final ChaosPlanService chaosPlanService;

	@Log("导出数据")
	@ApiOperation("导出数据")
	@GetMapping(value = "/download")
	@PreAuthorize("@el.check('chaosPlan:list')")
	public void exportChaosPlan(HttpServletResponse response, ChaosPlanQueryCriteria criteria) throws IOException {
		chaosPlanService.download(chaosPlanService.queryAll(criteria), response);
	}

	@GetMapping
	@ApiOperation("查询演练计划")
	@PreAuthorize("@el.check('chaosPlan:list')")
	public ResponseEntity<Object> queryChaosPlan(ChaosPlanQueryCriteria criteria, Pageable pageable) {
		return new ResponseEntity<>(chaosPlanService.queryAll(criteria, pageable), HttpStatus.OK);
	}

	@PostMapping
	@Log("新增演练计划")
	@ApiOperation("新增演练计划")
	@PreAuthorize("@el.check('chaosPlan:add')")
	public ResponseEntity<Object> createChaosPlan(@Validated @RequestBody ChaosPlan resources) {
		return new ResponseEntity<>(chaosPlanService.create(resources), HttpStatus.CREATED);
	}

	@PutMapping
	@Log("修改演练计划")
	@ApiOperation("修改演练计划")
	@PreAuthorize("@el.check('chaosPlan:edit')")
	public ResponseEntity<Object> updateChaosPlan(@Validated @RequestBody ChaosPlanCommon resources) {
		try {
			chaosPlanService.update(resources);
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return new ResponseEntity<>(HttpStatus.NO_CONTENT);
	}

	@DeleteMapping
	@Log("删除演练计划")
	@ApiOperation("删除演练计划")
	@PreAuthorize("@el.check('chaosPlan:del')")
	public ResponseEntity<Object> deleteChaosPlan(@RequestBody Long[] ids) {
		chaosPlanService.deleteAll(ids);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
