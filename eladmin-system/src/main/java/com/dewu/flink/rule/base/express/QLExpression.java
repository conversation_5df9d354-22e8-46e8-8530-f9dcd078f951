package com.dewu.flink.rule.base.express;

import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;

import java.util.Map;

public class QLExpression implements UDExpression {
	ExpressRunner runner;
	String expr;

	public QLExpression(ExpressRunner runner, String expr) {
		this.runner = runner;
		this.expr = expr;
	}

	@Override
	public Object execute(Map map) {
		DefaultContext<String, Object> context = new DefaultContext<String, Object>();
		context.putAll(map);
		try {
			return runner.execute(expr, context, null, true, false);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public Object execute(Map map, boolean ignoreError) {
		return null;
	}

	@Override
	public Object execute() {
		return null;
	}

	@Override
	public String getCalculatoExpressionr() {
		return null;
	}

	@Override
	public String getTargetColumn() {
		return null;
	}
}
