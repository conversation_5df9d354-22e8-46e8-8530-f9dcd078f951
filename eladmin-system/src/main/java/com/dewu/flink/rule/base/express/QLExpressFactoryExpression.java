package com.dewu.flink.rule.base.express;

import com.ql.util.express.ExpressRunner;

public class QLExpressFactoryExpression implements FactoryExpression {
	@Override
	public QLExpression compile(final String expression) {
		ExpressRunner runner = new ExpressRunner();
		try {
			runner.getOutFunctionNames(expression);
		} catch (Exception e) {
			throw new RuntimeException("表达式校验异常", e);
		}
		return new QLExpression(runner, expression);
	}


}
