///*
// * Licensed to the Apache Software Foundation (ASF) under one
// * or more contributor license agreements.  See the NOTICE file
// * distributed with this work for additional information
// * regarding copyright ownership.  The ASF licenses this file
// * to you under the Apache License, Version 2.0 (the
// * "License"); you may not use this file except in compliance
// * with the License.  You may obtain a copy of the License at
// *
// *     http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
// */
//
//package org.apache.flink.streaming.api.environment;
//
//
//import com.dewu.exception.ParserException;
//import com.dewu.flink.template.utils.*;
//import com.dewu.modules.quartz.config.ARKRunner;
//import com.dewu.property.sql.job.service.FlinkSqlJobLibraService;
//import com.dewu.property.sql.job.service.dto.FlinkSqlJobLibraDto;
//import com.dewu.property.sql.job.service.dto.FlinkSqlJobLibraQueryCriteria;
//import com.dewu.property.sql.lineage.domain.FlinkSqlLineage;
//import com.dewu.property.sql.lineage.domain.LineageResult;
//import com.dewu.property.sql.lineage.service.FlinkSqlLineageService;
//import com.dewu.property.sql.lineage.service.SqlInstanceService;
//import com.dewu.property.sql.lineage.service.dto.SqlInstanceMapDto;
//import com.dewu.property.sql.lineage.service.dto.SqlInstanceMapQueryCriteria;
//import com.dewu.property.sql.utils.OssUtils;
//import com.dewu.property.sql.utils.UdfJarsBean;
//import com.google.gson.Gson;
//import com.google.gson.reflect.TypeToken;
//import org.apache.calcite.plan.RelOptTable;
//import org.apache.calcite.rel.RelNode;
//import org.apache.calcite.rel.metadata.RelColumnOrigin;
//import org.apache.calcite.rel.metadata.RelMetadataQuery;
//import org.apache.calcite.sql.SqlNode;
//import org.apache.calcite.sql.parser.SqlParser;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.flink.api.java.tuple.Tuple2;
//import org.apache.flink.runtime.execution.librarycache.FlinkUserCodeClassLoaders;
//import org.apache.flink.sql.parser.ddl.SqlCreateTable;
//import org.apache.flink.sql.parser.ddl.SqlTableOption;
//import org.apache.flink.table.api.EnvironmentSettings;
//import org.apache.flink.table.api.SqlDialect;
//import org.apache.flink.table.api.TableException;
//import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
//import org.apache.flink.table.api.internal.TableEnvironmentImpl;
//import org.apache.flink.table.operations.CatalogSinkModifyOperation;
//import org.apache.flink.table.operations.Operation;
//import org.apache.flink.table.operations.ddl.CreateTableOperation;
//import org.apache.flink.table.planner.operations.PlannerQueryOperation;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.io.File;
//import java.io.IOException;
//import java.lang.reflect.Type;
//import java.net.MalformedURLException;
//import java.net.URL;
//import java.util.*;
//
//import static com.dewu.flink.template.utils.SqlParserUtils.getCurrentSqlParserConfig;
//import static com.dewu.property.sql.utils.OssUtils.PRE_DIR;
//
//@Component
//public class TableDataLineageSQL {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(TableDataLineageSQL.class);
//
//    public static final String  LINEAGE_PARSE_0_TABLE= "0";// 血缘推断表数量0
//
//    public static final String LINEAGE_PARSE_SUCCESS = "1"; //血缘推断成功
//
//    public static final String LINEAGE_PARSE_LOAD_JAR_SUCCESS = "2"; // 血缘推断失败后，加载jar后推断成功
//
//    public static final String LINEAGE_PARSE_LOAD_JAR_0_TABLE = "3"; // 血缘推断失败后，加载jar后推断表数量0
//
//    public static final String LINEAGE_PARSE_ERROR = "4";// 血缘推断异常
//
//    public static final String LINEAGE_PARSE_DOUDI_SUCCESS = "5";// 兜底解析成功
//
//    public static final String LINEAGE_PARSE_DOUDI_O_TABLE = "7";// 兜底解析0表
//
//    public static final String LINEAGE_PARSE_DOUDI_ERROR = "8";// 兜底解析异常
//
//    public static final String NO_LINEAGE_PARSE = "6";// 作业下线,不需要解析
//
////
////    @Autowired
////    ARKRunner arkRunner;
//
//    @Autowired
//    SqlInstanceService sqlInstanceService;
//
//    @Autowired
//    FlinkSqlJobLibraService flinkSqlJobLibraService;
//
//    @Autowired
//    FlinkSqlLineageService flinkSqlLineageService;
//
//
//    public void sqlLineageParseDeal(FlinkSqlJobLibraQueryCriteria queryCriteria,String dealModel) throws Exception {
//        String taskName = "";
//        try {
//            List<SqlInstanceMapDto> sqlInstanceMapList = sqlInstanceService.queryAll(new SqlInstanceMapQueryCriteria());
//            //查询在运行的flink作业
//            List<FlinkSqlJobLibraDto> runFlinkSqlJobLibraList = flinkSqlJobLibraService.queryAll(queryCriteria);
//            for (int i = 0; i < runFlinkSqlJobLibraList.size(); i++) {
//                FlinkSqlJobLibraDto runFlinkSqlJobLibra = runFlinkSqlJobLibraList.get(i);
//                taskName = runFlinkSqlJobLibra.getTaskName();
//                LOGGER.info("****血缘解析****************** 解析作业名:{}", taskName);
//                Set<TableMetaExt> sourceTableMetaSet = new HashSet<>();
//                Set<TableMetaExt> insertTableMetaSet = new HashSet<>();
//                Set<FlinkSqlLineage> tableLineageResults;
//                try {
//                    // bug,如果用户只是建了sink表，但是没有inset into语句，那么会认为这个表是source表
//                    getScriptSourceSink(runFlinkSqlJobLibra.getSqlScript().toLowerCase(), sourceTableMetaSet, insertTableMetaSet);
//                    //记录每个任务解析出几个source,sink表
//                    runFlinkSqlJobLibra.setSourceTableNum(sourceTableMetaSet.size());
//                    runFlinkSqlJobLibra.setTargetTableNum(insertTableMetaSet.size());
//                    runFlinkSqlJobLibra.setSqlContent("");
//                    if("doudi".equals(dealModel)) {
//                        tableLineageResults = supplementSqlLineagePlanB(sourceTableMetaSet, insertTableMetaSet);
//                        updateSqlLineResult(sqlInstanceMapList, runFlinkSqlJobLibra, tableLineageResults, LINEAGE_PARSE_DOUDI_SUCCESS);
//                    }else {
//                        //优先不加载udx
//                        tableLineageResults = sqlLineage(runFlinkSqlJobLibra, sourceTableMetaSet, insertTableMetaSet, false);
//                        //更新血缘数据
//                        updateSqlLineResult(sqlInstanceMapList, runFlinkSqlJobLibra, tableLineageResults, LINEAGE_PARSE_SUCCESS);
//                    }
//                } catch (Error error) {
//
//                    try {
//                        logError(error, runFlinkSqlJobLibra, taskName);
//
//                        tableLineageResults = sqlLineage(runFlinkSqlJobLibra, sourceTableMetaSet, insertTableMetaSet, true);
//                        updateSqlLineResult(sqlInstanceMapList, runFlinkSqlJobLibra, tableLineageResults, LINEAGE_PARSE_LOAD_JAR_SUCCESS);
//
//                    } catch (Error error1) {
//                        try {
//                            logError(error1, runFlinkSqlJobLibra, taskName);
//
//                            tableLineageResults = supplementSqlLineagePlanB(sourceTableMetaSet, insertTableMetaSet);
//                            updateSqlLineResult(sqlInstanceMapList, runFlinkSqlJobLibra, tableLineageResults, LINEAGE_PARSE_DOUDI_SUCCESS);
//                        } catch (ParserException e) {
//                            updateParserStatus(runFlinkSqlJobLibra, LINEAGE_PARSE_0_TABLE);
//                        } catch (Exception e) {
//                            updateParserStatus(runFlinkSqlJobLibra, LINEAGE_PARSE_DOUDI_ERROR);
//                        }
//
//                    } catch (Exception exception) {
//                        try {
//                            logException(exception, runFlinkSqlJobLibra, taskName);
//
//                            tableLineageResults = supplementSqlLineagePlanB(sourceTableMetaSet, insertTableMetaSet);
//                            updateSqlLineResult(sqlInstanceMapList, runFlinkSqlJobLibra, tableLineageResults, LINEAGE_PARSE_DOUDI_SUCCESS);
//                        } catch (ParserException e) {
//                            updateParserStatus(runFlinkSqlJobLibra, LINEAGE_PARSE_0_TABLE);
//                        } catch (Exception e) {
//                            updateParserStatus(runFlinkSqlJobLibra, LINEAGE_PARSE_DOUDI_ERROR);
//                        }
//                    }
//
//
//                } catch (Exception exception) {
//                    try {
//                        logException(exception, runFlinkSqlJobLibra, taskName);
//
//                        tableLineageResults = sqlLineage(runFlinkSqlJobLibra, sourceTableMetaSet, insertTableMetaSet, true);
//                        updateSqlLineResult(sqlInstanceMapList, runFlinkSqlJobLibra, tableLineageResults, LINEAGE_PARSE_LOAD_JAR_SUCCESS);
//
//                    } catch (Error error1) {
//                        try {
//                            logError(error1, runFlinkSqlJobLibra, taskName);
//
//                            tableLineageResults = supplementSqlLineagePlanB(sourceTableMetaSet, insertTableMetaSet);
//                            updateSqlLineResult(sqlInstanceMapList, runFlinkSqlJobLibra, tableLineageResults, LINEAGE_PARSE_DOUDI_SUCCESS);
//                        } catch (ParserException e) {
//                            updateParserStatus(runFlinkSqlJobLibra, LINEAGE_PARSE_0_TABLE);
//                        } catch (Exception e) {
//                            updateParserStatus(runFlinkSqlJobLibra, LINEAGE_PARSE_DOUDI_ERROR);
//                        }
//
//                    } catch (Exception exception1) {
//                        try {
//                            logException(exception1, runFlinkSqlJobLibra, taskName);
//
//                            tableLineageResults = supplementSqlLineagePlanB(sourceTableMetaSet, insertTableMetaSet);
//                            updateSqlLineResult(sqlInstanceMapList, runFlinkSqlJobLibra, tableLineageResults, LINEAGE_PARSE_DOUDI_SUCCESS);
//                        } catch (ParserException e) {
//                            updateParserStatus(runFlinkSqlJobLibra, LINEAGE_PARSE_0_TABLE);
//                        } catch (Exception e) {
//                            updateParserStatus(runFlinkSqlJobLibra, LINEAGE_PARSE_DOUDI_ERROR);
//                        }
//                    }
//
//                }
//            }
//        } catch (Exception exception) {
//            LOGGER.error("血缘解析exception异常日志解析作业名:{}", taskName, exception);
//            throw new Exception("血缘解析异常日志");
//        } catch (Error error) {
//            LOGGER.error("血缘解析error异常日志解析作业名:{}", taskName, error);
//            throw new Exception("血缘解析异常日志");
//        }
//    }
//
//    private static void logException(Exception e1, FlinkSqlJobLibraDto runFlinkSqlJobLibra, String taskName) {
//        runFlinkSqlJobLibra.setSqlContent(e1.getMessage());
//        LOGGER.error("解析作业名:{} 血缘解析异常:{}", taskName, runFlinkSqlJobLibra, e1);
//    }
//
//    private static void logError(Error error, FlinkSqlJobLibraDto runFlinkSqlJobLibra, String taskName) {
//        runFlinkSqlJobLibra.setSqlContent(error.getMessage());
//        LOGGER.error("解析作业名:{} 血缘解析异常:{}", taskName, runFlinkSqlJobLibra, error);
//    }
//
//    private void updateParserStatus(FlinkSqlJobLibraDto runFlinkSqlJobLibra, String parserStatus) {
//        runFlinkSqlJobLibra.setParserStatus(parserStatus);
//        flinkSqlJobLibraService.updateParserStatus(runFlinkSqlJobLibra);
//    }
//
//    private void updateSqlLineResult(List<SqlInstanceMapDto> sqlInstanceMapList, FlinkSqlJobLibraDto runFlinkSqlJobLibra, Set<FlinkSqlLineage> tableLineageResults, String parserStatus) {
//        updateSqlLineStrategy(sqlInstanceMapList, runFlinkSqlJobLibra, tableLineageResults, parserStatus);
//        flinkSqlJobLibraService.updateParserStatus(runFlinkSqlJobLibra);
//    }
//
//
//    public Set<FlinkSqlLineage> sqlLineage(FlinkSqlJobLibraDto flinkSqlJobLibraDto, Set<TableMetaExt> sourceTableMetaSet, Set<TableMetaExt> sinkTableMetaSet, boolean isLoadUdx) throws IOException {
//        Set<String> sourceTableSet = tableMetaExtToStringExtractedViewTable(sourceTableMetaSet);//提取viewtable,并转小写
//        Set<String> sinkTableSet = tableMetaExtToStringExtractedViewTable(sinkTableMetaSet);
//
//        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
//        TableEnvironmentImpl tEnv = (TableEnvironmentImpl) StreamTableEnvironment.create(env, EnvironmentSettings.newInstance().inStreamingMode().build());
//
//        loadUdxJars(flinkSqlJobLibraDto, env, isLoadUdx);
//
//
//        List<StringBuilder> sqlScriptStatementList = ScriptUtils.splitSqlScriptRegisterFunction(tEnv, flinkSqlJobLibraDto.getSqlScript());
//
//
//        SqlParser.Config parserConfig = getCurrentSqlParserConfig(SqlDialect.DEFAULT);
//        SqlParser parser = null;
//        SqlNode sqlNode = null;
//
//        Set<FlinkSqlLineage> sourceSinkTableRelationSet = new HashSet<>();
//
//        Set<TableMetaExt> tableMetaExtHashSet = new HashSet<>();
//
//        for (int i = 0; i < sqlScriptStatementList.size(); i++) {
//            StringBuilder statement = sqlScriptStatementList.get(i);
//            String sql = statement.toString()
//                    .replaceAll("(?i)json_value", "json_value_1")
//                    .replaceAll("(?i)/\\*\\+\\s*`?PARTITIONED_JOIN`?\\s*\\*/", "")//移除字符串中的/*+ PARTITIONED_JOIN */
//                    .replace(";", "");//语句结尾的;要去掉
//            LOGGER.info("要解析的-----" + sql);
//            List<Operation> operations = tEnv.getParser().parse(sql);
//            if (operations.size() != 1) {
//                throw new TableException(
//                        "Unsupported SQL query! executeSql() only accepts a single SQL statement of type "
//                                + "CREATE TABLE, DROP TABLE, ALTER TABLE, CREATE DATABASE, DROP DATABASE, ALTER DATABASE, "
//                                + "CREATE FUNCTION, DROP FUNCTION, ALTER FUNCTION, CREATE CATALOG, DROP CATALOG, "
//                                + "USE CATALOG, USE [CATALOG.]DATABASE, SHOW CATALOGS, SHOW DATABASES, SHOW TABLES, SHOW [USER] FUNCTIONS, SHOW PARTITIONS"
//                                + "CREATE VIEW, DROP VIEW, SHOW VIEWS, INSERT, DESCRIBE, LOAD MODULE, UNLOAD "
//                                + "MODULE, USE MODULES, SHOW [FULL] MODULES.");
//            }
//            Operation operation = operations.get(0);
//            if (org.apache.flink.util.StringUtils.isNullOrWhitespaceOnly(sql)) {
//                continue;
//            }
//            parser = SqlParser.create(sql, parserConfig);
//            try {
//                sqlNode = parser.parseStmt();
//            } catch (Exception e) {
//                throw new RuntimeException("meet unresolved sql : [" + sql + "]. 请联系模板平台管理员处理 ", e);
//            }
//
//
//            if (operation instanceof CatalogSinkModifyOperation) {
////                tEnv.executeSql(sql);
//                sourceSinkTableRelationSet.addAll(identifySourceSinkTableRelation(tEnv, (CatalogSinkModifyOperation) operation));
//            } else if (operation instanceof CreateTableOperation) {
//                CreateTableOperation createViewOperation = (CreateTableOperation) operation;
//                String viewTableName = createViewOperation.getTableIdentifier().getObjectName();
//                Map<String, String> options = createViewOperation.getCatalogTable().getOptions();
//
//                String connector = options.get("connector");
//                Map<String, String> ddlMapParam = mapToLowerCase(options);
//
//                TableMetaExt tableMetaExt = SqlScriptsRender.buildTableMetaV1(ddlMapParam, connector);
//                tableMetaExt.setViewTable(viewTableName);
//                tableMetaExt.setConnectorUnify(connector);
//
//                tableMetaExtHashSet.add(tableMetaExt);
//
//                // 待修改的SQL语句
//                if (connector.indexOf("odps") > -1 || connector.indexOf("jdbc") > -1 || connector.indexOf("hologres") > -1
//                        || connector.indexOf("influxdb") > -1
//                        || connector.indexOf("elasticsearch") > -1 //overlap-elasticsearch7
//                        || connector.indexOf("victoria-metric") > -1
//                        || connector.indexOf("sls") > -1
//                        || connector.indexOf("redis") > -1
//                        || connector.indexOf("kafka") > -1 // 过滤kafka，du_trade_inventory_db2odps 任务就会失败，只能说kafka根据format格式来决定用啥
//                    // "  `event_time` TIMESTAMP(3) METADATA FROM 'timestamp',\n" + 这个 还有一个from关键词要去掉
//                ) {
//                    if (connector.indexOf("kafka") > -1) {
//                        String format = ddlMapParam.get("format");
//                        String keyFormat = ddlMapParam.get("key.format");
//
//                        if ("msgpack".equals(format) || "csv".equals(format)
////                        || connector.equals("odps-kafka") //datagen 不支持BYTES类型
//                        ) {
//                            sql = replaceConnector(sourceTableSet, sinkTableSet, sql, viewTableName.toLowerCase());
//                        }
//
//                        if ("json".equalsIgnoreCase(keyFormat)) {
//                            // raw类型如果移除key.format会报错，两种方案，要么移除key.format，要么修改key.fields的参数值
//                            SqlCreateTable sqlCreateTable = (SqlCreateTable) sqlNode;
//                            Iterator<SqlNode> iterator = sqlCreateTable.getPropertyList().getList().iterator();
//                            while (iterator.hasNext()) {
//                                SqlTableOption next = (SqlTableOption) iterator.next();
//                                SqlNode key = next.getKey();
////                                if (key.toString().equals("'key.fields'")) {
////                                    SqlCharStringLiteral value = (SqlCharStringLiteral) next.getValue();
////                                    NlsString value1 = (NlsString) value.getValue();
////                                    value1.copy(value.toString().split(";")[0]);
////                                }
//                                if (key.toString().equals("'key.format'")
//                                        || key.toString().equals("'key.fields'")
//                                        || key.toString().equals("'key.fields-prefix'")
//                                        || key.toString().equals("'key.json.ignore-parse-errors'")) {
//                                    iterator.remove();
//                                }
//                            }
//
//                            sql = sqlNode.toString();
//////                            //进来的时候已经没有逗号了
//////                            options.put("key.fields",fields.split(",")[0]);
////                        }
//                        }
//                    } else {
//                        sql = replaceConnector(sourceTableSet, sinkTableSet, sql, viewTableName.toLowerCase());
//                    }
//                }
//
//                LOGGER.info("要执行的sql" + sql);
//                tEnv.executeSql(sql);
//            } else {
//                LOGGER.info("非create table和insert into的sql：" + sql);
//                tEnv.executeSql(sql);
//            }
//        }
//
//        supplementSqlLineage(sourceSinkTableRelationSet, tableMetaExtHashSet);
//        return sourceSinkTableRelationSet;
//    }
//
//    private void loadUdxJars(FlinkSqlJobLibraDto flinkSqlJobLibraDto, StreamExecutionEnvironment env,
//                             boolean isLoadUdx) throws MalformedURLException {
//        if (isLoadUdx) {
//            String udfJars = flinkSqlJobLibraDto.getUdfJars();
//            if (StringUtils.isNotBlank(udfJars)) {
//                ClassLoader parentClassLoader = env.getUserClassloader();
//                Type type = new TypeToken<List<UdfJarsBean>>() {
//                }.getType();
//                List<UdfJarsBean> udfJarsBeanList = new Gson().fromJson(udfJars, type);
//                if (udfJarsBeanList != null && udfJarsBeanList.size() > 0) {
//                    List<URL> urlList = new ArrayList<>();
//                    for (int i = 0; i < udfJarsBeanList.size(); i++) {
//                        UdfJarsBean udfJarsBean = udfJarsBeanList.get(i);
//                        String jarPath = udfJarsBean.getJarPath();
//                        String filePath = PRE_DIR + jarPath;
//                        if (new File(filePath).exists()) {
//                            //存在说明被加载过，不在加载，重新部署，文件一开始也是不存在的
//                            LOGGER.info("文件存在" + jarPath);
//                        } else {
//                            LOGGER.info("文件不存在,去下载文件" + jarPath);
//                            OssUtils.downloadUdxJar(jarPath, arkRunner.getAccessKey(), arkRunner.getAccessKeySecret());
//                        }
//                        URL jarURL = new File(filePath).toURI().toURL();
//                        urlList.add(jarURL);
//                    }
//
//
//                    if (urlList.size() > 0) {
//                        URL[] urls = new URL[urlList.size()];
//                        for (int i = 0; i < urls.length; i++) {
//                            urls[i] = urlList.get(i);
//                        }
//
//                        ClassLoader userCodeClassLoader = FlinkUserCodeClassLoaders.parentFirst(
//                                urls,
//                                parentClassLoader,
//                                t -> {
//                                },
//                                true
//                        );
//                        Thread.currentThread().setContextClassLoader(userCodeClassLoader);
//                    }
//                }
//            }
//        }
//    }
//
//
//    private void splitInsertSourceTables(MultiSQLWrapper
//                                                 multiSQLWrapper, Set<String> insertSqlSet, Set<TableMetaExt> sourceTableMetaSet, Set<TableMetaExt> insertTableMetas) {
//        Map<String, TableMeta> ddlCalls = multiSQLWrapper.getDdlCalls();
//        for (String viewTable : ddlCalls.keySet()) {
//            TableMeta value = ddlCalls.get(viewTable);
//            TableMetaExt tableMetaExt = new TableMetaExt();
//            BeanUtils.copyProperties(value, tableMetaExt);
//            tableMetaExt.setViewTable(viewTable);
//            tableMetaExt.setConnectorUnify(value.getConnector());
//
//            if (insertSqlSet.contains(viewTable)) {
//                insertTableMetas.add(tableMetaExt);
//            } else {
//                sourceTableMetaSet.add(tableMetaExt);
//            }
//        }
//    }
//
//
//    private Map<String, String> mapToLowerCase(Map<String, String> options) {
//        Map<String, String> newMap = new HashMap<>();
//        for (Map.Entry<String, String> entry : options.entrySet()) {
//            String key = entry.getKey().toLowerCase();
//            String value = entry.getValue().toLowerCase(); // 将字符串转换成小写字符串
//            newMap.put(key, value);
//        }
//        return newMap;
//    }
//
//    private String replaceConnector(Set<String> sourceTableSet, Set<String> sinkTableSet, String sql, String
//            viewTableName) {
//        sql = splitWith(sql);
//        if (sourceTableSet.contains(viewTableName)) {
//            sql = sql + "WITH ('connector' = 'datagen')";
//            //移除METADATA VIRTUAL,因为datagen不支持METADATA VIRTUAL
//            sql = sql.replaceAll("(?i) METADATA", "")
//                    .replaceAll("(?i) VIRTUAL", "")
////                                .replaceAll("(?i) from", "")
//            ;
//        } else if (sinkTableSet.contains(viewTableName)) {
//            sql = sql + "WITH ('connector' = 'print')";
//        }
//
//        //识别出source还是sink表，进行替换
//        LOGGER.info("正则后结果" + sql);
//        return sql;
//    }
//
//    /**
//     * 功能描述
//     *
//     * @param
//     * @return
//     * <AUTHOR>
//     * @date 2023/8/1
//     */
//    private Set<String> tableMetaExtToStringExtractedViewTable(Set<TableMetaExt> sourceTableMetaSet) {
//        Set<String> sourceTableSet = new HashSet<>();
//        Iterator<TableMetaExt> iterator = sourceTableMetaSet.iterator();
//        while (iterator.hasNext()) {
//            String viewTable = iterator.next().getViewTable();
//            sourceTableSet.add(viewTable.toLowerCase());
//        }
//        return sourceTableSet;
//    }
//
//
//    private void supplementSqlLineage
//            (Set<FlinkSqlLineage> sourceSinkTableRelationSet, Set<TableMetaExt> tableMetaExtHashSet) {
//        Iterator<FlinkSqlLineage> flinkSqlLineageIterator = sourceSinkTableRelationSet.iterator();
//        while (flinkSqlLineageIterator.hasNext()) {
//            FlinkSqlLineage element = flinkSqlLineageIterator.next();
//            String sourceViewTable = element.getSourceViewTable();
//            String targetViewTable = element.getTargetViewTable();
//            Iterator<TableMetaExt> tableMetaExtIterator = tableMetaExtHashSet.iterator();
//            while (tableMetaExtIterator.hasNext()) {
//                TableMetaExt tableMetaExt = tableMetaExtIterator.next();
//                String viewTable = tableMetaExt.getViewTable();
//                if (viewTable.equals(sourceViewTable)) {
//                    element.setSourceDbType(tableMetaExt.getConnector());
//                    element.setSourceTable(tableMetaExt.getTableName());
//                    element.setSourceDatabase(tableMetaExt.getDataBase());
////                    element.setSourceInstance(tableMetaExt.getHost());
//                    element.setSourceOriginalInstance(tableMetaExt.getHost());
//                }
//                if (viewTable.equals(targetViewTable)) {
//                    element.setTargetDbType(tableMetaExt.getConnector());
//                    element.setTargetDatabase(tableMetaExt.getDataBase());
//                    element.setTargetTable(tableMetaExt.getTableName());
////                    element.setTargetInstance(tableMetaExt.getHost());
//                    element.setTargetOriginalInstance(tableMetaExt.getHost());
//                }
//            }
//        }
//    }
//
//
//    public Set<FlinkSqlLineage> supplementSqlLineagePlanB
//            (Set<TableMetaExt> sourceTableMetaSet, Set<TableMetaExt> insertTableMetas) {
//        Set<FlinkSqlLineage> list = new HashSet<>();
//        for (TableMetaExt sourceTableMeta : sourceTableMetaSet) {
//            //跟insert表添加上游表信息
//            for (TableMetaExt insertTableMeta : insertTableMetas) {
//                FlinkSqlLineage a = new FlinkSqlLineage();
//                a.setSourceDbType(sourceTableMeta.getConnector());
//                a.setSourceTable(sourceTableMeta.getTableName());
//                a.setSourceViewTable(sourceTableMeta.getViewTable());
//                a.setSourceDatabase(sourceTableMeta.getDataBase());
////              a.setSourceInstance(sourceTableMeta.getHost());
//                a.setSourceOriginalInstance(sourceTableMeta.getHost());
//
//                a.setTargetDbType(insertTableMeta.getConnector());
//                a.setTargetTable(insertTableMeta.getTableName());
//                a.setTargetViewTable(insertTableMeta.getViewTable());
//                a.setTargetDatabase(insertTableMeta.getDataBase());
////              a.setTargetInstance(insertTableMeta.getHost());
//                a.setTargetOriginalInstance(insertTableMeta.getHost());
//                list.add(a);
//            }
//        }
//        return list;
//    }
//
//
//    private Set<FlinkSqlLineage> identifySourceSinkTableRelation(
//            TableEnvironmentImpl tEnv,
//            CatalogSinkModifyOperation operation) {
//
//        CatalogSinkModifyOperation sinkOperation = operation;
//
//        PlannerQueryOperation queryOperation = (PlannerQueryOperation) sinkOperation.getChild();
//
//        RelNode relNode = queryOperation.getCalciteTree();
//        Tuple2<String, RelNode> stringRelNodeTuple2 = new Tuple2<>(
//                sinkOperation.getTableIdentifier().asSummaryString(),
//                relNode);
//        String sinkTable = stringRelNodeTuple2.getField(0);
//        RelNode oriRelNode = stringRelNodeTuple2.getField(1);
//
//        // 2. Build lineage based from RelMetadataQuery
//        List<LineageResult> lineageResults = buildFiledLineageResult(tEnv, sinkTable, oriRelNode);
//        Set<FlinkSqlLineage> set = new HashSet<>();
//        for (int i = 0; i < lineageResults.size(); i++) {
//            LineageResult lineageResult = lineageResults.get(i);
////            String sourceCatalog = lineageResult.getSourceCatalog();
////            String sourceDatabase = lineageResult.getSourceDatabase();
//            String sourceTable = lineageResult.getSourceTable();
////            String targetCatalog = lineageResult.getTargetCatalog();
////            String targetDatabase = lineageResult.getTargetDatabase();
//            String targetTable = lineageResult.getTargetTable();
//            set.add(new FlinkSqlLineage(
//                    sourceTable,
//                    targetTable));
//        }
//        return set;
//    }
//
//
//    private List<LineageResult> buildFiledLineageResult(
//            TableEnvironmentImpl tEnv,
//            String sinkTable,
//            RelNode optRelNode) {
//        String DELIMITER = ".";
//        // target columns
//        List<String> targetColumnList = tEnv.from(sinkTable)
//                .getResolvedSchema()
//                .getColumnNames();
//
//        // check the size of query and sink fields match
////        validateSchema(sinkTable, optRelNode, targetColumnList);
//
//        RelMetadataQuery metadataQuery = optRelNode.getCluster().getMetadataQuery();
//        List<LineageResult> resultList = new ArrayList<>();
//
//        for (int index = 0; index < targetColumnList.size(); index++) {
//            String targetColumn = targetColumnList.get(index);
//            Set<RelColumnOrigin> relColumnOriginSet = metadataQuery.getColumnOrigins(
//                    optRelNode,
//                    index);
////            Set<ImmutableBitSet> uniqueKeys = metadataQuery.getUniqueKeys(optRelNode);
////            RelOptTable tableOrigin = metadataQuery.getTableOrigin(optRelNode);
////            Set<RexTableInputRef.RelTableRef> tableReferences = metadataQuery.getTableReferences(optRelNode);
//            if (CollectionUtils.isNotEmpty(relColumnOriginSet)) {
//                for (RelColumnOrigin rco : relColumnOriginSet) {
//                    // table
//                    RelOptTable table = rco.getOriginTable();
//                    String sourceTable = String.join(DELIMITER, table.getQualifiedName());
////                    List<String> qualifiedName = ((TableSourceTable) table).getQualifiedName();
////                    List<String> fieldNames = ((TableSourceTable) table)
////                            .getRowType()
////                            .getFieldNames();
////                            .catalogTable().getResolvedSchema().getColumnNames();
//                    // filed
////                    int ordinal = rco.getOriginColumnOrdinal();
////                    List<String> fieldNames =
////                            ((TableSourceTable) table).catalogTable().getResolvedSchema().getColumnNames();
////                    String sourceColumn = fieldNames.get(ordinal);
//////                    if (StringUtils.isNotEmpty(rco.getTransform())) {
//////                        LOG.debug("transform: {}", rco.getTransform());
//////                    }
////                    // add record
//                    resultList.add(
//                            new LineageResult(
//                                    sourceTable,
//                                    "sourceColumn",
//                                    sinkTable,
//                                    targetColumn,
//                                    rco.getOriginTable().getRowType().toString()));
//                }
//            }
//        }
//        return resultList;
//    }
//
//    public String splitWith(String sql) {
//        sql = sql.replaceAll("(?i)\\n\\s*", " ");
//        int indexOf0 = sql.toLowerCase().indexOf(" with ");
//        int indexOf1 = sql.toLowerCase().indexOf(" with(");
//        int indexOf2 = sql.toLowerCase().indexOf(")with ");
//        int indexOf3 = sql.toLowerCase().indexOf(")with ");
//        int minIndex = findMinIndex(indexOf0, indexOf1, indexOf2, indexOf3);
//        String substring = sql.substring(0, minIndex + 1);
//        return substring;
//    }
//
//
//    public int findMinIndex(int... indexes) {
//        int minIndex = -1;
//        for (int i = 0; i < indexes.length; i++) {
//            if (indexes[i] >= 0 && (minIndex == -1 || indexes[i] < minIndex)) {
//                minIndex = indexes[i];
//            }
//        }
//        return minIndex;
//    }
//
//
//    public void getScriptSourceSink(String
//                                            sqlContent, Set<TableMetaExt> sourceTableMetaSet, Set<TableMetaExt> insertTableMetas) throws IOException {
//        MultiSQLWrapper multiSQLWrapper = SqlScriptsRender.translateToSqlCallsV1(sqlContent);
//
//        //拿到insert into语句，识别insert表
//        Set<String> insertSqlSet = multiSQLWrapper.getInsertCalls().keySet();
//        //分类source，insert表，同时对connector进行归一，比如upsert-kafka,du-kafka,kafka归一为Kafka
//        splitInsertSourceTables(multiSQLWrapper, insertSqlSet, sourceTableMetaSet, insertTableMetas);
//    }
//
//
//    public void updateSqlLineStrategy(List<SqlInstanceMapDto> sqlInstanceMapDtos,
//                                      FlinkSqlJobLibraDto flinkSqlJobLibra,
//                                      Set<FlinkSqlLineage> tableLineageResults,
//                                      String parserStatus) throws ParserException {
//        if (tableLineageResults.size() > 0) {
//            //某个作业的血缘数据更新策略，待设计
//            flinkSqlLineageService.updateSqlLineStrategy(sqlInstanceMapDtos, flinkSqlJobLibra, tableLineageResults);
//            flinkSqlJobLibra.setParserStatus(parserStatus);
//        } else {
//            throw new ParserException("血缘解析source,sink表数量为0");
//        }
//    }
//
//
//}
