# 本地开发环境配置 - 简化版，用于快速启动
spring:
  # 禁用 Nacos 配置中心
  cloud:
    nacos:
      config:
        enabled: false
      discovery:
        enabled: false
  
  # 数据源配置 - 使用内存数据库H2进行快速测试
  datasource:
    druid:
      db-type: h2
      url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
      username: sa
      password: 
      driver-class-name: org.h2.Driver
      # 连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 30000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"

  # Redis配置 - 使用嵌入式Redis或禁用
  redis:
    host: 127.0.0.1
    port: 6379
    password: 
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 300
        max-idle: 100
        max-wait: -1ms
        min-idle: 20
      shutdown-timeout: 10000ms

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop  # 自动创建表结构
    show-sql: true
    database-platform: org.hibernate.dialect.H2Dialect

  # H2数据库控制台
  h2:
    console:
      enabled: true
      path: /h2-console

# 日志配置
logging:
  level:
    com.dewu: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 禁用一些不必要的功能
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# Flink相关配置 - 简化版
flink:
  # 禁用或简化Flink相关配置
  enabled: false

# JWT配置
jwt:
  header: Authorization
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少88位的Base64对该令牌进行编码
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/毫秒 ，默认4小时
  token-validity-in-seconds: 14400000
  # 在线用户key
  online-key: online-token-
  # 验证码
  code-key: code-key-
  # token 续期检查时间范围（默认30分钟，单位毫秒）
  detect: 1800000
  # 续期时间范围，默认1小时，单位毫秒
  renew: 3600000

# 是否允许生成代码，本地环境设置为true
generator:
  enabled: true

# 是否开启 swagger-ui，本地环境开启
swagger:
  enabled: true

# 登录相关配置
login:
  # 是否限制单用户登录
  single-login: false
  # Redis用户登录缓存配置
  user-cache:
    # 存活时间/秒
    idle-time: 7200
  # 验证码
  login-code:
    # 验证码类型配置
    code-type: arithmetic
    # 登录图形验证码有效时间/分钟
    expiration: 2
    # 验证码高度
    width: 111
    # 验证码宽度
    height: 36
    # 内容长度
    length: 2
    # 字体名称，为空则使用默认字体
    font-name:
    # 字体大小
    font-size: 25

# 本地环境DW配置 - 简化版
dw:
  realtime-platform:
    appId: realtime_property_platform_local
    appSecret: local_secret_key
    baseUrl: http://localhost:8080/libra/web
    webUrl: http://localhost:8080/
  feishu-bot:
    appId: local_app_id
    appSecret: local_app_secret
    baseUrl: http://localhost:8080/open-apis
    env: local
  poizon-metrics:
    accessId: local_access
    accessKey: local_access_key
    url: http://localhost:8080/
  kafka-manager:
    baseUrl: http://localhost:8080
    token: local_token
    instanceId: local_instance
  odps-datasource-id: 1

# 本地数据库配置
db:
  url: jdbc:h2:mem:testdb
  user: sa
  password:

# 本地请求配置
request:
  addmodel: http://localhost:8080/api/v1/h5/olap/metadata/addModel
  updatemodel: http://localhost:8080/api/v1/h5/olap/metadata/updateModel
  searchmodel: http://localhost:8080/api/v1/h5/olap/metadata/searchModels?engineType=CLICKHOUSE&&name=
  startmodel: http://localhost:8080/api/v1/h5/olap/metadata/updateModelStatus
  addmetrics: http://localhost:8080/api/v1/h5/compass/indicators/add
  searchmetrics: http://localhost:8080/api/v1/h5/compass/indicators/list
  startmetrics: http://localhost:8080/api/v1/h5/compass/indicators/status/update
  database: local_db
  datasource_name: local-datasource
  add16: http://localhost:9000/api/quartz/service/add/16
  update16: http://localhost:9000/api/quartz/service/update/16
  updatestate16: http://localhost:9000/api/quartz/service/update/16/state
  exec16: http://localhost:9000/api/quartz/service/exec/16
  delete16: http://localhost:9000/api/quartz/service/delete/16
  add13: http://localhost:9000/api/quartz/service/add/13
  update13: http://localhost:9000/api/quartz/service/update/13
  updatestate13: http://localhost:9000/api/quartz/service/update/13/state
  exec13: http://localhost:9000/api/quartz/service/exec/13
  delete13: http://localhost:9000/api/quartz/service/delete/13

# 本地监控配置
monitorReceiver: 本地测试告警群
monitorReceiverOffline: 本地测试告警群

# 本地Kafka配置
kafka:
  ip: localhost:9092
  job_status_sync_topic: local_job_status_sync
  job_status_monitor_topic: local_job_status_monitor
  alarm:
    ip: localhost:9092
    topic: local_alarm_topic

# 本地Libra配置
libra:
  binlog:
    task_info:
      ip: localhost:9092
      topic: local_libra_task_info
      group_id: local_rpp_libra_task_info
      time_out: 30000

# 本地Chaos配置
chaos:
  send:
    kafka:
      bootstrap_server: localhost:9092
      topic: local_chaos_data_bus

# 本地Realtime配置
realtime:
  data_wharehouse:
    cost:
      bootstrap_server: localhost:9092
      topic: local_realtime_data_wharehouse_cost
      metric_topic: local_dewu_datawarehouse_metrics

# 本地Pipeline metrics配置
pipeline:
  metrics:
    sr:
      url: jdbc:h2:mem:testdb
      username: sa
      password:
      database: testdb
      table: metric_table
    hbase:
      zookeeperQuorum: localhost:2181
      username: root
      password: root
      table: DIM:rt_lineage_job_info
    kafka:
      bootstrap_server: localhost:9092
      topic: lineage_metrics_topic
