# 本地开发环境配置 - 简化版，用于快速启动
spring:
  # 禁用 Nacos 配置中心
  cloud:
    nacos:
      config:
        enabled: false
      discovery:
        enabled: false
  
  # 数据源配置 - 使用内存数据库H2进行快速测试
  datasource:
    druid:
      db-type: h2
      url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
      username: sa
      password: 
      driver-class-name: org.h2.Driver
      # 连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 30000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"

  # Redis配置 - 使用嵌入式Redis或禁用
  redis:
    host: 127.0.0.1
    port: 6379
    password: 
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 300
        max-idle: 100
        max-wait: -1ms
        min-idle: 20
      shutdown-timeout: 10000ms

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop  # 自动创建表结构
    show-sql: true
    database-platform: org.hibernate.dialect.H2Dialect

  # H2数据库控制台
  h2:
    console:
      enabled: true
      path: /h2-console

# 日志配置
logging:
  level:
    com.dewu: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 禁用一些不必要的功能
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# Flink相关配置 - 简化版
flink:
  # 禁用或简化Flink相关配置
  enabled: false
