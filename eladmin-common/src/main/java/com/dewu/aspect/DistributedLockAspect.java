package com.dewu.aspect;

import com.dewu.annotation.DistributedLock;
import com.dewu.utils.RedisDistributedLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
public class DistributedLockAspect {

    @Autowired
    private RedisDistributedLock redisDistributedLock;

    @Around("@annotation(com.dewu.annotation.DistributedLock)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        String lockKey = getLockKey(joinPoint);
        log.info("分布式锁key:{}", lockKey);

        for (int i = 0; i < 10; i++) {
            if (redisDistributedLock.lock(lockKey)) {
                try {
                    log.info("获取到分布式锁key:{}", lockKey);
                    return joinPoint.proceed();
                } finally {
//                log.debug("释放分布式锁key:{}",lockKey);
//                redisDistributedLock.unlock(lockKey);
                }
            }
            Thread.sleep(1000);
            log.info("没有获取到分布式锁key:{} 进行重试:{}", lockKey, i);
        }
        log.info("没有获取到分布式锁key:{} 不再重试", lockKey);
        return null;
    }

    private String getLockKey(ProceedingJoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        DistributedLock distributedLock = methodSignature.getMethod().getAnnotation(DistributedLock.class);
        String lockKey = distributedLock.value();
        if (StringUtils.isEmpty(lockKey)) {
            // 默认使用类名 + 方法名作为锁的key
            lockKey = joinPoint.getTarget().getClass().getSimpleName() + ":" + methodSignature.getName();
        }
        return lockKey;
    }
}
