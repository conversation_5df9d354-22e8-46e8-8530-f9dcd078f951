package com.dewu.utils.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Author: WangLin
 * Date: 2022/7/24 下午2:09
 * Description: 得物自建实时平台OpenApi路径信息枚举
 */
@Getter
@AllArgsConstructor
public enum RealtimePlatformOpenApiEnum {

	CREATE("/openApi/task/addTask"),

	UPDATE("/openApi/task/updateTask"),

	SUBMIT("/openApi/task/submitTask"),

	KILL("/openApi/task/killTaskEnhance"),

	DELETE("/openApi/task/delete"),

	QUERY("/openApi/task/queryTasksByParam"),

	MONITOR_LIST("/openApi/monitor2/list"),

	MONITOR_UPDATE("/openApi/monitor2/update"),

	CHECKPOINT_LIST_OSS("/openApi/task/queryCheckpointList/oss/"),
	CHECKPOINT_LIST_HDFS("/openApi/task/queryCheckpointList/hdfs/"),

	QUERY_LIST_PAGE("/openApi/task/queryListPage");

	private final String url;

	public static RealtimePlatformOpenApiEnum find(String url) {
		for (RealtimePlatformOpenApiEnum value : RealtimePlatformOpenApiEnum.values()) {
			if (value.getUrl().equals(url)) {
				return value;
			}
		}
		return null;
	}
}
