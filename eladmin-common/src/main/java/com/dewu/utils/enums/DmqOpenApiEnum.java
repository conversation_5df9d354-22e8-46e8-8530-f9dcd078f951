package com.dewu.utils.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Author: WangLin
 * Date: 2023/7/24 上午11:26
 * Description: 得物DMQ平台OpenApi路径信息枚举
 */
@Getter
@AllArgsConstructor
public enum DmqOpenApiEnum {

	QUERY_TOPIC("/api/v2/kafka-console/open/topic/info"),

	CREATE_TOPIC("/api/v2/kafka-console/open/topic/create");

	private final String url;

	public static DmqOpenApiEnum find(String url) {
		for (DmqOpenApiEnum value : DmqOpenApiEnum.values()) {
			if (value.getUrl().equals(url)) {
				return value;
			}
		}
		return null;
	}
}
