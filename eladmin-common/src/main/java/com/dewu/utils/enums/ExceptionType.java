package com.dewu.utils.enums;

/**
 * @description: /
 * @Author: lmh
 * @date: 2022/7/29 11:32 上午
 */
public enum ExceptionType  {
    QUERY("query error","查询错误"),
    CREATE("create error","创建错误"),
    UPDATE("update error","更新错误"),
    DELETE("delete error","删除错误"),
    ERR_DELETE_STATUS("delete status error","删除任务状态错误"),
    DEPLOY("deploy error","一键部署错误"),
    ERR_DEPLOY_STATUS("deploy status error","部署任务当前状态错误"),
    CREATE_TASK("create flink error","创建Flink错误"),
    EXECUTE_TASK("execute flink error","执行Flink错误"),
    RELEASE("release job error","job发布错误"),
    STOP_TASK("stop job error","停止错误"),
    ERR_STOP_STATUS("stop status error","停止任务状态错误"),
    VALIDATOR("validator job error","校验错误"),
    CODE_GENERATOR("code_generator job error","generator job错误"),
    APPROVAL("error","审批错误");

    private final String name;
    private final String  describe;

    ExceptionType(String name,String describe) {
        this.name=name;
        this.describe=describe;
    }

    public Integer getCode() {
        throw new UnsupportedOperationException();
    }

    public String getName() {
        return this.name;
    }

    public String getDescribe() {
        return this.describe;
    }

    public String toString() {
        return name;
    }

}
