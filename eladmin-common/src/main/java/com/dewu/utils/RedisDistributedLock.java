package com.dewu.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedisDistributedLock {

    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final String LOCK_PREFIX = "lock:";
    private static final long LOCK_EXPIRE = 3000L; // 锁的过期时间，单位毫秒

    public boolean lock(String lockKey) {
        String key = LOCK_PREFIX + lockKey;
        long currentTime = System.currentTimeMillis();

        if (redisTemplate.opsForValue().setIfAbsent(key, String.valueOf(currentTime))) {
//            log.info("更新分布式锁key的过期时间:{}", lockKey);
            redisTemplate.expire(key, LOCK_EXPIRE, TimeUnit.MILLISECONDS);
            Thread thread = new Thread(() -> {
                while(true) {
                    try {
                        LocalDateTime localDateTime = LocalDateTime.now(); // 获取当前时间
                        int second = localDateTime.getSecond(); // 获取秒数
                        if((second>=10 && second<=15)||(second>=40 && second<=45)) {
//                            log.info("更新分布式锁key的过期时间:{}", lockKey);
                        }
                        redisTemplate.expire(key, LOCK_EXPIRE, TimeUnit.MILLISECONDS);
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });
            thread.setDaemon(true); // 将线程设置为守护线程，这样当应用程序关闭时，后台线程会自动停止
            thread.start(); // 启动后台线程
            return true;
        }
        return false;

    }

    public void expire(String lockKey) {
//        log.info("更新分布式锁key的过期时间:{}", lockKey);
        String key = LOCK_PREFIX + lockKey;
        redisTemplate.expire(key, LOCK_EXPIRE, TimeUnit.MILLISECONDS);
    }

    public void unlock(String lockKey) {
        String key = LOCK_PREFIX + lockKey;
        redisTemplate.delete(key);
    }

}