package com.dewu.exception;

import com.dewu.utils.StringUtils;
import com.dewu.utils.enums.ExceptionType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description job行为错误
 * @date 2022-07-21
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class JobException extends RuntimeException {

	private String msg;
	private ExceptionType exceptionType;

	public JobException(ExceptionType exceptionType, String jobName, String errorMess) {
		super(JobException.generateMessage(exceptionType.getName(), jobName, errorMess));
		setErrorMess(exceptionType, errorMess);
	}

	public JobException(ExceptionType exceptionType, String jobName, String errorMess, Throwable e) {
		super(JobException.generateMessage(exceptionType.getName(), jobName, errorMess), e);
		setErrorMess(exceptionType, errorMess);
	}

	public JobException(ExceptionType exceptionType, Long jobId, String errorMess) {
		super(JobException.generateMessage(exceptionType.getName(), jobId, errorMess));
		setErrorMess(exceptionType, errorMess);
	}

	private static String generateMessage(String header, String jobName, String val) {
		return StringUtils.join(header, "！jobName:", jobName, ";msg: ", val);
	}

	private static String generateMessage(String header, Long jodId, String val) {
		return StringUtils.join(header, "！jobId:", jodId, ";mes: ", val);
	}

	private void setErrorMess(ExceptionType exceptionType, String errorMess) {
		this.exceptionType = exceptionType;
		if (!StringUtils.isEmpty(errorMess)) {
			msg = StringUtils.join(exceptionType.getName(), ": ", errorMess);
		} else
			msg = StringUtils.join(exceptionType.getName(), ": 未知原因");
	}
}
