package com.dewu.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;

import static org.springframework.http.HttpStatus.BAD_REQUEST;

@Getter
public class ParserException extends RuntimeException{

    private Integer status = BAD_REQUEST.value();

    public ParserException(String msg){
        super(msg);
    }

    public ParserException(HttpStatus status, String msg){
        super(msg);
        this.status = status.value();
    }
}

