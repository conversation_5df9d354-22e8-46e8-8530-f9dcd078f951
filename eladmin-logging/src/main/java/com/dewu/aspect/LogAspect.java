/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dewu.aspect;

import com.alibaba.fastjson.JSONObject;
import com.dewu.domain.Log;
import com.dewu.service.LogService;
import com.dewu.utils.RequestHolder;
import com.dewu.utils.SecurityUtils;
import com.dewu.utils.StringUtils;
import com.dewu.utils.ThrowableUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Enumeration;
import java.util.UUID;

/**
 * <AUTHOR> Jie
 * @date 2018-11-24
 */
@Component
@Aspect
@Slf4j
public class LogAspect {

    private final LogService logService;

    ThreadLocal<Long> currentTime = new ThreadLocal<>();

    public LogAspect(LogService logService) {
        this.logService = logService;
    }

    /**
     * 配置切入点
     */
    @Pointcut("@annotation(com.dewu.annotation.Log)")
    public void logPointcut() {
        // 该方法无方法体,主要为了让同类中其他方法使用此切入点
    }

    /**
     * 配置环绕通知,使用在方法logPointcut()上注册的切入点
     *
     * @param joinPoint join point for advice
     */
    @Around("logPointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result;
        currentTime.set(System.currentTimeMillis());
        HttpServletRequest request = RequestHolder.getHttpServletRequest();

        Enumeration<String> headerNames = request.getHeaderNames();
        //如果有上层调用就用上层的ID
        String traceId = request.getHeader("traceId");
        String email = request.getHeader("email");
        log.warn("请求head中trace_id:{}", traceId);
        log.warn("请求head中email:{}", email);
        log.warn("请求head:{}", JSONObject.toJSON(headerNames));
        if (traceId == null) {
            traceId = UUID.randomUUID().toString();
        }
        MDC.put("traceId", traceId);

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String methodName = joinPoint.getTarget().getClass().getName() + "." + signature.getName() + "()";
        String parameter = logService.getParameter(method, joinPoint.getArgs());
        log.info("请求方法:{},请求参数:{}", methodName, parameter);

        result = joinPoint.proceed();
        Log logBean = new Log("INFO", System.currentTimeMillis() - currentTime.get());
        currentTime.remove();

        try {
            log.info("请求方法:{},返回参数:{}", methodName, JSONObject.toJSON(result));
        }catch (Exception e) {
            log.info("请求方法:{},返回参数:{}", methodName, result);
        }
        if(org.apache.commons.lang3.StringUtils.isBlank(email)) {
            logService.save(getUsername(), StringUtils.getBrowser(request), StringUtils.getIp(request), joinPoint, logBean);
        }else {
            logService.save(email, StringUtils.getBrowser(request), StringUtils.getIp(request), joinPoint, logBean);
        }

        return result;
    }

    /**
     * 配置异常通知
     *
     * @param joinPoint join point for advice
     * @param e         exception
     */
    @AfterThrowing(pointcut = "logPointcut()", throwing = "e")
    public void logAfterThrowing(JoinPoint joinPoint, Throwable e) {
        Log logBean = new Log("ERROR", System.currentTimeMillis() - currentTime.get());
        currentTime.remove();
        logBean.setExceptionDetail(ThrowableUtil.getStackTrace(e).substring(0, 3999).getBytes());
        HttpServletRequest request = RequestHolder.getHttpServletRequest();

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String methodName = joinPoint.getTarget().getClass().getName() + "." + signature.getName() + "()";
        log.info("请求方法:{},返回异常信息:{}", methodName, new String(logBean.getExceptionDetail()));

        logService.save(getUsername(), StringUtils.getBrowser(request), StringUtils.getIp(request), (ProceedingJoinPoint) joinPoint, logBean);
    }

    public String getUsername() {
        try {
            return SecurityUtils.getCurrentUsername();
        } catch (Exception e) {
            return "";
        }
    }
}
