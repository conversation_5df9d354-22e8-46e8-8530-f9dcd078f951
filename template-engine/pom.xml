<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>flink-template-engine</artifactId>
        <groupId>com.dewu</groupId>
        <version>2.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>template-engine</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <flink.version>du-1.13-SNAPSHOT</flink.version>
        <scala.binary.version>2.11</scala.binary.version>
        <mail.version>1.4.7</mail.version>
        <qiniu.version>7.9.3</qiniu.version>
        <alipay.version>4.22.57.ALL</alipay.version>
        <poizon-metrics.version>1.0.8.RELEASE</poizon-metrics.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.20</version>
        </dependency>

        <!--         低于 1.14 -->

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-planner-blink_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-api-java-bridge_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-planner_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-json</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-clients_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-jdbc_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-kafka_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>blink-sql-connector-kafka</artifactId>
            <version>${flink.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-sql-client_2.11</artifactId>
            <version>${flink.version}</version>
            <!--            <scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.25</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>blink-connector-common</artifactId>
            <version>du-1.13-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>blink-sql-connector-rds</artifactId>
            <version>du-1.13-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>blink-sql-connector-odps</artifactId>
            <version>du-1.13-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>blink-sql-connector-rds</artifactId>
            <version>du-1.13-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>blink-sql-connector-adb-3.0</artifactId>
            <version>du-1.13-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>blink-sql-connector-elasticsearch6</artifactId>
            <version>du-1.13-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>blink-sql-connector-clickhouse</artifactId>
            <version>du-1.13-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>blink-sql-connector-cloudhbase</artifactId>
            <version>du-1.13-SNAPSHOT</version>
        </dependency>
        <!--        du-starrocks-->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>blink-sql-connector-starrocks-v2</artifactId>
            <version>du-1.13-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>blink-sql-connector-rocketmq</artifactId>
            <version>du-1.13-SNAPSHOT</version>
        </dependency>


        <!--        <dependency>-->
        <!--            <groupId>org.apache.flink</groupId>-->
        <!--            <artifactId>blink-sql-connector-sls</artifactId>-->
        <!--            <version>du-1.16-SNAPSHOT</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.apache.commons</groupId>-->
        <!--                    <artifactId>commons-lang3</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.dewu</groupId>
            <artifactId>eladmin-common</artifactId>
            <version>2.6</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.dewu</groupId>
            <artifactId>eladmin-tools</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>QLExpress</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.2.5</version>
        </dependency>
        <dependency>
            <groupId>org.mvel</groupId>
            <artifactId>mvel2</artifactId>
            <version>2.4.8.Final</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <version>1.13.5-SNAPSHOT</version>
            <artifactId>flink-connector-dynamic-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-test-utils_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-runtime-web_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shizhuang</groupId>
            <artifactId>common-udf</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-core-asl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jayway.jsonpath</groupId>
                    <artifactId>json-path</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-mapper-asl</artifactId>
                </exclusion>
                <!-- 排除ARK配置客户端依赖 -->
                <exclusion>
                    <groupId>com.shizhuang</groupId>
                    <artifactId>ark-config-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-sql-user-function</artifactId>
            <version>du-1.13-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-core-asl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jayway.jsonpath</groupId>
                    <artifactId>json-path</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jackson</groupId>
                    <artifactId>jackson-mapper-asl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.poizon.metrics</groupId>
            <artifactId>poizon-metrics-client</artifactId>
            <version>${poizon-metrics.version}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.shizhuang</groupId>-->
<!--            <artifactId>ark-config-client</artifactId>-->
<!--            <version>1.2.6.RELEASE</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.alibaba.nacos</groupId>-->
<!--            <artifactId>nacos-client</artifactId>-->
<!--            <version>dewu-1.4.4</version>-->
<!--        </dependency>-->


        <dependency>
            <groupId>com.poizon</groupId>
            <artifactId>realtime_aksk_config_util</artifactId>
            <version>2.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <!-- 排除ARK配置客户端依赖 -->
                <exclusion>
                    <groupId>com.shizhuang</groupId>
                    <artifactId>ark-config-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dewu.bigdata</groupId>
            <artifactId>galaxy-sdk</artifactId>
            <version>1.1.7</version>
        </dependency>

    </dependencies>
    <repositories>
        <repository>
            <id>poizon-repos</id>
            <url>http://nexus.poizon.com/nexus/content/groups/public/</url>
        </repository>
    </repositories>
    <build>
    </build>
</project>
